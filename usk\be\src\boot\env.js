const Env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  URL_SITE: process.env.URL_SITE || 'localhost',
  R_DATABASE_URL: process.env.R_DATABASE_URL,
  W_DATABASE_URL: process.env.W_DATABASE_URL,
  JWT_SECRET: process.env.JWT_SECRET,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN,
  USER_AGENT_NOT_LOG: process.env.USER_AGENT_NOT_LOG || '',
  X_API_KEY: process.env.X_API_KEY,
  ALLOW_ORIGIN: process.env.ALLOW_ORIGIN || '*',

  // In all handlers `this` is the fastify instance
  COOKIE: {
    domain: process.env.DOMAIN || 'localhost',
    path: '/',
    secure: true, // or false
    httpOnly: true, // or false
    sameSite: process.env.SAMESITE || 'Strict', // None || Lax || Strict
    maxAge: +process.env.MAXAGE || 180000000,
  },
};

module.exports = Env;