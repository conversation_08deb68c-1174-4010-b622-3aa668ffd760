// ======== IMPORT ========================
const {
  getShippingListSchema,
  getShippingDetailSchema,
  cancelShippingSchema,
  editShippingSchema,
  exportShippingListSchema,
  getExportTodaySchema,
  resetExportTodaySchema,
  exportShippingDetailSchema,
} = require('./schemas');
const { auth, apiKeyVerify } = require('../base/authorized');

// ===== 1. Injection ==============
// ===== 2. Router Functions ==============
async function getShippingListHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { query, user } = request;
  const response = await shippingService.getShippingList(user, query);
  return reply.send(response);
}

async function getShippingDetailHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { params, user } = request;
  const { id } = params
  const response = await shippingService.getShippingDetail({ user, id });
  return reply.send(response);
}

async function cancelShippingHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { params, user } = request;
  const { id } = params
  const response = await shippingService.cancelShipping({ user, id });
  return reply.send(response);
}

async function editShippingHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { params, user, body } = request;
  const { id } = params
  const response = await shippingService.editShipping({ user, id, ...body });
  return reply.send(response);
}

async function exportShippingListHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { query, user } = request;
  const response = await shippingService.exportData({ user, query });
  return reply.send(response);
}

async function exportShippingDetailHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { query, user } = request;
  const response = await shippingService.exportShippingDetail({ user, query });
  return reply.send(response);
}

async function getExportTodayHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { user } = request;
  const response = await shippingService.getExportToday({ user });
  return reply.send(response);
}

async function resetExportTodayHandler(request, reply) {
  const shippingService = request.diScope.resolve('shippingService');
  const { user } = request;
  const response = await shippingService.resetExportToday({ user });
  return reply.send(response);
}
// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get('/get-shipping-list', { schema: getShippingListSchema, onRequest: [apiKeyVerify, auth] }, getShippingListHandler);
  fastify.get('/get-shipping-detail/:id', { schema: getShippingDetailSchema, onRequest: [apiKeyVerify, auth] }, getShippingDetailHandler);
  fastify.delete('/cancel-shipping/:id', { schema: cancelShippingSchema, onRequest: [apiKeyVerify, auth] }, cancelShippingHandler);
  fastify.post('/edit-shipping/:id', { schema: editShippingSchema, onRequest: [apiKeyVerify, auth] }, editShippingHandler);
  fastify.get('/export-shipping-list', { schema: exportShippingListSchema, onRequest: [apiKeyVerify, auth] }, exportShippingListHandler);
  fastify.get('/export-shipping-detail', { schema: exportShippingDetailSchema, onRequest: [apiKeyVerify, auth] }, exportShippingDetailHandler);
  fastify.get('/export-today', { schema: getExportTodaySchema, onRequest: [apiKeyVerify, auth] }, getExportTodayHandler);
  fastify.post('/reset-export-today', { schema: resetExportTodaySchema, onRequest: [apiKeyVerify, auth] }, resetExportTodayHandler);
};
