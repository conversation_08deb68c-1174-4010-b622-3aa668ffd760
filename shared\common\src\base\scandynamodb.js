const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, ScanCommand, QueryCommand, PutCommand, GetCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');

// Set the AWS Region.
const REGION = 'ap-northeast-1'; //e.g. 'us-east-1'
// Create an Amazon DynamoDB service client object.
const ddbClient = new DynamoDBClient({ region: REGION });

const marshallOptions = {
  // Whether to automatically convert empty strings, blobs, and sets to `null`.
  convertEmptyValues: false, // false, by default.
  // Whether to remove undefined values while marshalling.
  removeUndefinedValues: false, // false, by default.
  // Whether to convert typeof object to map attribute.
  convertClassInstanceToMap: false, // false, by default.
};

const unmarshallOptions = {
  // Whether to return numbers as a string instead of converting them to native JavaScript numbers.
  wrapNumbers: false, // false, by default.
};

const translateConfig = { marshallOptions, unmarshallOptions };

// Create the DynamoDB Document client.
const ddbDocClient = DynamoDBDocumentClient.from(ddbClient, translateConfig);

const scanDb = async (param = {}, lastEvaluatedKey = null) => {
  if (lastEvaluatedKey) {
    param.ExclusiveStartKey = lastEvaluatedKey;
  }
  const result = await ddbDocClient.send(new ScanCommand(param));
  const { Items: dataScan } = result;
  if (result.LastEvaluatedKey) {
    const resultRecursion = await scanDb(param, result.LastEvaluatedKey);
    dataScan.push(...resultRecursion);
  }
  return dataScan;
};

const queryDb = async (param = {}, lastEvaluatedKey = null) => {
  if (lastEvaluatedKey) {
    param.ExclusiveStartKey = lastEvaluatedKey;
  }
  const result = await ddbDocClient.send(new QueryCommand(param));
  const { Items: dataQuery } = result;
  if (result.LastEvaluatedKey) {
    const resultRecursion = await queryDb(param, result.LastEvaluatedKey);
    dataQuery.push(...resultRecursion);
  }
  return dataQuery;
};

const putDb = async (param) => {
  await ddbDocClient.send(new PutCommand(param));
}

const updateDb = async (param) => {
  await ddbDocClient.send(new UpdateCommand(param));
}

const getDb = async (param) => {
  const result = await ddbDocClient.send(new GetCommand(param));
  return result;
}

const deleteDb = async (param) => {
  await ddbDocClient.send(new DeleteCommand(param));
}


module.exports = {
  scanDb,
  queryDb,
  putDb,
  updateDb,
  getDb,
  deleteDb,
};