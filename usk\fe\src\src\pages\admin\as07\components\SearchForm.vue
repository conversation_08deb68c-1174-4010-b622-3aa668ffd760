<template>
  <form>
    <div :class="`tw:grid tw:mt-2 tw:grid-cols-2 tw:xl:grid-cols-4 tw:gap-x-5 tw:gap-y-1`">
      <div class="tw:mb-1 tw:col-span-2">
        <span class="tw:text-[1rem] tw:block tw:mb-1"> 作成日 </span>
        <div
          class="tw:flex tw:items-center tw:gap-2 tw:mb-1"
        >
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchFormProvideData.form.value.startDate"
              :error="!!searchFormProvideData.errors.value.startDate"
              :error-message="searchFormProvideData.errors.value.startDate"
              input-class="tw:text-base tw:font-normal tw:tl:text-[1rem] tw:dt:text-[1rem]"
            />
          </div>
          <span class="tw:text-[1rem] tw:font-semibold tw:mb-5">~</span>
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchFormProvideData.form.value.endDate"
              :error="!!searchFormProvideData.errors.value.endDate"
              :error-message="searchFormProvideData.errors.value.endDate"
              input-class="tw:text-base tw:font-normal tw:tl:text-[1rem] tw:dt:text-[1rem]"
            />
          </div>
        </div>
      </div>

      <div class="tw:mb-1 tw:col-span-1">
        <span class="tw:text-[1rem] tw:block tw:mb-1"> 件名</span>
        <q-input
          outlined
          input-class="tw:w-full"
          class="tw:rounded-lg"
          v-model="searchFormProvideData.form.value.title"
          autocomplete="nope"
          maxlength="256"
          :error="!!searchFormProvideData.errors.value.title"
          :error-message="searchFormProvideData.errors.value.title"
          no-error-icon
        />
      </div>

      <div class="tw:mb-1 tw:col-span-1">
        <span class="tw:text-[1rem] tw:block tw:mb-1"> 作成者ユーザーID</span>
        <q-input
          outlined
          v-model="searchFormProvideData.form.value.creator"
          autocomplete="nope"
          lazy-rules
          maxlength="256"
          no-error-icon
          :error="!!searchFormProvideData.errors.value.creator"
          :error-message="searchFormProvideData.errors.value.creator"
        />
      </div>

      <div class="tw:mb-1 tw:col-span-2 tw:xl:col-span-3">
        <span class="tw:text-[1rem] tw:block"> 送付対象ロール </span>
        <div class="tw:flex tw:gap-3 -tw:ml-3">
          <q-checkbox
            class="tw:text-[1rem]"
            v-for="(item, index) in userRoleOptions"
            :key="index"
            v-model="searchFormProvideData.form.value.roles"
            :label="item.label"
            :val="item.value"
            size="lg"
          />
        </div>
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import BaseDatePicker from 'components/base/BaseDatePicker.vue';
import { USER_ROLE_NOTIFY_OPTIONS } from 'src/helpers/constants';
import { inject } from 'vue';
// #endregion

const userRoleOptions = USER_ROLE_NOTIFY_OPTIONS;

// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion

// #region actions

// #endregion
</script>
