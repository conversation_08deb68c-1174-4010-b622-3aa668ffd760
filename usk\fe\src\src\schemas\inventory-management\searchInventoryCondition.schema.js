import MESSAGE from 'helpers/message';

const searchShipmentInventoryCondition = {
  type: 'object',
  additionalProperties: false,
  properties: {
    enterpriseName: {
      type: 'string',
      maxLength: 50,
    },
    name: {
      type: 'string',
      maxLength: 50,
    },
    startDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
    code: {
      type: 'string',
      pattern: '^[0-9]{1,7}$',
      errorMessage: {
        pattern: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
  },
  errorMessage: {
    additionalProperties: 'No additional properties are allowed',
  },
};

export default searchShipmentInventoryCondition;
