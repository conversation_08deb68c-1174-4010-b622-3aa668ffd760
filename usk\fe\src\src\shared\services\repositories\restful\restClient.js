
import Axios from 'axios';
import ToastUtil from 'utilities/toast';
import { useAuthStore } from 'stores/auth-store';
import { useAppStore } from 'stores/app-store';
import { storeToRefs } from 'pinia';
import { useLocalStorage } from 'composables/localstorage';
import MESSAGE from 'helpers/message';

// merge params to axios config
function mergeAxiosConfig(data, config) {
  const axiosConfig = Object.assign(config || {}, {
    params: data,
    // config for query array
    paramsSerializer: {
      indexes: null,
    },
  });
  return axiosConfig;
}

export default class RestClient {
  #client;

  #servicePath;

  constructor(servicePath) {
    this.client = this.createAxiosClient();
    this.servicePath = servicePath;
  }

  createAxiosClient() {
    const axiosInstance = Axios.create({
      baseURL: process.env.API_URL,
      headers: {
        'Content-type': 'application/json',
        'x-api-key': process.env.X_API_KEY,
      },
      withCredentials: false,
    });

    axiosInstance.interceptors.request.use(this.onRequest, this.onRequestError);
    axiosInstance.interceptors.response.use(this.onResponse);

    return axiosInstance;
  }

  onRequest = async config => {
    // await this.extendTokenValidityPeriod();
    const { userToken: token } = useLocalStorage();
    if (config.headers && token.value) {
      // if proxy user token exists, use it to call API
      config.headers.Authorization = `Bearer ${token.value}`;
    }

    // do something here before request
    return config;
  };

  onRequestError(error) {
    return Promise.reject(error);
  }

  onResponse = response => {
    const { userToken: token } = useLocalStorage();
    const newToken = response.headers['x-access-token'];
    if (newToken) {
      token.value = newToken;
    }
    return response;
  };

  async extendTokenValidityPeriod() {
    // extend token here
  }

  request(method, url, data, config) {
    const { setLoading } = useAppStore();
    const { isLoadingManual } = storeToRefs(useAppStore());
    return new Promise((resolve, reject) => {
      // show loading
      if (!isLoadingManual.value) {
        setLoading(true);
      }
      const axiosConfig = Object.assign(config, {
        url: `${this.servicePath}${url}`,
        method,
        data,
      });
      this.client.request(axiosConfig).then(response => {
        const result = response.data;
        if (result.code === 401) {
          const auth = useAuthStore();
          const {
            setRole, setSettingUser, setIsNonActive, role,
          } = useAppStore();
          setSettingUser(null);
          setIsNonActive(role);
          setRole({});
          auth.signOut();
        }

        if (![0, 402, 401].includes(result.code)) {
          if (url.includes('/import-user') || url.includes('login-link')) {
            resolve(result);
          } else {
            ToastUtil.error(result.message);
            reject(result);
          }
        }

        resolve(result);
      })
        .catch(error => {
          ToastUtil.error(error?.response?.data?.message || MESSAGE.SERVER_NOK_ERROR);
          if (Axios.isAxiosError(error)) {
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              if (error.response.data) {
                // error data from server
                const serviceErrorResponse = error.response.data;
                reject(serviceErrorResponse);
              } else {
                reject(error);
              }
            } else if (error.request) {
              // The request was made but no response was received
              // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
              // http.ClientRequest in node.js
              reject(error);
            } else {
              // Something happened in setting up the request that triggered an Error
              reject(error);
            }
          } else {
            reject(error);
          }
          // ToastUtil.exception(error);
        }).finally(() => {
          // hide loading
          if (!isLoadingManual.value) {
            setLoading(false);
          }
        });
    });
  }

  get(url, data, config) {
    // merge params to axios config
    const axiosConfig = mergeAxiosConfig(data, config);
    return this.request('GET', url, null, axiosConfig);
  }

  post(url, data, config) {
    // merge params to axios config
    const axiosConfig = mergeAxiosConfig(data, config);
    delete axiosConfig.params;
    return this.request('POST', url, data, axiosConfig);
  }

  put(url, data, config) {
    // merge params to axios config
    const axiosConfig = mergeAxiosConfig(data, config);
    delete axiosConfig.params;
    return this.request('PUT', url, data, axiosConfig);
  }

  delete(url, data, config) {
    // merge params to axios config
    const axiosConfig = mergeAxiosConfig(data, config);
    delete axiosConfig.params;
    return this.request('DELETE', url, data, axiosConfig);
  }
}
