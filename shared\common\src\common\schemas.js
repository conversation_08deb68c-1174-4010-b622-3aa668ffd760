const { OPTION_TYPE } = require('../helpers/enum');
const { sharedSchema } = require('../validations');

const getOptionsSchema = {
  summary: 'Get option',
  description: 'Get option by group name',
  tags: ['Common'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    required: ['type'],
    properties: {
      type: {
        type: 'string',
        enum: Object.values(OPTION_TYPE),
      },
    },
  },
};

const getPresignedUrlSchema = {
  summary: 'Get presigned URL',
  description: 'Get presigned URL for document',
  tags: ['Common'],
  security: [{ 'apiKey': [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    properties: {
      isAdmin: {
        type: 'boolean',
        default: true,
      },
    }
  },
};

const getCodeSuffixSchema = {
  summary: 'Get code suffix',
  description: 'Get code suffix for a shipment',
  tags: ['Common'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    properties: {
      code_group_key: {
        type: 'string',
        description: 'Code group key (apply code + date now) to get the suffix for',
      },
    },
    required: ['code_group_key'],
  },
}

module.exports = {
  getOptionsSchema,
  getPresignedUrlSchema,
  getCodeSuffixSchema,
};
