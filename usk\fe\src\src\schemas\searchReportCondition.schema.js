import MESSAGE from 'helpers/message';

const searchReportConditionSchema = {
  type: 'object',
  additionalProperties: false,
  properties: {
    startDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
  },
};

export default searchReportConditionSchema;
