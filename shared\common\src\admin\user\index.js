// ===== 1. Injection ==============
const { auth, apiKeyVerify, authAdmin } = require('../../base/authorized');
const {
  getUserListAdminSchema,
  getUserDetailAdminSchema,
  registerNewUserSchema,
  editUserSchema,
  presignImportUserSchema,
  getImportUserSchema,
  downloadImportUserSchema,
  bulkUpdateUserStatusSchema,
} = require('./schema');

// ===== 2. Router Functions ==============
async function getUserListAdminHandler(request, reply) {
  const { query } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.getUserListAdmin(query);
  return reply.send(response);
}

async function getUserDetailAdminHandler(request, reply) {
  const { params } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.getUserDetail(params.id);
  return reply.send(response);
}

async function editUserAdminHandler(request, reply) {
  const { params, body, user } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.editUser(params.id, body, user);
  return reply.send(response);
}

async function registerUserAdminHandler(request, reply) {
  const { body, user } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.registerNewUser(body, user);
  return reply.send(response);
}

async function presignImportUserAdminHandler(request, reply) {
  const { body } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.presignImportUser(body);
  return reply.send(response);
}

async function getImportUserAdminHandler(request, reply) {
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.getListImportUserFile();
  return reply.send(response);
}

async function downloadImportUserAdminHandler(request, reply) {
  const { body } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.downloadDataImportUserByFile(body);
  return reply.send(response);
}

async function bulkUpdateUserStatusHandler(request, reply) {
  const { body, user } = request;
  const userAdminService = request.diScope.resolve('userAdminService');
  const response = await userAdminService.bulkUpdateUserStatus(body, user);
  return reply.send(response);
}


// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get('/get', { schema: getUserListAdminSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, getUserListAdminHandler);
  fastify.get('/user-detail/:id', { schema: getUserDetailAdminSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, getUserDetailAdminHandler);
  fastify.post('/edit-user/:id', { schema: editUserSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, editUserAdminHandler);
  fastify.post('/register-user', { schema: registerNewUserSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, registerUserAdminHandler);
  fastify.post('/presign-import-user', { schema: presignImportUserSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, presignImportUserAdminHandler);
  fastify.get('/get-import-user', { schema: getImportUserSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, getImportUserAdminHandler);
  fastify.post('/download-import-user', { schema: downloadImportUserSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, downloadImportUserAdminHandler);
  fastify.post('/bulk-update-user-status', { schema: bulkUpdateUserStatusSchema, onRequest: [apiKeyVerify, auth, authAdmin] }, bulkUpdateUserStatusHandler);
};
