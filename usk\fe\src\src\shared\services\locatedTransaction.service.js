import BaseService from 'services/base.service';

// TODO: change name class to PartnerService
// TODO: change file name to partner.service.js
class LocatedTransactionService extends BaseService {
  async searchAll(params) {
    try {
      return this.dao.searchAll(params);
    } catch (error) {
      return null;
    }
  }

  async searchPartnerHybridList(params) {
    try {
      return this.dao.searchPartnerHybridList(params);
    } catch (error) {
      return null;
    }
  }

  async getPartnerHybridDetail(id) {
    try {
      return this.dao.getPartnerHybridDetail(id);
    } catch (error) {
      return null;
    }
  }

  async registerPartnerHybrid(params) {
    try {
      return this.dao.registerPartnerHybrid(params);
    } catch (error) {
      return null;
    }
  }

  async toggleHidden(params, enterpriseCode) {
    try {
      return this.dao.toggleHidden(params, enterpriseCode);
    } catch (error) {
      return null;
    }
  }

  async registerHandMade(params) {
    try {
      return this.dao.registerHandMade(params);
    } catch (error) {
      return null;
    }
  }

  async getLocatedTransactionDetail(id) {
    try {
      return this.dao.getLocatedTransactionDetail(id);
    } catch (error) {
      return null;
    }
  }

  async updateLocatedTransaction(id, params) {
    try {
      return this.dao.updateLocatedTransaction(id, params);
    } catch (error) {
      return null;
    }
  }

  async deleteLocatedTransaction(id) {
    try {
      return this.dao.deleteLocatedTransaction(id);
    } catch (error) {
      return null;
    }
  }
}

export default new LocatedTransactionService('located-transaction');
