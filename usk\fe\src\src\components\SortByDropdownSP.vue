<template>
  <div class="vs flex items-center space-x-2">
    <span class="text-xl font-bold">表示順</span>
    <q-select
      @update:model-value="(option) => handleClickOption(option)"
      :model-value="model"
      :options="provideData.sortByOptions"
      outlined
      hide-dropdown-icon
      hide-bottom-space
      class="tw:w-[25rem] tw:text-m-design tw:h-[4.25rem]"
      popup-content-class="popup-content-class-custom tw:text-m-design"
    >
      <template v-slot:append>
        <q-icon
          name="expand_more"
          class="tw:bg-[#004AB9] tw:h-[4.25rem] tw:w-[4.25rem]"
          color="white"
          size="1.5em"
        />
      </template>
    </q-select>
  </div>
</template>

<script setup>
import { inject } from 'vue';

const provideData = inject('sortByDropdownProvideData');
const model = defineModel();
const handleClickOption = option => {
  model.value = option;
  provideData.handleClickSortByItem(option);
};
</script>

<style scoped>
:deep(.q-icon.q-btn-dropdown__arrow) {
  display: none;
}

.q-field--outlined .q-field__control {
  padding-right: 0px !important;
  padding-left: 12px !important;
}
</style>
