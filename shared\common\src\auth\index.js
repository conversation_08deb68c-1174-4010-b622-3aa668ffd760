// ======== IMPORT ========================
const {
  loginSchema,
  loginLinkSchema,
  checkLoginSchema,
  smsPasswordSchema,
  resetPasswordSchema,
  adminLoginSchema,
  recoveryPasswordSchema,
  loginInsteadSchema,
} = require('./schema');
const { auth, apiKeyVerify, authForUserTemporary, authStatusRegister } = require('../base/authorized');

// ===== 1. Injection ==============

// ===== 2. Router Functions ==============

// Login
async function loginHandler(request, reply) {
  const {
    body,
  } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.login(body);
  return reply.send(response);
}

/**
 * admin login
 * @param {*} request
 * @param {*} reply
 * @returns
 */
async function adminLoginHandler(request, reply) {
  const {
    body,
  } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.adminLogin(body);
  return reply.send(response);
}

// Login QR code
async function loginLink(request, reply) {
  const {
    body,
  } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.loginLink(body);
  return reply.send(response);
}

// Check login
async function getLoginHandler(request, reply) {
  const user = request.user;
  const authService = request.diScope.resolve('authService');
  const response = await authService.checkLogin(user);
  return reply.send(response);
}

// Send sms password
async function smsPasswordHandler(request, reply) {
  const {
    body,
  } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.smsPassword(body);
  return reply.send(response);
}

// Reset password
async function resetPasswordHandler(request, reply) {
  const { body, params } = request;
  const { token } = params;
  const authService = request.diScope.resolve('authService');
  const response = await authService.resetPassword(body, token);
  return reply.send(response);
}

// Login instead user id
async function loginInsteadUserIdHandler(request, reply) {
  const { user, body } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.loginInsteadUserId(user, body);
  return reply.send(response);
}

// Login instead user id
async function loginInsteadLinkHandler(request, reply) {
  const { user, body } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.loginInsteadLink(user, body);
  return reply.send(response);
}

async function recoveryPasswordHandler(request, reply) {
  const { body, user } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.recoveryPassword(user, body);
  return reply.send(response);
}

// eslint-disable-next-line no-unused-vars
async function adminLoginProxyUserHandler(request, reply) {
  const { params } = request;
  const authService = request.diScope.resolve('authService');
  const response = await authService.adminLoginProxyUser(params.id);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.post('/login', { schema: loginSchema, onRequest: [apiKeyVerify] }, loginHandler);
  fastify.post('/admin/login', { schema: adminLoginSchema, onRequest: [apiKeyVerify] }, adminLoginHandler);
  fastify.post('/login-link', { schema: loginLinkSchema, onRequest: [apiKeyVerify] }, loginLink);
  fastify.get('/checklogin', { schema: checkLoginSchema, onRequest: [apiKeyVerify, authForUserTemporary, authStatusRegister] }, getLoginHandler);
  fastify.post('/sms-password', { schema: smsPasswordSchema, onRequest: [apiKeyVerify] }, smsPasswordHandler);
  fastify.post('/login-instead/user-id', { schema: loginInsteadSchema, onRequest: [apiKeyVerify, auth] }, loginInsteadUserIdHandler);
  fastify.post('/login-instead-link', { schema: loginLinkSchema, onRequest: [apiKeyVerify, auth] }, loginInsteadLinkHandler);
  fastify.post('/resetpassword/:token', { schema: resetPasswordSchema, onRequest: [apiKeyVerify] }, resetPasswordHandler);
  fastify.post('/recovery-password', { schema: recoveryPasswordSchema, onRequest: [apiKeyVerify, authForUserTemporary] }, recoveryPasswordHandler);
};