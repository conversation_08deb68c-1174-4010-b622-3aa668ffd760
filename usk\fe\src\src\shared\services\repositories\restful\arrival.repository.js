import BaseRepository from './base.repository';

class ArrivalRepository extends BaseRepository {
  constructor() {
    super('arrival');
  }

  getDetailQrArrival(query) {
    return this.client.get('/detail-qr', query);
  }

  registerArrivalManual(data) {
    return this.client.post('/register-manual', data);
  }

  registerArrivalQr(data) {
    return this.client.post('/register-by-qr', data);
  }

  getArrivalList(query) {
    return this.client.get('/arrival-list', query);
  }

  getArrivalDetail(id) {
    return this.client.get(`/arrival-detail/${id}`);
  }

  editArrival(id, data) {
    return this.client.post(`/edit-arrival/${id}`, data);
  }

  cancelArrival(id) {
    return this.client.post(`/cancel-arrival/${id}`);
  }
}

export default new ArrivalRepository();
