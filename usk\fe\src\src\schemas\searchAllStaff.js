import MESSAGE from 'helpers/message';

const searchAllStaff = {
  additionalProperties: false,
  type: 'object',
  properties: {
    pageIndex: {
      type: 'integer',
      default: 1,
      errorMessage: {
        _: MESSAGE.MSG_PAGE_INDEX_INVALID,
      },
    },
    pageSize: {
      type: 'integer',
      default: 10,
      errorMessage: {
        _: MESSAGE.MSG_PAGE_SIZE_INVALID,
      },
    },
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    name: {
      type: 'string',
      maxLength: 50,
      minLength: 0,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
    },
    note2: {
      type: 'string',
      maxLength: 256,
    },
    keySort: {
      type: 'string',
      enum: ['license_number', 'id', 'desc_id', 'name_kana'],
    },
  },
};

export default searchAllStaff;
