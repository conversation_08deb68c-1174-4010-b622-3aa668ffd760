<template>
  <form>
    <div
      class="tw:pb-5 tw:tl:pb-0 tw:grid tw:grid-rows-1 tw:grid-cols-1 tw:tl:grid-cols-2
      tw:gap-14 tw:px-[.625rem]"
    >
      <div>
        <BaseLabel label="出荷先(届出事業者名)" />
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.enterpriseName"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.enterpriseName"
          :error-message="searchFormProvideData.errors.value.enterpriseName"
          no-error-icon
          maxlength="256"
        />
      </div>
      <div>
        <BaseLabel label="出荷先(事業者名)" />
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.destination"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.destination"
          :error-message="searchFormProvideData.errors.value.destination"
          no-error-icon
          maxlength="256"
        />
      </div>
      <div>
          <BaseLabel label="出荷日" />
          <div class="tw:flex tw:gap-1 tw:items-center">
            <div class="tw:flex-1">
              <BaseDatePicker
              v-model.trim="searchFormProvideData.form.value.startDate"
              :error="!!searchFormProvideData.errors.value.startDate"
              :error-message="searchFormProvideData.errors.value.startDate"
              input-class="tw:text-m-design"
              />
            </div>
            <span>～</span
            >
            <div class="tw:flex-1">
              <BaseDatePicker
              v-model.trim="searchFormProvideData.form.value.endDate"
              :error="!!searchFormProvideData.errors.value.endDate"
              :error-message="searchFormProvideData.errors.value.endDate"
              input-class="tw:text-m-design"
              />
            </div>
          </div>
      </div>
      <div>
        <BaseLabel label="届出番号" />
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.code"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          maxlength="7"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.code"
          :error-message="searchFormProvideData.errors.value.code"
          no-error-icon
          inputmode="numeric"
        />
      </div>
      <div>
        <BaseLabel label="許可番号" />
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.licenseNumber"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          maxlength="256"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.licenseNumber"
          :error-message="searchFormProvideData.errors.value.licenseNumber"
          no-error-icon
        />
      </div>
      <div>
        <BaseLabel label="備考１" />
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.note1"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          maxlength="256"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.note1"
          :error-message="searchFormProvideData.errors.value.note1"
          no-error-icon
        />
      </div>
      <div>
        <BaseLabel label="備考２" />
        <BaseInput
          v-model.trim="searchFormProvideData.form.value.note2"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          maxlength="256"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.note2"
          :error-message="searchFormProvideData.errors.value.note2"
          no-error-icon
        />
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import BaseDatePicker from 'src/components/base/vs/BaseDatePicker.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import {
  inject,
} from 'vue';
// #endregion

// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion

</script>
