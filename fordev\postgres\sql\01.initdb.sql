-- public.enterprises definition

-- Drop table

-- DROP TABLE public.enterprises;

CREATE TABLE public.enterprises (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	enterprise_code varchar(256) NOT NULL,
	enterprise_name varchar(256) NOT NULL,
	enterprise_name_kana varchar(256) NULL,
	enterprise_name_nospace varchar(256) NOT NULL,
	enterprise_name_kana_nospace varchar(256) NULL,
	"type" int4 NOT NULL,
	CONSTRAINT enterprises_pkey PRIMARY KEY (id)
);
CREATE INDEX enterprises_enterprise_code_idx ON public.enterprises USING btree (enterprise_code);
CREATE INDEX enterprises_enterprise_name_kana_nospace_idx ON public.enterprises USING btree (enterprise_name_kana_nospace);
CREATE INDEX enterprises_enterprise_name_nospace_idx ON public.enterprises USING btree (enterprise_name_nospace);

-- public.inventories definition

-- Drop table

-- DROP TABLE public.inventories;

CREATE TABLE public.inventories (
	id serial4 NOT NULL,
	delete_flag bool DEFAULT false NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	group_name varchar(256) NULL,
	net_weight_inventory numeric(65, 2) NOT NULL,
	net_weight_total numeric(65, 2) NOT NULL,
	latest_arrival_date timestamp(3) NOT NULL,
	cancelable_from_date timestamp(3) NULL,
	is_not_reset_edit bool NOT NULL,
	gross_weight_inventory numeric(65, 2) NOT NULL,
	tare_weight_inventory numeric(65, 2) NOT NULL,
	user_id int4 NOT NULL,
	CONSTRAINT inventories_pkey PRIMARY KEY (id)
);
CREATE INDEX inventories_user_id_idx ON public.inventories USING btree (user_id);
CREATE INDEX inventories_group_name_idx ON public.inventories USING btree (group_name);
CREATE INDEX inventories_latest_arrival_date_idx ON public.inventories USING btree (latest_arrival_date);

-- public.inventories_history definition

-- Drop table

-- DROP TABLE public.inventories_history;

CREATE TABLE public.inventories_history (
	id serial4 NOT NULL,
	delete_flag bool DEFAULT false NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	inventory_id int4 NOT NULL,
	group_name varchar(256) NULL,
	net_weight_inventory numeric(65, 2) NOT NULL,
	net_weight_total numeric(65, 2) NOT NULL,
	latest_arrival_date timestamp(3) NOT NULL,
	is_not_reset_edit bool NOT NULL,
	gross_weight_inventory numeric(65, 2) NOT NULL,
	new_gross_weight_inventory numeric(65, 2) NOT NULL,
	new_net_weight_inventory numeric(65, 2) NOT NULL,
	new_tare_weight_inventory numeric(65, 2) NOT NULL,
	tare_weight_inventory numeric(65, 2) NOT NULL,
	user_id int4 NOT NULL,
	type_diff int4 NULL,
	reason_diff varchar(256) NULL,
	CONSTRAINT inventories_history_pkey PRIMARY KEY (id)
);
CREATE INDEX inventories_history_user_id_idx ON public.inventories_history USING btree (user_id);
CREATE INDEX inventories_history_inventory_id_idx ON public.inventories_history USING btree (inventory_id);

-- public.licenses definition

-- Drop table

-- DROP TABLE public.licenses;

CREATE TABLE public.licenses (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	license_code varchar(256) NOT NULL,
	user_id int4 NULL,
	expiry_date_start timestamp(3) NULL,
	expiry_date timestamp(3) NULL,
	license_state int4 NULL,
	CONSTRAINT licenses_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX licenses_license_code_key ON public.licenses USING btree (license_code);
CREATE INDEX licenses_user_id_idx ON public.licenses USING btree (user_id);

-- public.notifications definition

-- Drop table

-- DROP TABLE public.notifications;

CREATE TABLE public.notifications (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	title varchar(256) NULL,
	"content" text NULL,
	date_send timestamp(3) NOT NULL,
	role_notify _varchar NULL,
	CONSTRAINT notifications_pkey PRIMARY KEY (id)
);

-- public.partners definition

-- Drop table

-- DROP TABLE public.partners;

CREATE TABLE public.partners (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	user_id int4 NOT NULL,
	partner_id int4 NOT NULL,
	hidden_flag bool DEFAULT false NOT NULL,
	partner_type _int4 DEFAULT ARRAY[]::integer[] NULL,
	CONSTRAINT partners_user_id_partner_id_key UNIQUE (user_id, partner_id),
	CONSTRAINT partners_pkey PRIMARY KEY (id)
);
CREATE INDEX partners_user_id_idx ON public.partners USING btree (user_id);
CREATE INDEX partners_partner_id_idx ON public.partners USING btree (partner_id);

-- public.readers definition

-- Drop table

-- DROP TABLE public.readers;

CREATE TABLE public.readers (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NOT NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	notification_id int4 NOT NULL,
	reader_id int4 NOT NULL,
	CONSTRAINT readers_pkey PRIMARY KEY (id)
);
CREATE INDEX readers_notification_id_idx ON public.readers USING btree (notification_id);
CREATE INDEX readers_reader_id_idx ON public.readers USING btree (reader_id);

-- public.settings definition

-- Drop table

-- DROP TABLE public.settings;

CREATE TABLE public.settings (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	user_id int4 NOT NULL,
	unit_type int4 NOT NULL,
	unit_per_gram numeric(65, 2) NOT NULL,
	price_per_gram _numeric DEFAULT ARRAY[]::numeric[]::numeric(65,2)[] NULL,
	destination_id int4 NULL,
	display_shipment_weight bool NOT NULL,
	display_actual_received bool NOT NULL,
	display_proxy_function bool NOT NULL,
	display_import_export_function bool NOT NULL,
	qr_scan_init int4 NULL,
	receipt_number int4 DEFAULT 2 NULL,
	report_type int4 DEFAULT 2 NULL,
	enable_session_timeout bool DEFAULT true NOT NULL,
	session_expirytime int4 DEFAULT 24 NOT NULL,
	CONSTRAINT settings_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX settings_user_id_key ON public.settings USING btree (user_id);

-- public.sys_settings definition

-- Drop table

-- DROP TABLE public.sys_settings;

CREATE TABLE public.sys_settings (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	setting_name varchar(256) NOT NULL,
	setting_value varchar(256) NULL,
	CONSTRAINT sys_settings_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX sys_settings_setting_name_key ON public.sys_settings USING btree (setting_name);

-- public.the_origins definition

-- Drop table

-- DROP TABLE public.the_origins;

CREATE TABLE public.the_origins (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	code text NULL,
	shipping_date timestamp(3) NULL,
	shipping_gross_weight numeric(65, 2) NULL,
	shipping_tare_weight numeric(65, 2) NULL,
	shipping_net_weight numeric(65, 2) NULL,
	shipping_quantity int4 NULL,
	arrival_gross_weight numeric(65, 2) NULL,
	arrival_tare_weight numeric(65, 2) NULL,
	arrival_net_weight numeric(65, 2) NULL,
	arrival_quantity int4 NULL,
	arrival_date timestamp(3) NULL,
	type_diff int4 NULL,
	shipping_type int4 NULL,
	reason_diff varchar(256) NULL,
	qr_code varchar(256) NULL,
	destination_enterprise_id int4 NULL,
	starting_enterprise_id int4 NULL,
	inventory_id int4 NULL,
	ingredient jsonb NULL,
	setting jsonb NULL,
	destination_user_id int4 NULL,
	starting_user_id int4 NULL,
	CONSTRAINT the_origins_pkey PRIMARY KEY (id)
);
CREATE INDEX the_origins_arrival_date_idx ON public.the_origins USING btree (arrival_date);
CREATE INDEX the_origins_code_idx ON public.the_origins USING btree (code);
CREATE INDEX the_origins_destination_enterprise_id_idx ON public.the_origins USING btree (destination_enterprise_id);
CREATE INDEX the_origins_destination_user_id_idx ON public.the_origins USING btree (destination_user_id);
CREATE INDEX the_origins_inventory_id_idx ON public.the_origins USING btree (inventory_id);
CREATE INDEX the_origins_qr_code_idx ON public.the_origins USING btree (qr_code);
CREATE INDEX the_origins_shipping_date_idx ON public.the_origins USING btree (shipping_date);
CREATE INDEX the_origins_shipping_type_idx ON public.the_origins USING btree (shipping_type);
CREATE INDEX the_origins_starting_enterprise_id_idx ON public.the_origins USING btree (starting_enterprise_id);
CREATE INDEX the_origins_starting_user_id_idx ON public.the_origins USING btree (starting_user_id);

-- public.users definition

-- Drop table

-- DROP TABLE public.users;

CREATE TABLE public.users (
	id serial4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	user_code varchar(256) NULL,
	qr_code varchar(256) NULL,
	enterprise_id int4 NULL,
	"name" varchar(256) NOT NULL,
	name_kana varchar(256) NULL,
	phone varchar(256) NOT NULL,
	"password" text NOT NULL,
	"role" varchar(2) NOT NULL,
	statistics_date_from timestamp(3) NULL,
	license_id int4 NULL,
	note_1 varchar(256) NULL,
	note_2 varchar(256) NULL,
	license_number varchar(256) NULL,
	name_nospace varchar(256) NOT NULL,
	name_kana_nospace varchar(256) NULL,
	province_custom_data json NULL,
	loginfail int4 NOT NULL DEFAULT 0,
  locktime timestamp NULL,
	account_registration_status int4 NOT NULL DEFAULT 0,
	status int4 NULL,
	province_id int4 NULL,
	enterprise_type int4 NULL,
	staff_type int4 NULL,
	CONSTRAINT users_pkey PRIMARY KEY (id)
);
CREATE INDEX users_enterprise_id_idx ON public.users USING btree (enterprise_id);
CREATE INDEX users_license_id_idx ON public.users USING btree (license_id);
CREATE INDEX users_name_kana_nospace_idx ON public.users USING btree (name_kana_nospace);
CREATE INDEX users_name_nospace_idx ON public.users USING btree (name_nospace);
CREATE INDEX users_phone_idx ON public.users USING btree (phone);
CREATE INDEX users_status_idx ON public.users USING btree (status);
CREATE UNIQUE INDEX users_user_code_key ON public.users USING btree (user_code);

CREATE TABLE public.provinces (
  id serial4 NOT NULL,
  name varchar(300) NULL,
  setting json NOT NULL,
  created_by_id int4 DEFAULT 0 NULL,
  latest_updated_by_id int4 NULL,
  created_on timestamp(3) NULL,
  latest_updated_on timestamp(3) NULL,
  delete_flag bool DEFAULT false NOT NULL,
	region_id int4 NOT NULL,
  CONSTRAINT provinces_pkey PRIMARY KEY (id),
);

CREATE TABLE regions (
  id serial4 NOT NULL,
  region_name VARCHAR NOT NULL UNIQUE,
  created_by_id INTEGER NOT NULL DEFAULT 0,
  latest_updated_by_id INTEGER NULL,
  created_on TIMESTAMP NOT NULL,
  latest_updated_on TIMESTAMP NULL,
  delete_flag BOOLEAN NOT NULL DEFAULT FALSE,
	CONSTRAINT regions_pkey PRIMARY KEY (id),
);