// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #1976d2;
$secondary: #26a69a;
$accent: #9c27b0;

$dark: #1d1d1d;
$dark-page: #121212;

$positive: #21ba45;
$negative: #c10015;
$info: #31ccec;
$warning: #f2c037;
$blue-1: #3f83f8;
$blue-2: #1d4ed8;
$blue-3: #004ab9;
$green-1: #0d7d4d;
$green-2: #044127;
$purple-1: #8e45a7;
$purple-2: #40043a;
$blue-5: #d6ecfc;
$red-1: #f4511e;
$blue-4: #1d4dd8;

