const {
  ROLES_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  USER_STATUS_ENUM,
  PROVINCES_ENUM,
  STAFF_TYPE_ENUM,
  TOTAL_LIMIT_EXPORT } = require("../helpers/enum");
const { hash } = require("../utils/crypto");
const { ControlledException } = require("../base/errors");
const BaseService = require("../base/serviceFn");
const { MESSAGE } = require('../utils/message');
const sprintf = require('sprintf-js').sprintf;
const { Prisma } = require('@prisma/client');

class StaffService extends BaseService {
  async _checkRoleEnterprise(connect, userId) {
    const user = await connect.users.findFirst({
      where: {
        id: userId,
        delete_flag: false,
      }
    });
    if (!user?.user_code?.endsWith('-0000')) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }
  }
  async searchAll(params) {
    const { page, limit, name, sortBy, userId, enterpriseId, licenseNumber, note1, note2, isMobile } = params;
    const connect = this.DB.READ;

    await this._checkRoleEnterprise(connect, userId);
    const query = {
      where: {
        NOT: {
          id: userId,
        },
        AND: {
          delete_flag: false,
          status: { not: USER_STATUS_ENUM.PENDING },
          enterprise_id: enterpriseId,
          note_1: note1 ? {
            contains: note1,
          } : undefined,
          note_2: note2 ? {
            contains: note2,
          } : undefined,
          license_number: licenseNumber ? {
            contains: licenseNumber
          } : undefined
        }
      }
    }
    if (name) {
      query.where.OR = [
        {
          name_nospace: {
            contains: name.replace(/\s+/g, '')
          }
        },
        {
          name_kana_nospace: {
            contains: name.replace(/\s+/g, '')
          }
        }
      ]
    }
    const count = await connect.users.count(query);
    if (count === 0) {
      return this.SUCCESS({
        items: [],
        total_item: 0,
        page: 1,
        page_size: limit,
      });
    }
    if (count > TOTAL_LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_TOO_MANY_RESULTS);
    }

    // calculate offset
    const offset = (+page - 1) * +limit;
    let tempPage = +page;
    let tempOffset = offset;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= count) {
      tempPage = 1;
      tempOffset = (tempPage - 1) * +limit;
    }
    query.orderBy = { id: 'desc' };

    if (isMobile) {
      query.take = +limit;
      query.orderBy = sortBy === 'id' ? { id: 'desc' } : sortBy === 'desc_id' ? { id: 'asc' } : sortBy === 'license_number' ? {
        license_number: 'asc'
      } :
        { name_kana_nospace: 'asc' };
      query.skip = tempOffset;
    }

    const data = await connect.users.findMany({
      include: {
        license: true,
      },
      ...query,
    });
    return this.SUCCESS({
      items: data.map(item => {
        delete item.password;
        return item;
      }),
      total_item: count,
      page: tempPage,
      page_size: +limit,
    })
  }

  async registerNewStaff(params) {
    const { userId, enterpriseId, enterpriseType, name, nameKana, licenseNumber, phone, note1, note2, password } = params;
    const connect = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    await this._checkRoleEnterprise(connect, userId);
    if (phone) {
      const checkPhoneDuplicate = await connect.users.findFirst({
        where: {
          phone
        }
      });
      if (checkPhoneDuplicate) {
        throw new ControlledException(MESSAGE.MSG_USED_FHONENUMBER_ERROR);
      }
    }
    const hashPassword = await hash(password);
    await connectWrite.$transaction(async tx => {
      const newUser = await tx.users.create({
        data: {
          enterprise_id: enterpriseId,
          phone,
          password: hashPassword,
          role: ROLES_ENUM.NORMAL_USER,
          enterprise_type: enterpriseType,
          staff_type: STAFF_TYPE_ENUM.STAFF,
          created_by_id: userId,
          license_number: licenseNumber,
          note_1: note1,
          note_2: note2,
          name,
          name_nospace: name.replace(/\s+/g, ''),
          name_kana: nameKana || null,
          name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
          delete_flag: false,
          status: USER_STATUS_ENUM.ACTIVE,
          province_id: PROVINCES_ENUM.NONE,
        }
      });
      await tx.settings.create({
        data: {
          user_id: newUser.id,
          price_per_kilogram: [200],
          delete_flag: false,
          price_per_quantity: [400],
          qr_scan_init: SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA,
          created_by_id: userId,
          latest_updated_by_id: userId,
        }
      });
    }, { isolationLevel: Prisma.TransactionIsolationLevel.Serializable });
    return this.SUCCESS({ message: MESSAGE.MSG_RESISTER_EMPLOYEE_INFO });
  }

  async editStaff(params) {
    const { id, userId, enterpriseId, name, nameKana, licenseNumber, phone, note1, note2 } = params;
    const connect = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    await this._checkRoleEnterprise(connect, userId);
    if (phone) {
      const checkPhoneDuplicate = await connect.users.findFirst({
        where: {
          phone,
          id: {
            not: +id
          }
        }
      });
      if (checkPhoneDuplicate) {
        throw new ControlledException(MESSAGE.MSG_USED_FHONENUMBER_ERROR);
      }
    }
    if (+id === +userId) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }
    await connectWrite.users.update({
      where: {
        id: +id,
        enterprise_id: enterpriseId,
      },
      data: {
        phone,
        note_1: note1,
        note_2: note2,
        license_number: licenseNumber,
        name,
        name_nospace: name.replace(/\s+/g, ''),
        name_kana: nameKana || null,
        name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
        latest_updated_by_id: userId,
      }
    });
    return this.SUCCESS({
      message: MESSAGE.MSG_FIX_EMPLOYEE_INFO
    })
  }

  async deleteStaff(params) {
    const { userId, enterpriseId, id } = params;
    const connect = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    await this._checkRoleEnterprise(connect, userId);
    const delUser = await connect.users.findFirst({
      where: {
        id: +id,
        delete_flag: false,
      }
    });
    if (!delUser || delUser.id === userId || delUser.enterprise_id !== enterpriseId) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }
    await connectWrite.users.update({
      where: {
        id: +id
      },
      data: {
        delete_flag: true,
        latest_updated_by_id: userId,
      }
    });
    return this.SUCCESS({ message: sprintf(MESSAGE.MSG_DEL_EMPLOYEE_INFO, delUser.name) });
  }

  async getStaff(params) {
    const { id, userId, enterpriseId } = params;
    const connect = this.DB.READ;
    await this._checkRoleEnterprise(connect, userId);
    const user = await connect.users.findFirst({
      include: {
        license: true,
      },
      where: {
        id: +id,
        enterprise_id: enterpriseId,
        delete_flag: false,
      }
    });
    if (!user) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR, {}, 402);
    }
    delete user.password;
    return this.SUCCESS({ data: user });
  }
}

module.exports = StaffService;