const nodemailer = require('nodemailer');
const Env = require('../boot/env');
const { APP_LOGGER } = require('../boot/logger');


const transporter = nodemailer.createTransport({
  service: 'gmail',
  host: 'smtp.gmail.com',
  auth: {
    user: '<EMAIL>',
    pass: 'bwelwpkamynczqha'
  }
});

function sendMail(to, subject, htmlContent, from = null) {

  let prefix = Env.NODE_ENV === 'production' ? '' : `「${Env.NODE_ENV}」`;
  transporter.sendMail({
    from: from ?? 'Unagi',
    to: to,
    subject: `${prefix}${subject}`,
    html: htmlContent
  }, function (err) {
    if (err) {
      APP_LOGGER.info(`[FAILED] Send mail ${subject} to ${to}`);
    } else {
      APP_LOGGER.info(`[SUCCESS] Send mail ${subject} to ${to}`);
    }
  });
}

module.exports = { sendMail };