<template>
  <q-card class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
    <div class="tw:p-4 tw:text-[#333333] tw:flex-1">
      <div class="tw:text-s-design">
        ユーザーIDを入力してログインボタンを押してください。
      </div>
      <!-- content -->
      <div class="tw:mt-3">
        <BaseLabel label="ユーザーID" isRequired />
        <q-input
          class="tw:mt-3"
          input-class=" tw:text-s-design tw:font-normal tw:text-[#333333]"
          placeholder="ユーザーIDを入力してください。"
          outlined
          v-model.trim="userId"
          autocomplete="nope"
          inputmode="numeric"
          maxlength="12"
          :error="!!errors.userid"
          :error-message="errors.userid"
        />

        <div class="text-center">
          <BaseButton
            outline

            class="tw:rounded-[40px]"
            :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
            tw:tl:w-[16.5rem] tw:tl:h-[5.5rem]
            tw:w-full`"
            label="ログイン"
            @click.prevent="handleLogin"
          />
        </div>
      </div>
    </div>
    <!-- Footer Button -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:justify-start tw:tl:items-center tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-8 tw:tl:flex-row"
    >
      <div class="tw:text-[#333333] tw:text-m-design tw:font-normal">
        入荷登録方法
      </div>
      <div class="tw:flex tw:gap-4 tw:tl:gap-8">
        <BaseButton
          outline

          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[19.5rem] tw:tl:h-[5.5rem]
        tw:w-full`"
          label="QRスキャナー"
          @click.prevent="handleClickQrScan"
        />

        <BaseButton
          outline

          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[16.5rem] tw:tl:h-[5.5rem]
        tw:w-full`"
          label="カメラ"
          @click.prevent="handleClickQrCamera"
        />
      </div>

      <BaseButton
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[20.5rem] tw:tl:h-[5.5rem]
      tw:w-full`"
        label="伝票から手入力"
        @click.prevent="handleClickManualRegistration"
      />
    </q-footer>
  </q-card>
</template>

<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import useValidate from 'composables/validate';
import loginInsteadSchema from 'schemas/loginInsteadUserId';
import authService from 'services/auth.service';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import { OPTION_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';
import commonService from 'src/shared/services/common.service';
import toast from 'src/shared/utilities/toast';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// ===== REF =====
const userId = ref('');
const { errors, validateData } = useValidate();

// ===== METHOD =====

const handleLogin = async () => {
  const formData = {
    userid: userId.value,
  };

  const valid = validateData(loginInsteadSchema, formData);
  if (!valid) {
    return;
  }

  const response = await authService.loginInsteadUserId(formData);
  if (response) {
    await router.push({
      name: 'registerProxyOutboundShipment',
    });
  }
};

const handleClickQrScan = () => {
  router.push({ name: 'arrivalQrScan' });
};

const handleClickQrCamera = () => {
  router.push({ name: 'arrivalQrCamera' });
};

const handleClickManualRegistration = async () => {
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SHIPPER,
  });
  if (partnerOptionsResponse.payload?.length === 0) {
    toast.error(MESSAGE.MSG_NA_CUSTOMER_ERR);
  } else {
    router.push({ name: 'manualRegistration' });
  }
};
</script>

<style>
.badgeColor {
  background-color: #e80f00 !important;
  color: #ffffff !important;
}
</style>
