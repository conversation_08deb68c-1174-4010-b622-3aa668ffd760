/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');

module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
  ],
  important: true,
  theme: {
    fontFamily: {
      sans: ['"Noto Sans JP"', '"Noto Sans"', '"Noto Sans CJK JP"', '"Noto Serif JP"', '"Meiryo"', ...defaultTheme.fontFamily.sans],
    },
    extend: {
      fontFamily: {
        pdfSans: ['"Noto Sans JP"', '"Noto Sans"', '"Noto Sans CJK JP"', '"Noto Serif JP"', ...defaultTheme.fontFamily.sans],
      },
      colors: {
        'blue-1': '#3F83F8',
        'blue-2': '#1D4ED8',
        'blue-3': '#004AB9',
        'green-1': '#0D7D4D',
        'green-2': '#044127',
        'purple-1': '#8E45A7',
        'purple-2': '#40043A',
        qcard: '#d6ecfc',
        red: '#F4511E',
        blue: '#2962FF',
        gray: '#737373',
        'red-2': '#FD0707',
        'gray-1': '#707070',
        'red-error': '#C10015',
      },
    },
  },
  plugins: [],
  safelist: [
    {
      pattern: /^tw:(text|bg|border|fill)-(blue|green|purple)-(1|2)$/,
    },
  ],
};
