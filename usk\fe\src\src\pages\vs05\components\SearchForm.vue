<template>
  <form>
    <div
      class="tw:flex tw:flex-col tw:gap-[.5rem] tw:tl:gap-[2rem] tw:px-[.625rem]"
    >
      <div
        class="tw:flex tw:w-full tw:flex-col tw:tl:flex-row tw:gap-[.5rem] tw:tl:gap-[2rem] tw:justify-between"
      >
        <div
          class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mb-3 tw:tl:mb-0"
        >
          <div class="tw:mb-[.25rem] tw:tl:mb-[1rem]">氏名</div>
          <BaseInput
            outlined
            input-class="tw:w-full"
            class="tw:rounded-lg"
            v-model.trim="searchFormProvideData.form.value.name"
            autocomplete="nope"
            maxlength="50"
            :error="!!searchFormProvideData.errors.value.name"
            :error-message="searchFormProvideData.errors.value.name"
            no-error-icon
          />
        </div>
        <div
          class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mb-3 tw:tl:mb-0"
        >
          <div class="tw:mb-[.25rem] tw:tl:mb-[1rem]">許可番号</div>
          <BaseInput
            outlined
            input-class="tw:w-full"
            class="tw:rounded-lg"
            v-model.trim="searchFormProvideData.form.value.licenseNumber"
            maxlength="256"
            autocomplete="nope"
            :error="!!searchFormProvideData.errors.value.licenseNumber"
            :error-message="searchFormProvideData.errors.value.licenseNumber"
            no-error-icon
          />
        </div>
      </div>
      <div
        class="tw:flex tw:w-full tw:flex-col tw:tl:flex-row tw:gap-[.5rem] tw:tl:gap-[2rem] tw:justify-between"
      >
        <div
          class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mb-3 tw:tl:mb-0"
        >
          <div class="tw:mb-[.25rem] tw:tl:mb-[1rem]">備考１</div>
          <BaseInput
            outlined
            input-class="tw:w-full"
            class="tw:rounded-lg"
            v-model.trim="searchFormProvideData.form.value.note1"
            autocomplete="nope"
            maxlength="256"
            :error="!!searchFormProvideData.errors.value.note1"
            :error-message="searchFormProvideData.errors.value.note1"
            no-error-icon
          />
        </div>
        <div
          class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mb-3 tw:tl:mb-0"
        >
          <div class="tw:mb-[.25rem] tw:tl:mb-[1rem]">備考２</div>
          <BaseInput
            outlined
            input-class="tw:w-full"
            class="tw:rounded-lg"
            v-model.trim="searchFormProvideData.form.value.note2"
            autocomplete="nope"
            maxlength="256"
            :error="!!searchFormProvideData.errors.value.note2"
            :error-message="searchFormProvideData.errors.value.note2"
            no-error-icon
          />
        </div>
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import { inject } from 'vue';
// #endregion

// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion
</script>
