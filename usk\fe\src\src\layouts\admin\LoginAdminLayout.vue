<template>
  <q-layout view="lHh Lpr lFf" class="tw:flex">
    <q-header elevated :class="`tw:bg-${colorMain}`">
      <q-toolbar>

        <q-toolbar-title>
          <div class="tw:flex tw:justify-between">
            <div :class="`tw:flex-1 tw:text-[1.5rem] tw:flex tw:font-bold tw:py-3
                tw:flex-wrap tw:tl:justify-center`">
              {{ envPrefix }}シラスウナギトレーサビリティ支援システム（管理者用）
            </div>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <div class="tw:w-screen tw:flex-1 tw:pt-[3.5rem] tw:tl:pt-[5.3rem] tw:dt:pt-[3rem]">
      <q-scroll-area :thumb-style="thumbStyle" :bar-style="barStyle" class="tw:w-full tw:h-full" ref="scrollAreaRef">
        <q-page-container
          class="tw:px-3 tw:tl:px-8 tw:tl:pt-[2rem]! tw:pt-[8rem] tw:bg-[#F7F7F9] tw:min-h-[calc(100vh-6rem)]">
          <div class="w-content tw:tl:w-[calc(100vw-4rem)] tw:mb-3 tw:dt:mt-[1rem]">
            <div class="tw:flex tw:justify-between" v-if="
              router.currentRoute.value?.meta?.title &&
              !router.currentRoute.value?.meta?.isBreadcrumb
            ">
              <div
                class="tw:tl:leading-[4rem] tw:tl:text-[4rem] tw:font-bold tw:text-2xl tw:border-b tw:border-gray tw:pb-6 tw:pt-4 tw:flex-1">
                {{ router.currentRoute.value.meta.title }}
              </div>
            </div>
            <div class="tw:flex tw:justify-between" v-if="router.currentRoute.value?.meta?.isBreadcrumb">
              <!-- Breadcrumbs -->
              <q-breadcrumbs class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:flex-1"
                active-color="tw:text-blue-3">
                <template v-slot:separator>
                  <q-icon size="1.5em" name="chevron_right" class="tw:text-[#7E8093]" />
                </template>
                <q-breadcrumbs-el class="tw:cursor-pointer" label="トップ" @click.prevent="gotoPage('adminUserManager')" />
                <q-breadcrumbs-el :label="`${router.currentRoute.value.meta.title}`" @click.prevent="
                  gotoPage(`${router.currentRoute.value.meta.prevRoute}`)
                  " />
                <div v-if="router.currentRoute.value.meta.tag"
                  class="tw:ml-2 tw:text-[#333333] tw:flex tw:items-center tw:justify-center">
                  <span class="tw:text-[#7E8093] tw:pb-1 tw:pl-4 tw:pr-5">|</span>
                  {{
                    router.currentRoute.value.meta.tag
                      ? router.currentRoute.value.meta.tag
                      : ""
                  }}
                </div>
              </q-breadcrumbs>
            </div>
            <router-view />
          </div>
        </q-page-container>
      </q-scroll-area>
    </div>
  </q-layout>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { onMounted, ref} from 'vue';
import {  useRouter } from 'vue-router';

const { colorMain } = storeToRefs(useAppStore());
const router = useRouter();
const envPrefix = ref('');

const gotoPage = async name => {
  await router.push({ name });
};
onMounted(() => {
  const html = document.querySelector('html');
  html.classList.add('admin-layout');
  envPrefix.value = `${process.env.PREFIX}`;

});

// style scroll
const thumbStyle = {
  backgroundColor: '#737373',
  width: '8px',
  height: '8px',
  opacity: 0.75,
};
const barStyle = {
  backgroundColor: '#737373',
  opacity: 0.3,
};

</script>

<style>
.q-item__label {
  line-height: initial !important;
}
</style>
