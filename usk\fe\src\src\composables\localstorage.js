import { useStorage } from '@vueuse/core';

export function useLocalStorage() {
  const userToken = useStorage('USER_TOKEN', null);
  // for admin proxy user
  const cacheAdminToken = useStorage('CACHE_ADMIN_TOKEN', null);
  // for save query params in each list page
  const listPageQueryParams = useStorage('LIST_PAGE_QUERY_PARAMS', {});
  return {
    userToken,
    cacheAdminToken,
    listPageQueryParams,
  };
}
