<template>
  <PopupConfirm
    v-model="isShowPopupConfirm"
    @onClickCancel="isShowPopupConfirm = false"
    @onClickSubmit="handleClickSubmit"
  >
    <div class="tw:text-m-design tw:p-5">
      <p class="tw:text-m-design">以下の内容で、在庫を修正します</p>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[40%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 総在庫量 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[60%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <div class="tw:flex tw:tl:gap-12">
            <div
              class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row"
            >
              <!-- 全体重量 -->
              <div>
                <span class="tw:text-m-design">
                  {{ form.grossWeight }}
                </span>
                <span class="tw:text-xs-design"> g </span>
              </div>
            </div>
            <div class="tw:flex tw:items-center tw:relative">
              <!-- subtraction sign sm -->
              <div
                class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-2 tw:mx-5 tw:tl:hidden"
              />
              <div
                class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3"
              >
                <!-- subtraction sign tl -->
                <div
                  class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mx-5 tw:hidden tw:tl:block tw:absolute tw:top-[1.5rem] tw:-left-[3.25rem]"
                />
                <!-- 風袋 -->
                <div>
                  <span class="tw:text-m-design">
                    {{ form.tareWeight || 0 }}
                  </span>
                  <span class="tw:text-xs-design"> g </span>
                </div>
              </div>
            </div>
          </div>
          <div class="tw:flex tw:items-center tw:gap-4">
            <!-- 総在庫量 -->
            <span class="tw:text-xs-design">総在庫量</span>
            <div class="tw:font-bold">
              <span class="tw:text-m-design">
                {{ form.netWeight }}
              </span>
              <span class="tw:text-xs-design"> g </span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[40%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 修正理由 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[60%] tw:bg-white tw:px-5 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1
          tw:border-[#E0E0E0] tw:flex tw:items-center tw:min-h-[4.25rem]"
        >
          <span>
            {{
              reasonOptions.find(
                (item) => item.value === form.typeDiff
              )?.label
            }}
          </span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        v-if="form.typeDiff === TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER"
      >
        <div
          class="tw:tl:w-[40%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 「その他」の理由 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[60%] tw:bg-white tw:px-5 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1
          tw:border-[#E0E0E0] tw:flex tw:items-center tw:min-h-[4.25rem]"
        >
          <span v-html="clearHTML(linkify(form.reasonDiff))">
          </span>
        </div>
      </div>
    </div>
  </PopupConfirm>
  <q-card class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
    <q-card class="tw:p-5">
      <div>
        <h2 class="tw:text-l-design tw:font-bold">在庫管理詳細</h2>
      </div>
      <div class="tw:text-m-design tw:mt-5">
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#E0E0E0]"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
          >
            <span> グループ名 </span>
          </div>
          <div
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <span>{{
              form.groupName || inventoryDetail?.fallback_group_name
            }}</span>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          v-if="inventoryDetail?.the_origins"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
          >
            <span> 漁獲/荷口番号：仕入先 </span>
          </div>
          <div
            class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <div :class="`tw:max-h-[16rem] tw:overflow-y-auto`">
              <p class="tw:mb-0"
                v-for="(item, index) in inventoryDetail?.the_origins || []"
                :key="index"
              >
                <span class="tw:hidden tw:md:inline">
                  {{ maskCodeString(item.code) }}：{{
                    `${item.starting_enterprise_name}`
                  }}
                </span>
                <span class="tw:inline tw:md:hidden">
                  {{ maskCodeString(item.code) }}：
                  <br />
                  {{ `${item.starting_enterprise?.enterprise_name}` }}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
          >
            <span> 総入荷量 </span>
          </div>
          <div
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <span>
              {{
                FORMAT_NUMBER(inventoryDetail?.net_weight_total) ||
                '000,000.00'
              }}g
            </span>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
          >
            <span> 総在庫量 </span>
            <q-badge
              class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
            >
              必須
            </q-badge>
          </div>
          <div
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <div
              class="tw:grid tw:grid-cols-1 tw:gap-x-10 tw:gap-y-3 tw:tl:grid-cols-2"
            >
              <div
                class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row"
              >
                <!-- 全体重量 -->
                <span class="tw:text-xs-design tw:min-w-[6rem]">全体重量</span>
                <BaseInput
                  clearable
                  type="text"
                  maxlength="20"
                  inputmode="numeric"
                  autocomplete="nope"
                  :model-value="form.grossWeight"
                  @update:model-value="handleInputGrossWeight"
                  :error-message="errors.grossWeightInventory"
                  :error="!!errors.grossWeightInventory"
                  :mask="{
                    mask: Number, // Chỉ chấp nhận số
                    thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                    scale: 2, // Không cho phép số thập phân
                    signed: false, // Không cho phép số âm
                    min: 0, // Chỉ cho phép số không âm
                    lazy: false, // Hiển thị placeholder ngay lập tức
                    radix: '.',
                    max: 9999999999999.99,
                  }"
                  outlined
                  class="tw:flex-1"
                  input-class="tw:text-right tw:text-m-design"
                >
                  <template v-slot:append>
                    <div
                      :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                    >
                      g
                    </div>
                  </template>
                </BaseInput>
              </div>
              <div class="tw:flex tw:items-center tw:relative">
                <!-- subtraction sign sm -->
                <div
                  class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-12 tw:mx-5 tw:tl:hidden"
                />
                <div
                  class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3"
                >
                  <!-- subtraction sign tl -->
                  <div
                    class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden tw:tl:block tw:absolute tw:top-[2rem] tw:-left-[3.25rem]"
                  />
                  <!-- 風袋 -->
                  <span class="tw:text-xs-design tw:min-w-[3rem]">風袋</span>
                  <BaseInput
                    clearable
                    type="text"
                    maxlength="20"
                    inputmode="numeric"
                    autocomplete="nope"
                    :model-value="form.tareWeight"
                    @update:model-value="handleInputTareWeight"
                    :error="!!errors.tareWeightInventory"
                    :error-message="errors.tareWeightInventory"
                    :mask="{
                      mask: Number, // Chỉ chấp nhận số
                      thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                      scale: 2, // Không cho phép số thập phân
                      min: 0, // Chỉ cho phép số không âm
                      lazy: false, // Hiển thị placeholder ngay lập tức
                      radix: '.',
                      max: 9999999999999.99,
                    }"
                    outlined
                    class="tw:flex-1"
                    input-class="tw:text-right tw:text-m-design"
                  >
                    <template v-slot:append>
                      <div
                        :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-xs-design tw:mt-2`"
                      >
                        g
                      </div>
                    </template>
                  </BaseInput>
                </div>
              </div>
              <!-- Divider -->
              <div
                class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2 tw:md:hidden"
              />
              <div
                class="tw:tl:col-span-2 tw:flex tw:items-center tw:justify-between tw:md:justify-start tw:gap-4 tw:mt-5"
              >
                <!-- 総在庫量 -->
                <span class="tw:text-xs-design">総在庫量</span>
                <div class="tw:font-bold">
                  <span class="tw:text-m-design">
                    {{ form.netWeight !== '0' ? form.netWeight : '000,000.00' }}
                  </span>
                  <span class="tw:text-xs-design"> g </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
          >
            <span>修正理由</span>
            <q-badge
              class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
            >
              必須
            </q-badge>
          </div>
          <div
            class="tw:tl:w-[70%] tw:flex tw:gap-4 tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <q-radio
              v-for="(item, index) in reasonOptions"
              v-model="form.typeDiff"
              :key="index"
              :val="item.value"
              size="3.75rem"
              class="tw:transform tw:-translate-x-3"
            >
              <span class="tw:text-m-design tw:text-[#333333]">
                {{ item.label }}
              </span>
            </q-radio>
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
          >
            <span> 「その他」の理由 </span>
            <q-badge
              class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
            >
              必須
            </q-badge>
          </div>
          <div
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <q-input
              :class="[
                {
                  'tw:bg-[#CACACA] tw:border tw:border-[#D2D2D2]':
                    form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
                },
                'tw:text-m-design',
              ]"
              :disable="form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER"
              v-model="form.reasonDiff"
              outlined
              autocomplete="nope"
              type="textarea"
              maxlength="300"
              :error="!!errors.reasonDiff"
              :error-message="errors.reasonDiff"
              no-error-icon
              hide-bottom-space
            />
          </div>
        </div>
        <div
          class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        >
          <div
            class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
          >
            <span> 最新の入荷日 </span>
          </div>
          <div
            class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
          >
            <span>
              {{ FORMAT_DATE(inventoryDetail?.latest_arrival_date) }}
            </span>
          </div>
        </div>
      </div>
    </q-card>

    <q-footer
      elevated
      class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
      tw:tl:w-[20.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="修正をやめる"
        @click="router.back()"
      />
      <BaseButton
        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[16.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="登録する"
        @click="handleClickConfirm"
      />
    </q-footer>
  </q-card>
</template>
<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import useValidate from 'composables/validate';
import {
  clearHTML,
  doParseFloatNumber,
  FORMAT_DATE,
  FORMAT_NUMBER,
  isNumeric,
  linkify,
  maskCodeString,
} from 'helpers/common';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'helpers/constants';
import { storeToRefs } from 'pinia';
import inventoryManagementService from 'services/inventoryManagement.service';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import { useAppStore } from 'stores/app-store';
import toast from 'utilities/toast';
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import editInventorySchema from 'src/schemas/inventory-management/editInventory.schema';
import MESSAGE from 'src/helpers/message';

import PopupConfirm from './components/PopupConfirm.vue';

// #region state
const { settingUser } = storeToRefs(useAppStore());
const router = useRouter();
const { validateData, errors } = useValidate();

const inventoryDetail = ref(null);
const isShowPopupConfirm = ref(false);
const reasonOptions = ref([
  { label: '斃死', value: TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH },
  { label: '計量誤差', value: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR },
  { label: 'その他', value: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER },
]);
const form = ref({
  groupName: '',
  grossWeight: '',
  tareWeight: '',
  netWeight: '',
  typeDiff: TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH,
  reasonDiff: '',
});

const unitPerGram = ref(settingUser.value.unit_per_gram ?? 0);
// #endregion

// #region actions
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(form.value.tareWeight || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue;
  form.value.netWeight =
    netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(
          Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
        )
      : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(
    form.value.grossWeight || 0
  );
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue;
  form.value.netWeight =
    netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(
          Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
        )
      : undefined;
};

const handleClickConfirm = async () => {
  // TODO: validate form
  const data = {
    grossWeightInventory: doParseFloatNumber(form.value.grossWeight),
    tareWeightInventory: doParseFloatNumber(form.value.tareWeight) || undefined,
    netWeightInventory: doParseFloatNumber(form.value.netWeight),
    quantityInventory: doParseFloatNumber(form.value.quantity),
    typeDiff: form.value.typeDiff,
    reasonDiff: form.value.reasonDiff,
  };
  if (data.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER) {
    delete data.reasonDiff;
  }
  const validate = validateData(editInventorySchema, data);
  if (!validate) {
    return;
  }
  isShowPopupConfirm.value = true;
};

const handleClickSubmit = async () => {
  // TODO: handle submit logic
  const data = {
    groupName: form.value.groupName,
    grossWeightInventory: doParseFloatNumber(form.value.grossWeight),
    tareWeightInventory: doParseFloatNumber(form.value.tareWeight) || 0,
    netWeightInventory: doParseFloatNumber(form.value.netWeight),
    typeDiff: form.value.typeDiff,
    reasonDiff: form.value.reasonDiff,
  };
  const result = await inventoryManagementService.editInventory(
    inventoryDetail.value.id,
    data
  );
  if (result.code === 0) {
    await router.push({ name: 'inventoryList' });
    toast.access(MESSAGE.MSG_FIX_STOCK_INFO);
  }
  isShowPopupConfirm.value = false;
};
watch(
  () => form.value.typeDiff,
  () => {
    if (form.value.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER) {
      form.value.reasonDiff = '';
    }
  }
);
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  const inventoryId = router.currentRoute.value?.params?.id;
  if (!inventoryId) {
    await router.push({ name: 'inventoryList' });
  }
  const inventoryDetailResponse =
    await inventoryManagementService.getInventoryDetail(inventoryId);
  if (inventoryDetailResponse.code !== 401) {
    if (inventoryDetailResponse.code === 0) {
      if (
        !inventoryDetailResponse.payload?.net_weight_inventory
      ) {
        router.back();
      }
      inventoryDetail.value = inventoryDetailResponse.payload;
      form.value.groupName = inventoryDetailResponse.payload.group_name;
      form.value.grossWeight = FORMAT_NUMBER(
        inventoryDetailResponse.payload.net_weight_inventory
      );
      form.value.netWeight = FORMAT_NUMBER(
        inventoryDetailResponse.payload.net_weight_inventory
      );
    } else {
      toast.error(inventoryDetailResponse.message);
      await router.push({ name: 'inventoryList' });
    }
  }
});
// #endregion

// #endregion
</script>
<style scoped>
:deep(textarea) {
  padding-top: 1rem !important;
  line-height: 1.2 !important;
  max-height: 10rem !important;
}
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}
</style>
