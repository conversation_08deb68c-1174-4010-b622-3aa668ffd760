<template>
  <q-page class="h-full" id="header">
    <div class="tw:tl:mb-[8rem] tw:mb-[12rem]">
      <q-card class="tw:mb-4 tw:bg-white tw:p-4 tw:rounded-xl">
        <div class="tw:mt-2 tw:grid tw:grid-cols-1 tw:tl:grid-cols-5 tw:gap-2">
        <!-- tabs item -->
        <div class="tw:flex tw:justify-center tw:tl:justify-start tw:tl:col-span-2 tw:max-w-full">
          <TabsItemNotifi
          />
        </div>
        <!-- pagination start -->
        <div class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0">
          <PaginationNotifi />
        </div>
      </div>

      <!-- start table for smartphone -->
      <div class="q-py-sm">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-1"
          card-class="tw:w-full"
          :rows="notificationListData"
          :pagination="paginationComputed"
          row-key="id"
          hide-pagination
          hide-header
        >
          <template v-slot:item="props">
            <div
              class="tw:w-full tw:max-w-full"
              @click.prevent="handleSelectInventory(props.row)"
            >
              <q-card flat bordered>
                <q-card-section class="tw:cursor-pointer tw:text-[#333333]">
                  <div
                    :class="`${
                      props.row.isRead ? 'tw:font-medium' : 'tw:font-bold '
                    }
                      tw:truncate tw:text-m-design tw:max-w-[90vw]
                     tw:flex tw:items-center`"
                  >
                    <q-badge
                    v-if="!props.row.isRead"
                    class="tw:text-xxs-design tw:font-normal tw:mr-2 tw:px-[0.8rem] tw:py-[0.3rem]
                    badgeColor"
                    >
                    New
                    </q-badge>
                    <p class="tw:truncate tw:m-0">{{ props.row.title }}</p>
                  </div>
                  <div>
                    <span
                      :class="`${props.row.isRead ? '' : 'tw:font-bold '}
                        tw:text-xs-design`"
                      >{{ props.row.dateSend }}</span
                    >
                  </div>
                  <p
                    class="tw:truncate tw:text-s-design tw:max-w-[80vw]"
                  >
                    {{ props.row.content }}
                  </p>
                </q-card-section>
              </q-card>
            </div>
          </template>
          <template v-slot:no-data="">
            <div class="tw:w-full tw:text-center tw:text-s-design">データが見つかりません。</div>
          </template>
        </q-table>
      </div>
      <!-- table end -->
      <!-- pagination start -->
      <div class="tw:tl:justify-end tw:flex tw:justify-center tw:my-2">
        <PaginationNotifi />
      </div>
      <!-- pagination end -->
      </q-card>
    </div>
    <!-- Footer Button -->
    <q-footer elevated class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
    tw:tl:justify-start tw:items-center tw:flex tw:justify-center tw:min-h-[91px] tw:tl:h-[6.5rem]">
      <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
      label="トップに戻る"
      @click.prevent="goToPage('home')"
    />
    </q-footer>
  </q-page>
</template>

<script setup>
// #region import
import TabsItemNotifi from 'components/TabsItemNotifi.vue';
import PaginationNotifi from 'components/PaginationNotifi.vue';
import notificationService from 'src/shared/services/notification.service';
import { NOTIFICATION_STATUS_ENUM } from 'helpers/constants';
import {
  computed, onMounted, ref, watch, provide,
} from 'vue';
import { useRouter } from 'vue-router';
import { FORMAT_DATE } from 'helpers/common';
import BaseButton from 'src/components/base/vs/BaseButton.vue';

// #endregion
// const { colorMain } = storeToRefs(useAppStore());

// #region state

const router = useRouter();
// for manage state of notification
const activeTab = ref(NOTIFICATION_STATUS_ENUM.ALL);
// pagination state
const pagination = ref({
  sortBy: '',
  descending: true,
  page: 1,
  limit: 10,
});
// total page default is 0
const totalPage = ref(0);
// page index default is 1
const pageIndex = ref(1);
// store data of notification list
const notificationListData = ref([]);
// check if page index is different from the value server return
const hasDiffPageIndex = ref(false);
// #endregion

// #region actions
// get notification list from server
const getNotificationList = async () => {
  const notificationList = (await notificationService.getNotificationList({
    ...pagination.value,
    status: activeTab.value,
  })) || [];

  if (notificationList.payload.items.length === 0 && pagination.value.page > 1) {
  router.replace({
    query: { tab: activeTab.value, ...pagination.value, page: '1' },
  });
  return;
}

  if (notificationList) {
    // map data to render in table

    notificationListData.value = notificationList.payload.items.map(item => ({
      ...item,
      dateSend: FORMAT_DATE(item.date_send),
      isRead: item.is_read,
    }));

    // calculate total page
    totalPage.value = Math.ceil(
      (notificationList.payload.total_item ?? 1) / pagination.value.limit
    );
    // check if page index is different from the value server return
    // if different, replace query to the new page index (value server return)
    if (notificationList.payload.page !== pageIndex.value) {
      hasDiffPageIndex.value = true;
      router.replace({
        query: { tab: activeTab.value, ...pagination.value, page: notificationList.payload.page },
      });
    }
  }
};

// handle select notification => redirect to notification detail
const handleSelectInventory = async row => {
  await router.push({
    name: 'notificationDetail',
    params: {
      id: row.id,
    },
  });
};

const goToPage = name => {
  router.push({ name });
};

const goHeader = () => {
  document.getElementById('header').scrollIntoView({ behavior: 'smooth' });
};
// #endregion

// #endregion

// #region watch
// watch change query params
// if query params change, get notification list
watch(
  () => router.currentRoute.value.query.tab
    + router.currentRoute.value.query.page
    + router.currentRoute.value.query.limit
    + router.currentRoute.value.query.sortBy
    + router.currentRoute.value.query.descending,
  async () => {
    activeTab.value = router.currentRoute.value.query.tab;
    pageIndex.value = +router.currentRoute.value.query.page;
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };

    // if hasDiffPageIndex is false, get notification list
    // if hasDiffPageIndex is true, do nothing because the query has been replaced
    if (!hasDiffPageIndex.value) {
      if (!router.currentRoute.value.query.tab) {
        activeTab.value = 'all';
      }
      await getNotificationList();
    } else {
      hasDiffPageIndex.value = false;
    }
  }
);
// #endregion

// #region computed
const paginationComputed = computed(() => ({
  page: 1,
  rowsPerPage: pagination.value.limit,
}));
// #endregion

// #region lifecycle
onMounted(async () => {
  // if query params tab or page is not exist, replace query to default value
  if (
    !router.currentRoute.value?.query?.tab
    || !router.currentRoute.value?.query?.page
  ) {
    router.replace({
      query: { tab: NOTIFICATION_STATUS_ENUM.ALL, ...pagination.value },
    });
  } else {
    // if query params tab or page is exist, get notification list
    // and set value for activeTab and pageIndex ...
    activeTab.value = router.currentRoute.value.query.tab;
    pageIndex.value = +router.currentRoute.value.query.page;
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
   await getNotificationList();

  }
});
// #endregion

// #region provide
const tabsItemProvideData = {
  activeTab,
  tabOptions: [
    {
      label: '全て',
      value: NOTIFICATION_STATUS_ENUM.ALL,
    },
    {
      label: '未読',
      value: NOTIFICATION_STATUS_ENUM.UNREAD,
    },
    {
      label: '既読',
      value: NOTIFICATION_STATUS_ENUM.READ,
    },
  ],
  handleClickTabItem(tab) {
       getNotificationList();
       router.push({ query: { tab: tab.value, ...pagination.value, page: 1 } });},
};
provide('tabsItemProvideData', tabsItemProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: { tab: activeTab.value, ...pagination.value },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);
// #endregion
</script>

<style scoped>
.badgeColor {
  background-color: #E80F00 !important;
  color: #ffffff !important;
}
</style>
