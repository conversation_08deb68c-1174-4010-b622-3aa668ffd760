const { MESSAGE } = require('../utils/message');
const {
  sharedSchema,
} = require('../validations');

const searchStaffSchema = {
  summary: 'Search staff',
  description: 'Search list staff',
  tags: ['Staff'],
  security: [{ 'bearToken': [] }],
  query: {
    type: 'object',
    additionalProperties: false,
    properties: {
      page: {
        type: 'integer',
        default: 1,
        errorMessage: {
          _: 'page index invalid',
        }
      },
      limit: {
        type: 'integer',
        default: 10,
        errorMessage: {
          _: 'page size invalid',
        }
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      name: {
        type: 'string',
        minLength: 0,
        maxLength: 50,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        }
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      },
      isMobile: {
        type: 'integer',
        default: 0,
        enum: [0, 1]
      }
    },
  },
  response: sharedSchema.response,
};

const registerStaffSchema = {
  summary: 'Register staff',
  description: 'Register new staff',
  tags: ['Staff'],
  security: [{ 'bearToken': [] }],
  required: ['name', 'password', 'passwordConfirm'],
  body: {
    type: 'object',
    additionalProperties: false,
    properties: {
      name: {
        type: 'string',
        minLength: 1,
        errorMessage: {
          _: 'name invalid'
        }
      },
      nameKana: {
        type: 'string',
        oneOf: [
          { minLength: 0, maxLength: 0 },
          {
            maxLength: 50,
            pattern: '^[ァ-ヴー　]+$',
          }
        ],
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_KANA_ERROR
        }
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      phone: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR
        }
      },
      password: {
        type: 'string',
        maxLength: 256,
        minLength: 1,
        pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
        errorMessage: {
          minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR
        }
      },
      passwordConfirm: {
        type: 'string',
        minLength: 1,
        const: {
          $data: '1/password'
        },
        errorMessage: {
          minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          _: MESSAGE.MSG_NOTMATCH_PASSWARD_ERROR
        }
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      }
    }
  },
  response: sharedSchema.response,
};

const editStaffSchema = {
  summary: 'Edit staff',
  description: 'Edit staff',
  tags: ['Staff'],
  security: [{ 'bearToken': [] }],
  body: {
    additionalProperties: false,
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 1,
        errorMessage: {
          _: 'name invalid'
        }
      },
      nameKana: {
        type: 'string',
        oneOf: [
          { minLength: 0, maxLength: 0 },
          {
            maxLength: 50,
            pattern: '^[ァ-ヴー　]+$',
          }
        ],
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_KANA_ERROR
        }
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      phone: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR
        }
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      }
    }
  },
  response: sharedSchema.response,
};

const deleteStaffSchema = {
  summary: 'Delete staff',
  description: 'Delete staff',
  tags: ['Staff'],
  security: [{ 'bearToken': [] }],
  response: sharedSchema.response,
}

const getStaffSchema = {
  summary: 'Get staff',
  description: 'Get staff',
  tags: ['Staff'],
  security: [{ 'bearToken': [] }],
  response: sharedSchema.response,
}


module.exports = {
  searchStaffSchema,
  registerStaffSchema,
  editStaffSchema,
  deleteStaffSchema,
  getStaffSchema
}