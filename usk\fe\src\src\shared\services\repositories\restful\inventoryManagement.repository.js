import BaseRepository from './base.repository';

class InventoryManagementRepository extends BaseRepository {
  constructor() {
    super('inventory-management');
  }
  getInventoryList(query) {
    return this.client.get('/list', query);
  }

  getInventoryEditedList(query) {
    return this.client.get('/edited-list', query);
  }

  getInventoryDetail(id) {
    return this.client.get(`/${id}`);
  }

  getInventoryEditedDetail(id) {
    return this.client.get(`/edited/${id}`);
  }

  editInventory(id, data) {
    return this.client.put(`/${id}`, data);
  }

  undoChangeInventory(id) {
    return this.client.put(`/undo-change/${id}`);
  }

  resetInventory(id) {
    return this.client.put(`/reset/${id}`);
  }

  getTotalWeightInventory() {
    return this.client.get('/total-weight-inventory');
  }
}

export default new InventoryManagementRepository();
