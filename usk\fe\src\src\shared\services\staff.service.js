import BaseService from 'services/base.service';

class StaffService extends BaseService {
  async getStaff(query) {
    try {
      return this.dao.getStaff(query);
    } catch (error) {
      return null;
    }
  }

  async registerStaff(body) {
    try {
      return this.dao.registerStaff(body);
    } catch (error) {
      return null;
    }
  }

  async editStaff(id, body) {
    try {
      return this.dao.editStaff(id, body);
    } catch (error) {
      return null;
    }
  }

  async delStaff(id) {
    try {
      return this.dao.delStaff(id);
    } catch (error) {
      return null;
    }
  }

  async getOneStaff(id) {
    try {
      return this.dao.getOneStaff(id);
    } catch (error) {
      return null;
    }
  }
}

export default new StaffService('staff');
