<template>
  <q-table
    :table-header-class="`table-header tw:bg-${colorMain} tw:text-[white]`"
    hide-pagination
  >
    <template v-slot:no-data="">
      <div class="tw:w-full tw:text-center tw:font-normal tw:text-[1rem] tw:leading-[1.5rem]
      tw:tl:text-[1.53rem] tw:tl:leading-[2.31rem]">データが見つかりません。</div>
    </template>
    <template v-for="(_, name) in $slots" v-slot:[name]="props" :key="name">
      <slot :name="name" :props="props" />
    </template>
  </q-table>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';

const { colorMain } = storeToRefs(useAppStore());
</script>

<style scoped>
:deep(.table-header th) {
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 2.5rem;
}
:deep(tbody tr:hover) {
  background-color: #e2e8f0;
}

:deep(tr td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1rem;
}

@media (min-width: 960px) and (max-width: 1439px) {
  :deep(tr td) {
    font-size: 1.53rem;
  }
}

</style>
