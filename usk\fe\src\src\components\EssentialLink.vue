<template>
  <div v-for="item in items"
    :key="item"
    :class="`${item.borderTop ? 'tw:border-t tw:border-gray' : ''}
  ${item.borderBot ? 'tw:border-b tw:border-gray' : ''}
  ${item.marginTop ? 'tw:pt-6' : ''}
  ${item.marginBot ? 'tw:pb-6' : ''}
  ${item.class || ''}
  ${
    !item?.role
      ? ''
      : CHECK_ROLE(item.role, item.type, item.staff, user) &&
        [undefined, true].includes(item.subCond)
      ? ' '
      : ' tw:hidden'
  }`">
      <q-item
    @click.prevent="pushRoute(item)"
    clickable
  >
    <q-item-section v-if="item.icon" avatar>
      <q-icon :name="item.icon" class="tw:text-[2.25rem] tw:fill-[#7E8093]" />
    </q-item-section>
    <div v-else class="tw:pr-3 tw:flex tw:items-center">
      <img
        v-if="item.svg"
        :src="item.svg"
        class="tw:w-[2.25rem] tw:h-[2.25rem] tw:fill-[#7E8093]"
      />
    </div>

    <q-item-section class="tw:flex-row tw:justify-between tw:items-center">
      <q-item-label class="tw:text-m-design">{{ item.title }}</q-item-label>
      <span
        v-if="item.noticeFlag"
        class="tw:w-[10px] tw:h-[10px] tw:mr-4 tw:rounded-full tw:bg-red-2"
      />
    </q-item-section>
  </q-item>
  </div>
</template>

<script setup>
import { inject } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { CHECK_ROLE } from 'helpers/common';
import { useAuthStore } from 'src/stores/auth-store';

const items = inject('items');
const router = useRouter();
const { role } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const downloadPDF = async (url, filename = 'download.pdf') => {
  const link = document.createElement('a');
  link.href = url;
  link.target = '_blank';
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const pushRoute = async item => {
  if (!item || typeof item !== 'object') {
    return;
  }

  const { external, link, filename = 'ユーザーマニュアル.pdf' } = item;

  if (external && link) {
    await downloadPDF(link, filename);
  } else if (link) {
    if (router.currentRoute.value.name === link) {
      window.location.href = router.resolve({ name: link }).href;
    } else {
      router.push({ name: link });
    }
  }
};
</script>
