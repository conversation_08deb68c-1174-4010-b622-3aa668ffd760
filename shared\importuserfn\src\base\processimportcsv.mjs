import ShortUniqueId from 'short-unique-id';
import { sprintf } from 'sprintf-js';
import { MESSASES } from './messages.mjs';
import dayjs from './dayjs.mjs';
import { ApplicationException } from './errors.mjs';

export const checkValidProvinceName = async (trx, data) => {
  const { province_name: provinceName, line_number: lineNumber } = data;
  if (!provinceName) return;

  const province = await trx
    .table('provinces')
    .first('id')
    .where('name', provinceName.trim());

  if (!province) {
    throw new ApplicationException(
      sprintf(MESSASES.MSG_INVARID_ADDRESSFORMAT_ERROR, lineNumber)
    );
  }
  data.province_id = province.id;
};

const makePassword = (length) => {
  // charset: A-Z, a-z, 0-9 (except O, l, 1, 0)
  const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789';
  let fpwd = '';
  const lpwd = length;

  for (;;) {
    const rnd = Math.floor(Math.random() * charset.length);
    fpwd += charset[rnd];
    if (fpwd.length == lpwd) {
      if (
        fpwd.match(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[A-Za-z0-9]{8,256}$/g)
      ) {
        break;
      } else {
        fpwd = '';
      }
    }
  }

  return fpwd;
};
const checkEnterpriseByStaff = async (
  trx,
  line,
  enterpriseCode,
  enterpriseType
) => {
  const enterprise = await trx
    .table('enterprises')
    .first('enterprise_code')
    .where({
      enterprise_code: enterpriseCode,
      type: +enterpriseType,
    });

  if (!enterprise) {
    throw new ApplicationException(
      sprintf(MESSASES.MSG_INVARID_NOTIFICATIONUMBER_CHANGE_ERROR, line)
    );
  }
};

const getEnterpriseByCorp = async (
  trx,
  line,
  enterpriseCode,
  enterpriseType
) => {
  const enterprise = await trx
    .table('enterprises')
    .first('enterprise_code')
    .where({
      enterprise_code: enterpriseCode,
      type: +enterpriseType,
    });
  if (!enterprise) {
    throw new ApplicationException(
      sprintf(MESSASES.MSG_INVARID_NOTIFICATIONUMBER_CHANGE_ERROR, line)
    );
  }
  if (enterprise) {
    const user = await trx
      .table('users')
      .first('*')
      .where('user_code', enterpriseCode.concat('-0000'));
    return user;
  }
  return null;
};

const getUserCode = async (trx, enterpriseId, enterpriseCode) => {
  const maxUserCode = await trx
    .table('users')
    .first({
      maxDigit: trx.raw('coalesce(max(right(user_code,4))::int + 1,0)'),
    })
    .where('enterprise_id', enterpriseId);
  const maxCode = maxUserCode.maxDigit;
  return enterpriseCode.concat('-', maxCode.toString().padStart(4, '0'));
};

const insertUserIdByCorp = async (trx, data) => {
  const {
    line_number: line,
    enterprise_code: enterpriseCode,
    license_number: licenseNumber,
    name,
    name_kana: nameKana,
    province_id: provinceId,
    phone,
    expiry_date_start: expiryDateStart,
    expiry_date: expiryDate,
    status: status,
    note_1: note1,
    note_2: note2,
    province_custom_data,
    staff_type: staffType,
    enterprise_type: enterpriseType,
  } = data;
  const dateTimeNow = dayjs().toDate();

  if ([0, 5].includes(+enterpriseType) && +staffType === 0) {
    if (!enterpriseCode) {
      throw new ApplicationException(
        sprintf(MESSASES.MSG_LICENSE_NUMBER_REQUIRED, line, 'enterprise_code')
      );
    }
  }

  if (enterpriseCode && [0, 5].includes(+enterpriseType)) {
    const checkEnterprise = await trx.table('enterprises').first('id').where({
      enterprise_code: enterpriseCode,
      type: +enterpriseType,
    });
    if (checkEnterprise) {
      throw new ApplicationException(
        sprintf(MESSASES.MSG_USED_NOTIFICATIONUMBER_ERROR, line)
      );
    }
  }

  let enterprise = null;
  const enterpriseNew = await trx
    .table('enterprises')
    .insert({
      created_on: dateTimeNow,
      latest_updated_on: dateTimeNow,
      enterprise_code:
        !enterpriseCode && +enterpriseType === 9 ? '' : enterpriseCode,
      enterprise_name: name,
      enterprise_name_kana: nameKana || null,
      enterprise_name_nospace: name.replace(/\s+/g, ''),
      enterprise_name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
      type: +enterpriseType,
    })
    .returning('id');
  enterprise = enterpriseNew[0];
  // const userCode = await getUserCode(trx, enterprise?.id, enterpriseCode);
  const dataInsert = {
    created_on: dateTimeNow,
    latest_updated_on: dateTimeNow,
    enterprise_id: enterprise?.id,
    user_code: enterpriseCode.concat('-0000'),
    name,
    name_kana: nameKana || null,
    phone,
    password: makePassword(8),
    role: '00',
    enterprise_type: +enterpriseType,
    staff_type: staffType,
    statistics_date_from: enterpriseCode.startsWith('5')
      ? dayjs().toDate()
      : null,
    note_1: note1,
    note_2: note2,
    license_number:
      +enterpriseType === 0 || +enterpriseType === 9 ? licenseNumber : null,
    name_nospace: name.replace(/\s+/g, ''),
    name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
    status: status,
    province_id: provinceId,
    province_custom_data: province_custom_data
      ? JSON.stringify(province_custom_data)
      : null,
  };
  if (+enterpriseType === 9) {
    delete dataInsert.user_code;
  }
  const newUser = await trx.table('users').insert(dataInsert).returning('id');

  await trx.table('settings').insert({
    created_on: dateTimeNow,
    latest_updated_on: dateTimeNow,
    user_id: newUser[0]?.id,
    unit_per_gram: 0.2,
    price_per_kilogram: [200],
    display_shipment_weight: true,
    display_actual_received: true,
    delete_flag: false,
    qr_scan_init: 1,
    enable_session_timeout: true,
    price_per_quantity: [400],
  });

  const licenseCode = new ShortUniqueId({
    length: 16,
    dictionary: 'alphanum',
  }).randomUUID();

  const license = await trx
    .table('licenses')
    .insert({
      created_on: dateTimeNow,
      latest_updated_on: dateTimeNow,
      license_code: licenseCode,
      user_id: newUser[0]?.id,
      expiry_date_start:
        (+enterpriseType === 0 || +enterpriseType === 9) && expiryDateStart
          ? dayjs.getDateFromJST(`${expiryDateStart} 00:00:00`).toDate()
          : null,
      expiry_date:
        (+enterpriseType === 0 || +enterpriseType === 9) && expiryDate
          ? dayjs.getDateFromJST(`${expiryDate} 23:59:59`).toDate()
          : null,
    })
    .returning('id');

  const user = await trx
    .table('users')
    .update({
      license_id: license[0].id,
    })
    .where('id', newUser[0].id)
    .returning(['user_code', 'password']);

  return {
    is_updated: 0,
    ...data,
    user_code: user[0].user_code,
    license_code: licenseCode,
    init_password: user[0].password,
  };
};

const insertUserIdByStaff = async (trx, data) => {
  const {
    line_number: line,
    enterprise_code: enterpriseCode,
    license_number: licenseNumber,
    name,
    name_kana: nameKana,
    province_id: provinceId,
    phone,
    expiry_date_start: expiryDateStart,
    expiry_date: expiryDate,
    status: status,
    note_1: note1,
    note_2: note2,
    province_custom_data,
    staff_type: staffType,
    enterprise_type: enterpriseType,
  } = data;
  const dateTimeNow = dayjs().toDate();

  const enterprise = await trx.table('enterprises').first('id').where({
    enterprise_code: enterpriseCode,
    type: +enterpriseType,
  });
  if (!enterprise) {
    throw new ApplicationException(
      sprintf(
        MESSASES.MSG_INVARID_CREATEUSER_NOTWORKER_ERROR,
        line,
        'enterprise_code'
      )
    );
  }
  const userCode = await getUserCode(trx, enterprise?.id, enterpriseCode);
  const newUser = await trx
    .table('users')
    .insert({
      created_on: dateTimeNow,
      latest_updated_on: dateTimeNow,
      enterprise_id: enterprise?.id,
      user_code: userCode,
      name,
      name_kana: nameKana || null,
      phone,
      password: makePassword(8),
      role: '00',
      enterprise_type: enterpriseType,
      staff_type: staffType,
      statistics_date_from: enterpriseCode.startsWith('5')
        ? dayjs().toDate()
        : null,
      note_1: note1,
      note_2: note2,
      license_number:
        +enterpriseType === 0 || +enterpriseType === 9 ? licenseNumber : null,
      name_nospace: name.replace(/\s+/g, ''),
      name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
      status: status,
      province_id: provinceId,
      province_custom_data: province_custom_data
        ? JSON.stringify(province_custom_data)
        : null,
    })
    .returning('id');

  await trx.table('settings').insert({
    created_on: dateTimeNow,
    latest_updated_on: dateTimeNow,
    user_id: newUser[0]?.id,
    unit_per_gram: 0.2,
    price_per_kilogram: [200],
    display_shipment_weight: true,
    display_actual_received: true,
    delete_flag: false,
    qr_scan_init: 1,
    enable_session_timeout: true,
    price_per_quantity: [400],
  });

  const licenseCode = new ShortUniqueId({
    length: 16,
    dictionary: 'alphanum',
  }).randomUUID();

  const license = await trx
    .table('licenses')
    .insert({
      created_on: dateTimeNow,
      latest_updated_on: dateTimeNow,
      license_code: licenseCode,
      user_id: newUser[0]?.id,
      expiry_date_start:
        (+enterpriseType === 0 || +enterpriseType === 9) && expiryDateStart
          ? dayjs.getDateFromJST(`${expiryDateStart} 00:00:00`).toDate()
          : null,
      expiry_date:
        (+enterpriseType === 0 || +enterpriseType === 9) && expiryDate
          ? dayjs.getDateFromJST(`${expiryDate} 23:59:59`).toDate()
          : null,
    })
    .returning('id');

  const user = await trx
    .table('users')
    .update({
      license_id: license[0].id,
    })
    .where('id', newUser[0].id)
    .returning(['user_code', 'password']);

  return {
    is_updated: 0,
    ...data,
    user_code: user[0].user_code,
    license_code: licenseCode,
    init_password: user[0].password,
  };
};

const updateUser = async (trx, data, user) => {
  const {
    line_number: line,
    enterprise_type: enterpriseType,
    staff_type: staffType,
    enterprise_code: enterpriseCode,
    user_code: userCode,
    license_number: licenseNumber,
    name,
    name_kana: nameKana,
    province_id: provinceId,
    phone,
    expiry_date_start: expiryDateStart,
    expiry_date: expiryDate,
    status: status,
    note_1: note1,
    note_2: note2,
    account_registration_status: accountRegistrationStatus,
    province_custom_data,
  } = data;

  const dateTimeNow = dayjs().toDate();
  let licenseId;
  let licenseCode;

  let isChanged = 0;
  const userInDB = await trx.table('users').first('*').where('id', user.id);
  const enterpriseInDB = await trx
    .table('enterprises')
    .first('enterprise_code')
    .where('id', user.enterprise_id);

  if (
    +user.enterprise_type !== 9 &&
    enterpriseInDB.enterprise_code !== enterpriseCode
  ) {
    throw new ApplicationException(
      sprintf(
        MESSASES.MSG_INVARID_NOTIFICATIONUMBER_CHANGE_ERROR,
        line,
        'enterprise_code'
      )
    );
  }

  if (
    enterpriseInDB.enterprise_code !== enterpriseCode ||
    userInDB.name !== name ||
    userInDB.name_kana !== (nameKana || null)
  ) {
    isChanged = 1;
  }

  let initPassword = null;
  if (
    +userInDB.account_registration_status === 1 &&
    +accountRegistrationStatus === 0
  ) {
    initPassword = makePassword(8);
  } else if (
    +userInDB.account_registration_status === 0 &&
    +accountRegistrationStatus === 1
  ) {
    throw new ApplicationException(
      sprintf(
        MESSASES.MSG_INVALID_ACCOUNT_REGISTRATION_STATUS_CHANGE_ERROR,
        userInDB.user_code,
        line
      )
    );
  }

  if (user.user_code?.endsWith('0000')) {
    let enterprise = null;
    let id = null;
    if (+enterpriseType === 9) {
      enterprise = await trx
        .table('users')
        .first('enterprise_id')
        .where('user_code', userCode);
      id = enterprise?.enterprise_id;
    } else {
      enterprise = await trx.table('enterprises').first('id').where({
        enterprise_code: enterpriseCode,
        type: +enterpriseType,
      });
      id = enterprise?.id;
    }
    await trx
      .table('enterprises')
      .update({
        latest_updated_on: dateTimeNow,
        enterprise_code:
          !enterpriseCode && +enterpriseType === 9 ? '' : enterpriseCode,
        enterprise_name: name,
        enterprise_name_kana: nameKana || null,
        enterprise_name_nospace: name.replace(/\s+/g, ''),
        enterprise_name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
      })
      .where('id', id);
  }

  const isCatch = +enterpriseType === 0;
  const isEel = +enterpriseType === 9;
  const isDistribute = +enterpriseType === 5;

  const licenseCheck = await trx
    .table('licenses')
    .first(['id', 'expiry_date', 'expiry_date_start', 'license_code'])
    .where('id', user.license_id);

  licenseCode = licenseCheck?.license_code || null;

  const hasLicenseDateChanged =
    (expiryDate || expiryDateStart) &&
    (!dayjs(licenseCheck?.expiry_date).isSame(
      dayjs.getDateFromJST(`${expiryDate} 23:59:59`).toDate(),
      'day'
    ) ||
      !dayjs(licenseCheck?.expiry_date_start).isSame(
        dayjs.getDateFromJST(`${expiryDateStart} 00:00:00`).toDate(),
        'day'
      ));

  if (hasLicenseDateChanged) {
    if (isCatch) {
      // CATCH: always create new license and soft-delete old one
      licenseCode = new ShortUniqueId({
        length: 16,
        dictionary: 'alphanum',
      }).randomUUID();

      if (licenseCheck?.id) {
        await trx
          .table('licenses')
          .update({
            delete_flag: true,
            latest_updated_on: dateTimeNow,
          })
          .where('id', licenseCheck.id);
      }

      const newLicense = await trx
        .table('licenses')
        .insert({
          created_on: dateTimeNow,
          latest_updated_on: dateTimeNow,
          license_code: licenseCode,
          user_id: user.id,
          expiry_date_start: dayjs
            .getDateFromJST(`${expiryDateStart} 00:00:00`)
            .toDate(),
          expiry_date: dayjs.getDateFromJST(`${expiryDate} 23:59:59`).toDate(),
        })
        .returning('id');

      licenseId = newLicense[0]?.id;
    }

    if (isEel) {
      if (licenseCheck?.id) {
        // EEL: update existing license
        await trx
          .table('licenses')
          .update({
            expiry_date_start: dayjs
              .getDateFromJST(`${expiryDateStart} 00:00:00`)
              .toDate(),
            expiry_date: dayjs
              .getDateFromJST(`${expiryDate} 23:59:59`)
              .toDate(),
            latest_updated_on: dateTimeNow,
          })
          .where('id', licenseCheck.id);

        licenseId = licenseCheck.id;
      } else {
        // EEL: create new license if none exists
        licenseCode = new ShortUniqueId({
          length: 16,
          dictionary: 'alphanum',
        }).randomUUID();

        const newLicense = await trx
          .table('licenses')
          .insert({
            created_on: dateTimeNow,
            latest_updated_on: dateTimeNow,
            license_code: licenseCode,
            user_id: user.id,
            expiry_date_start: dayjs
              .getDateFromJST(`${expiryDateStart} 00:00:00`)
              .toDate(),
            expiry_date: dayjs
              .getDateFromJST(`${expiryDate} 23:59:59`)
              .toDate(),
          })
          .returning('id');

        licenseId = newLicense[0]?.id;
      }
    }
  } else if (!expiryDate && !expiryDateStart) {
    if (isCatch) {
      // CATCH: if dates are empty, delete existing license
      await trx
        .table('licenses')
        .update({
          delete_flag: true,
          latest_updated_on: dateTimeNow,
        })
        .where('id', licenseCheck?.id);
      licenseId = null;
    }

    if (isEel) {
      // EEL: keep existing license, do nothing
      if (licenseCheck?.id) {
        await trx
          .table('licenses')
          .update({
            expiry_date_start: null,
            expiry_date: null,
            latest_updated_on: dateTimeNow,
          })
          .where('id', licenseCheck?.id);
      }
      licenseId = licenseCheck?.id || null;
    }
    if (isDistribute) {
      licenseId = licenseCheck?.id || null;
    }
  }
   const userUp = await trx
    .table('users')
    .update({
      license_id: licenseId,
      latest_updated_on: dateTimeNow,
      name,
      name_kana: nameKana || null,
      phone,
      status,
      password: initPassword ? initPassword : userInDB.password,
      note_1: note1,
      note_2: note2,
      account_registration_status: accountRegistrationStatus,
      license_number:
        +enterpriseType === 0 || +enterpriseType === 9 ? licenseNumber : null,
      name_nospace: name.replace(/\s+/g, ''),
      name_kana_nospace: nameKana?.replace(/\s+/g, '') || null,
      province_id: provinceId,
      province_custom_data: province_custom_data
        ? JSON.stringify(province_custom_data)
        : null,
    })
    .where('id', user.id)
    .returning('*');

    if (+staffType === 0 ) {
      await trx.table('users').update({
        status,
      }).where('enterprise_id', user.enterprise_id);
    }
  return {
    is_updated: 1,
    is_changed: isChanged,
    ...data,
    user_code: user.user_code,
    license_code: licenseCode,
    init_password:
      +userUp[0].account_registration_status === 0 ? userUp[0].password : null,
  };
};

const upSertUserIdByCorp = async (trx, data) => {
  const {
    enterprise_code: enterpriseCode,
    user_code: userCode,
    enterprise_type: enterpriseType,
    line_number: lineNumber,
  } = data;
  if (!userCode) {
    return await insertUserIdByCorp(trx, data);
  } else {
    let user = null;
    if (+enterpriseType !== 9) {
      user = await getEnterpriseByCorp(
        trx,
        lineNumber,
        enterpriseCode,
        enterpriseType
      );
      if (userCode !== user.user_code) {
        throw new ApplicationException(
          sprintf(MESSASES.USER_CODE_NOT_EXITS, lineNumber)
        );
      }
    } else {
      user = await trx.table('users').first('*').where('user_code', userCode);
      if (!user) {
        throw new ApplicationException(
          sprintf(MESSASES.USER_CODE_NOT_EXITS, lineNumber)
        );
      }
    }
    return await updateUser(trx, data, user);
  }
};

const upSertUserIdByStaff = async (trx, data) => {
  const {
    line_number: lineNumber,
    user_code: userCode,
    enterprise_code: enterpriseCode,
    enterprise_type: enterpriseType,
  } = data;
  if (!userCode) {
    return await insertUserIdByStaff(trx, data);
  } else {
    await checkEnterpriseByStaff(trx, lineNumber, enterpriseCode, enterpriseType);
    const user = await trx
      .table('users')
      .first('*')
      .where('user_code', userCode);
    if (!user) {
      throw new ApplicationException(
        sprintf(MESSASES.USER_CODE_NOT_EXITS, lineNumber)
      );
    }
    return await updateUser(trx, data, user);
  }
};

export const processImportCSV = async (trx, data) => {
  try {
    const csvHeader = [
      'urlcardid',
      'user_code',
      'init_password',
      'enterprise_type',
      'enterprise_code',
      'staff_type',
      'license_number',
      'name',
      'name_kana',
      'province_name',
      'phone',
      'expiry_date_start',
      'expiry_date',
      'status',
      'account_registration_status',
      'note_1',
      'note_2',
    ];
    if (data.enterprise_code) {
      data.enterprise_code = String(data.enterprise_code || '')
        .replace(/\D/g, '')
        .padStart(7, '0');
    }
    const {
      staff_type: staffType,
      enterprise_code: enterpriseCode,
      enterprise_type: enterpriseType,
    } = data;
    const province_custom_data = {};
    let isExtra = false;
    Object.keys(data).forEach((key) => {
      if (!csvHeader.includes(key) && key !== 'line_number') {
        if (typeof data[key] === 'string') {
          data[key] = data[key].replace(/\r\n|\r|\n/g, ';');
        }
        if (![undefined].includes(data[key])) {
          if (enterpriseCode.startsWith('0') || data[key] !== '') {
            province_custom_data[key] = data[key];
          }
        }

        if (['__parsed_extra', ''].includes(key)) {
          isExtra = true;
        }
        delete data[key];
      }
    });
    data.province_custom_data = province_custom_data;

    if (!Object.keys(province_custom_data).length) {
      data.province_custom_data = null;
    }

    await checkValidProvinceName(trx, data);

    if ((+enterpriseType !== 0 && data.province_custom_data) || isExtra) {
      throw new ApplicationException(
        sprintf(MESSASES.MSG_INVARID_ITEM_WITHOUTSAIHO_ERROR, data.line_number)
      );
    }
    if (staffType === '0') {
      return await upSertUserIdByCorp(trx, data);
    } else {
      return await upSertUserIdByStaff(trx, data);
    }
  } catch (ex) {
    console.log(ex);
    if (!(ex instanceof ApplicationException)) {
      throw new ApplicationException(
        sprintf(MESSASES.IMPORT_USER_NOK, data.line_number)
      );
    } else {
      throw ex;
    }
  }
};
