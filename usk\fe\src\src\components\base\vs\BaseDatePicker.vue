<template>
  <q-input
    v-model="model"
    maxlength="256"
    mask="date"
    ref="inputRef"
    bg-color="white"
    outlined
    hide-bottom-space
    class="vs icon tw:h-[4.5rem]"
    input-class="tw:text-l-design tw:dt:h-[4.5rem]"
    autocomplete="off"
    no-error-icon
  >
    <template v-slot:append>
      <div :style="{height: computedFontSize, width: computedFontSize}"
      class="tw:flex tw:justify-center tw:items-center tw:text-white tw:bg-[#004AB9]">
        <q-icon name="calendar_month" class="cursor-pointer" size="2.5rem">
          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
            <q-date v-model="model" :color="colorMain">
              <div class="row items-center justify-end">
                <q-btn
                  v-close-popup
                  label="閉じる"
                  flat
                  :class="`tw:bg-[#004AB9]`"
                  class="tw:text-white"
                />
              </div>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </div>
    </template>
  </q-input>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';

const { colorMain, colorSub } = storeToRefs(useAppStore());
const inputRef = ref(null);
const computedFontSize = ref('16px');
const model = defineModel();

onMounted(() => {
  const el = inputRef.value?.$el?.querySelector('.q-field__control');

  if (el) {
    const px = parseFloat(getComputedStyle(el).height);
    const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
    computedFontSize.value = `${px/rootFontSize}rem`;
  }
});
</script>
<style scoped>
  :deep(.q-date__content) {
    width: 100%;
  }

  q-field__control{
  height: 4.25rem !important;
  min-height: 4.25rem !important;
  max-width: 4.25rem !important;
}
</style>
