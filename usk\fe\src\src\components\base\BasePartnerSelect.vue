<template>
  <BaseSingleSelectInput
    v-model="model"
    :options="options"
    emit-value
    map-options
    @filter="filterPartner"
  >
    <template v-slot:option="scope">
      <q-item v-bind="scope.props?.itemProps">
        <q-item-section>
          <q-item-label class="tw:text-[18px] tw:tl:text-[2rem]">{{
            scope.props?.opt?.label
          }}</q-item-label>
          <q-item-label caption class="tw:text-[#334155] tw:tl:text-[1.2rem]">{{
            scope.props?.opt?.description
          }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </BaseSingleSelectInput>
</template>

<script setup>
import { inject, ref, watch } from 'vue';

import BaseSingleSelectInput from './BaseSingleSelectInput.vue';

const options = ref([]);
const listPartner = ref([]);

const model = defineModel();
const provideData = inject('basePartnerSelectProvideData');
const filterPartner = (val, update) => {
  if (val === '') {
    update(() => {
      options.value = listPartner.value;
    });
    return;
  }
  update(() => {
    const needle = val.toLowerCase();
    options.value = listPartner.value.filter(
      item => item.label.toLowerCase().indexOf(needle) > -1
    );
  });
};

watch(
  () => provideData.listPartner.value,
  val => {
    listPartner.value = val;
    options.value = val;
  }
);
</script>
<style scoped>
:deep(.q-field__native span) {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
}

@media (min-width: 960px) {
  :deep(.q-field__native span) {
    font-size: 2rem;
    line-height: 2.2rem;
  }
}
</style>
