<template>
  <div class="tw:my-4">
    <!-- user name -->
    <div
      class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
    >
      氏名<span class="tw:text-red">*</span>
    </div>
    <div class="tw:mt-1">
      <q-input
        outlined
        v-model.trim="formRegister.name"
        autocomplete="off"
        lazy-rules
        :error="!!errors.name"
        :error-message="errors.name"
        no-error-icon
        maxlength="50"
      />
    </div>
    <!-- name kana -->
    <div
      class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
    >
      氏名（カナ）
    </div>
    <div class="tw:mt-1">
      <q-input
        outlined
        v-model.trim="formRegister.nameKana"
        autocomplete="off"
        maxlength="50"
        lazy-rules
        :error="!!errors.nameKana"
        :error-message="errors.nameKana"
        no-error-icon
      />
    </div>
    <!-- license -->
    <div
      v-if="
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          user
        )
      "
    >
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        許可番号
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        type="string"
        v-model.trim="formRegister.licenseNumber"
        autocomplete="off"
        maxlength="256"
        lazy-rules
        :error="!!errors.licenseNumber"
        :error-message="errors.licenseNumber"
        no-error-icon
      />
    </div>
    <!-- phone number -->
    <div
      class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
    >
      携帯電話番号
    </div>
    <q-input
      class="tw:mt-1"
      outlined
      type="tel"
      v-model="formRegister.phone"
      autocomplete="off"
      lazy-rules
      maxlength="256"
      :error="!!errors.phone"
      :error-message="errors.phone"
      no-error-icon
    />
    <!-- password -->
    <div
      class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
    >
      パスワード<span class="tw:text-red">*</span>
    </div>
    <q-input
      class="tw:mt-1"
      outlined
      v-model="formRegister.password"
      autocomplete="new-password"
      maxlength="256"
      :type="showPassword ? 'text' : 'password'"
      lazy-rules
      :error="!!errors.password"
      :error-message="errors.password"
      no-error-icon
    >
      <template v-slot:append>
        <q-icon
          :name="showPassword ? 'visibility' : 'visibility_off'"
          @click.prevent="showPassword = !showPassword"
        />
      </template>
    </q-input>
    <!-- confirm password -->
    <div
      class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
    >
      パスワード（確認用）<span class="tw:text-red">*</span>
    </div>
    <q-input
      class="tw:mt-1"
      outlined
      v-model="formRegister.passwordConfirm"
      autocomplete="new-password"
      maxlength="256"
      :type="showPasswordConfirm ? 'text' : 'password'"
      lazy-rules
      :error="!!errors.passwordConfirm"
      :error-message="errors.passwordConfirm"
      no-error-icon
    >
      <template v-slot:append>
        <q-icon
          :name="showPasswordConfirm ? 'visibility' : 'visibility_off'"
          @click.prevent="showPasswordConfirm = !showPasswordConfirm"
        />
      </template>
    </q-input>

    <!-- note 1 -->
    <div
      v-if="
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          user
        )
      "
    >
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        備考１
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model.trim="formRegister.note1"
        autocomplete="off"
        lazy-rules
        maxlength="256"
        :error="!!errors.note1"
        :error-message="errors.note1"
        no-error-icon
      />
    </div>

    <!-- note 2 -->
    <div
      v-if="
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          user
        )
      "
    >
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        備考２
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model.trim="formRegister.note2"
        autocomplete="off"
        lazy-rules
        maxlength="256"
        :error="!!errors.note2"
        :error-message="errors.note2"
        no-error-icon
      />
    </div>

    <div class="tw:mt-4 tw:flex tw:flex-col tw:gap-2">
      <q-btn
        class="tw:w-full tw:text-white tw:flex tw:justify-center tw:items-center
        tw:mr-4 tw:font-[700] tw:tl:leading-[2.3rem] tw:tl:text-[1.53rem] tw:border tw:leading-[1.5rem] tw:text-[1rem] tw:rounded-lg"
        :class="`tw:bg-${colorMain}`"
        label="確認する"
        @click.prevent="pushToConfirm"
      />
    </div>
    <PopupConfirmItems />
  </div>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { provide, ref } from 'vue';
import { useRouter } from 'vue-router';
import PopupConfirmItems from 'components/PopupConfirmItems.vue';
import staffService from 'services/staff.service';
import toast from 'utilities/toast';
import registerStaff from 'schemas/registerStaff';
import useValidate from 'composables/validate';
import { useAuthStore } from 'stores/auth-store';
import {
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import { CHECK_ROLE } from 'src/helpers/common';

const { colorMain } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const showPassword = ref(false);
const showPasswordConfirm = ref(false);

const formRegister = ref({
  name: '',
  nameKana: '',
  licenseNumber: '',
  phone: '',
  password: '',
  passwordConfirm: '',
  note1: '',
  note2: '',
});
const { errors, validateData } = useValidate();

// method
const confirmFunc = async () => {
  const result = await staffService.registerStaff(formRegister.value);
  if (result.code === 0) {
    toast.access(result.payload.message);
    await router.push({ name: 'employeeList' });
  }
};

const popupConfirmItems = ref({
  isPopup: false,
  titlePopup: '以下の内容で、従事者を登録します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 98,
  minWidthTlDefault: 140,
  minWidthDtDefault: 212,
});

const pushToConfirm = async () => {
  const valid = validateData(registerStaff, formRegister.value);
  if (!valid) {
    return;
  }
  if (
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
      [STAFF_TYPE_ENUM.ENTERPRISE],
      user.value
    )
  ) {
    popupConfirmItems.value.listItems = [
      { key: '氏名', value: formRegister.value.name },
      { key: '氏名（カナ）', value: formRegister.value.nameKana },
      { key: '許可番号', value: formRegister.value.licenseNumber },
      { key: '携帯電話番号', value: formRegister.value.phone },
      { key: 'パスワード', value: '**********' },
      { key: '備考１', value: formRegister.value.note1 },
      { key: '備考２', value: formRegister.value.note2 },
    ];
  } else {
    popupConfirmItems.value.listItems = [
      { key: '氏名', value: formRegister.value.name },
      { key: '氏名（カナ）', value: formRegister.value.nameKana },
      { key: '携帯電話番号', value: formRegister.value.phone },
      { key: 'パスワード', value: '**********' },
    ];
  }
  popupConfirmItems.value.isPopup = true;
};
// provide
provide('popupConfirmItems', popupConfirmItems);
</script>
