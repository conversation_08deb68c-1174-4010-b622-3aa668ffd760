import MESSAGE from 'helpers/message';

const inputMobile = {
  additionalProperties: false,
  type: 'object',
  required: ['mobileNumber'],
  properties: {
    mobileNumber: {
      type: 'string',
      minLength: 1,
      pattern: '^\\d{11}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      mobileNumber: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default inputMobile;
