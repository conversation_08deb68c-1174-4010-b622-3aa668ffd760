<template>
  <q-card class="tw:border-[#004AB9]" bordered>
    <q-expansion-item
      :model-value="localExpanded"
      @update:model-value="updateExpanded"
      expand-separator
      class="w-full"
      header-class="tw:text-m-design tw:text-[#004AB9] tw:font-bold"
      expand-icon-class="tw:text-[3rem] tw:text-[#004AB9] tw:pr-0!"
    >
      <template #header>
        <div class="row items-center tw:flex-1">
          <q-img :src="svg" class="tw:w-[2.25rem]" />
          <span class="tw:ml-2 tw:text-m-design">{{
            provideData.title || "検索"
          }}</span>
        </div>
      </template>

      <q-card class="tw:border-neutral-500 tw:border-t tw:border-none">
        <q-card-section class="tw:px-6 tw:py-6">
          <div
            class="tw:tl:text-[2rem] tw:tl:leading-[2rem] tw:font-normal tw:text-[1rem] tw:leading-[1.5rem]"
          >
            <slot />
          </div>
          <div
            class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-end tw:gap-[1.5rem] tw:py-[1rem] tw:px-[.625rem]"
          >
            <q-btn
              outline
              class="tw:bg-white tw:rounded-[40px] tw:text-[#004AB9]
              tw:text-xs-design tw:font-bold tw:w-full tw:tl:w-[16.5rem] tw:h-[4.75rem]!"
              label="クリア"
              @click.prevent="provideData.handleClear()"
            />
            <q-btn
              class="tw:bg-[#004AB9] tw:rounded-[40px] tw:text-white tw:text-xs-design tw:font-bold tw:w-full tw:tl:w-[16.5rem] tw:h-[4.75rem]!"
              label="検索"
              @click.prevent="handleSearch()"
            />
          </div>
        </q-card-section>
      </q-card>
    </q-expansion-item>
  </q-card>
</template>

<script setup>
import { inject, ref, watch } from 'vue';
import UnionSvg from 'assets/Union.svg';

// store refs (không dùng nhưng giữ nguyên nếu có cần dùng lại sau)
const provideData = inject('expansionSearchProvideData');
const svg = ref(UnionSvg);

// Props + Emits xử lý v-model
const props = defineProps({
  expanded: Boolean,
});
const emit = defineEmits(['update:expanded']);

const localExpanded = ref(props.expanded);

watch(
  () => props.expanded,
  val => {
    localExpanded.value = val;
  }
);

const updateExpanded = val => {
  localExpanded.value = val;
  emit('update:expanded', val);
};

// Auto collapse after search
const handleSearch = async () => {
  await provideData.handleSearch?.();

  if (provideData.isClose){
    updateExpanded(false); // đóng lại
  }
};
</script>

<style>
.q-item__section--side > .q-icon {
  font-size: 3rem !important;
}

/* .q-card__section--vert {
  padding: 0 !important;
} */
</style>
