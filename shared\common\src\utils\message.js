const MESSAGE = {
  MSG_LIMITS_DATE_ERROR: `日付（YYYY/MM/DD）のみを入力してください。`,
  ERROR_MESSAGE_500:
    "このページを何らかの原因で正しく表示できませんでした。ブラウザの更新を行うか、もしくは再度時間をおいてからアクセスしてください。",
  MSG_REQUIRED_INPUTFORM_ERROR: `この項目は入力が必須です。`,
  MSG_NA_USERID_ERROR: "このユーザーIDは存在しません。",
  MSG_LIMITS_KANA_ERROR: "カタカナのみを入力してください。",
  MSG_LIMITS_PHONENUMBER_ERROR:
    "携帯電話番号は、数字のみの、11桁の数字を入力してください。",
  MSG_LIMITS_PASSWARD_ERROR:
    "パスワードは、8文字以上で英大文字、英小文字、数字をすべて含めて入力してください。",
  MSG_NOTMATCH_PASSWARD_ERROR:
    "パスワードとパスワード（確認用）が一致していません。",
  MSG_INVALID_QRCODE_ERROR: "このQRコードはサポートされていません。",
  MSG_LIMITS_PREMISSION_ERROR:
    "この 利用者カードは、採捕許可がありません。採捕許可の有効期限を超えていないか確認してください。",
  MSG_INVALID_LICENSES_ERROR: "ライセンスIDが存在しない",
  MSG_USED_NOTIFICATIONUMBER_ERROR:
    "この届出/事業者割振り番号はすでに使用されています。",
  MSG_USED_FHONENUMBER_ERROR: "この携帯電話番号はすでに使用されています。",
  MSG_NA_TERMS_ERROR: "利用規約を確認し、利用規約に同意してください。",
  MSG_PHONE_NUMBER_DOES_NOT_EXIST:
    "この電話番号をもつユーザーが存在しません。ユーザーへ登録した電話番号として正しいか、確認してください。",
  API_KEY_NOT_VALID: "認証情報が正しくありません。",
  AUTH_TOKEN_ERROR: "認証情報が正しくありません。",
  MSG_ACCOUNT_NOT_DISTRIBUTION_BUSINESS:
    "取扱事業者以外がこの機能を使用することはできません。",
  MSG_CHARACTERS_ARE_NOT_FULLSIZE:
    "この項目へ入力した文字列に半角文字が含まれます。全角文字のみを指定してください。",
  MSG_LIMITS_NUMBER_ERROR: "数字のみを入力してください。",
  LICENSE_NUMBER_ERROR:
    "無効な許可番号が指定されました。許可番号として有効な値を指定してください。",
  MSG_DEL_EMPLOYEE_INFO: `従事者から %1$s を削除しました。`,
  MSG_CHANGED_USERSETTING_INFO: "ユーザー設定を変更しました。",
  MSG_PAGE_INDEX_INVALID:
    "無効なページ番号が指定されました。有効なページ番号を指定してください。",
  MSG_PAGE_SIZE_INVALID:
    "無効なページサイズが指定されました。有効なページサイズを指定してください。",
  MSG_NAME_INVALID:
    "無効な会社名が指定されました。有効な文字列を指定してください。",
  MSG_PARTNER_TYPE_INVALID:
    "取引先区分が指定されていません。取引先区分を指定してください。",
  MSG_HIDDEN_FLG_INVALID:
    "無効な非表示フラグが指定されました。有効な非表示フラグを指定してください。",
  MSG_NA_NOTIFICATIONUMBER_ERR: `検索した届出/事業者割振り番号の事業者は存在しません。`,
  MSG_LIMITS_NOTIFICATIONUMBER_ERROR:
    "届出番号/事業者割振り番号は、数字のみの、7桁の数字を入力してください。",
  MSG_RESISTER_CUSTOMER_INFO: "取引先を登録しました。",
  MSG_RESISTER_EMPLOYEE_INFO: `従事者を登録しました。`,
  MSG_FIX_EMPLOYEE_INFO: `従事者を修正しました。`,
  MSG_CHANGED_SETTING_INFO: `設定を変更しました。`,
  MSG_SAGE_NUM_ERROR:
    "上限を超える数字が入力されています。サポートしている範囲の数字を入力してください。",
  MSG_SAFE_DIGITS_ERROR:
    "上限を越える桁数が入力されています。サポートしている範囲の桁数で入力してください。",
  MSG_INVALID_LOGIN_ERROR: "養鰻事業者はログインできません。",
  MSG_TOO_MANY_RESULTS: "検索結果が多すぎるため、検索条件を指定して絞り込んでください。",
  MSG_NO_DATA_TO_UPDATE_ERROR: "「無許可」へ更新する対象ユーザーがが存在しないため、処理を中止しました。",

  // for from date and to date
  MSG_FROM_DATE_ERROR:
    "検索条件において、日付の開始日の形式が不正です。YYYY/MM/DDの形式で指定してください。",
  MSG_TO_DATE_ERROR:
    "検索条件において、日付の終了日の形式が不正です。YYYY/MM/DDの形式で指定してください。",
  MSG_DATE_CONSTRAINT_ERROR: "開始日は終了日より前の日付を指定してください。",
  MSG_ACCOUNT_NOT_ADMIN: "Admin以外がこの機能を使用することはできません。",
  MSG_ACCOUNT_NOT_SYSTEM_ADMIN:
    "System Admin以外がこの機能を使用することはできません。",
  MSG_START_DATE_CONSTRAINT_ERROR:
    "開始日は終了日より前の日付を指定してください。",
  MSG_LINKED_PARTNER_ERROR:
    "この事業者は取引先として登録できません。取引先に指定した事業者を確認してください。",
  DATE_ERROR: "日付（YYYY/MM/DD）のみを入力してください。",
  NOTE1_ERROR:
    "検索条件において、備考１へ不正な形式が指定されています。サポートされた形式を指定してください。",
  NOTE2_ERROR:
    "検索条件において、備考２へ不正な形式が指定されています。サポートされた形式を指定してください。",
  MSG_PERMISSION_DUPLICATE: "この許可番号はすでに使用されています。",
  MSG_PARTNER_NOT_MATCH_FUNCTION:
    "指定した届出番号に対して、登録できない取引先区分が指定されています。指定した届出番号と取引先区分が正しいか、確認してください。",
  MSG_NO_DATA_INFO: "一致する検索結果がありませんでした。",
  MSG_NO_DATA_NOTIFICATION: "データが見つかりません。",
  MSG_LIMIT_EXPORT:
    "出力するPDF、またはCSVファイルが出力上限である10,000行を越えました。検索条件として値を追加して、ファイルを分割して出力してください。",
  MSG_INVARID_CREATEUSER_NOTWORKER_ERROR:
    "この届出番号の事業者が存在しないため、従事者を登録できません。事業者を作成のうえ、従事者を登録してください。",
  MSG_LIMITS_ROWS_ERROR:
    "指定されたファイルのユーザー設定が5000個を越えています。ユーザー設定の数を5000個以下へ編集してください。",
  MSG_NA_CSVITEM_ERROR:
    "ユーザー設定CSVとして必要な列項目がありません、もしくは不要な列項目があります。対象のCSVファイルの1行目の列項目が正しいか確認してください。",
  SG_INVARID_USERIDFORMAT_ERROR:
    "不正な形式のユーザーIDが入力されています。正しい形式のユーザーIDを入力してください。",
  MSGMSG_INVARID_CREATEUSER_NOTWORKER_ERROR_INVARID_ITEM_WITHOUTSAIHO_ERROR:
    "採捕事業者および採捕従事者以外に不要な項目が入力されています。不要な項目を削除してください。",
  MSG_INVARID_NOTIFICATIONUMBER_WORKERCLASS_ERROR:
    "養鰻事業者は、事業者区分を従事者として設定することができません。採捕事業者および取扱事業者のみ従事者と設定できます。",
  MSG_INVARID_ADDRESSFORMAT_ERROR:
    "不正な住所が入力されています。正しい住所を入力してください。",
  MSG_REUSE_INITPASSWARD_ERROR:
    "初期パスワードを再度パスワードとして登録することはできません。初期パスワードとは異なるパスワードを入力してください。",
  MSG_UNDELETABLECUSTOMER_FORSIPPINGRESULT_ERROR:
    "この取引先を指定した出荷実績が、入荷されていないため削除できません。対象の出荷実績の入荷を完了する、もしくは取り消した上で削除してください。",
  MSG_UNEDITABLECUSTOMER_FORSIPPINGRESULT_ERROR:
    "この取引先を指定した出荷実績が、入荷されていないため修正できません。対象の出荷実績の入荷を完了する、もしくは取り消した上で修正してください。",
  MSG_LOCKED_USERID_ERROR:
    "一定回数以上の失敗があったため、1分後に再試行してください。",
  MSG_NOTMATCH_USERIDPASS_ERROR:
    "ユーザーIDまたはパスワードが正しくありません。ユーザーIDとパスワードを確認してください。",
  MSG_LOGIN_EEL_FARMING_BUSINESSESL: "養鰻事業者はログインできません。",
  MSG_INVARID_NOTIFICATIONUMBER_SAIHO_ERROR:
    "採捕事業者および採捕従事者の届出番号には「0」から始まる7桁の数字を入力してください。",
  MSG_INVARID_NOTIFICATIONUMBER_DISTRIBUTOR_ERROR:
    "取扱事業者および取扱従事者の届出番号には「5」から始まる7桁の数字を入力してください。",
  MSG_INVARID_ITEM_DISTRIBUTOR_ERROR:
    "取扱事業者および取扱従事者に不要な項目が入力されています。不要な項目を削除してください。",
  MSG_UPDATE_USER_STATUS_INFO: "ユーザーの状態を「無許可」に更新しました。",
  // for login instead user id
  MSG_DIFFERENT_NOTIFICATIONUMBER_ERROR: '他の採捕事業者からの入荷登録はできません。',
  // for setting
  MSG_LIMITS_AUTOLOGOUTTIME_ERROR:
    "自動ログアウトまでの時間は、1~24の数字を入力してください。",
  // for partners
  MSG_DIFFERENT_NOTIFICATIONUMBER_PARTNER_ERROR: '他の採捕事業者のユーザーは取引先に登録できません。',
};

module.exports = { MESSAGE };
