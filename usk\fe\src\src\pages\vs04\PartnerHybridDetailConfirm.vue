<template>
  <q-card
    class="tw:p-5 tw:flex tw:flex-col tw:h-full tw:pb-[19rem] tw:tl:pb-[8rem]"
  >
    <div class="tw:text-l-design tw:font-bold">取引先詳細</div>
    <div class="tw:text-m-design tw:mt-5">
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> ユーザーID </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.user_code }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 事業者/従事者区分 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{
            mapEnterpriseTypeStaffTypeToDisplay(
              partnerHybridDetail.enterprise_type,
              partnerHybridDetail.staff_type
            )
          }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 都道府県 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:min-h-[4.25rem] tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.provinces?.name || '' }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 届出事業者名 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.user_apply?.name || '' }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 事業者名 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.name || '' }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 事業者名（カナ） </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:min-h-[4.25rem] tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.name_kana || '' }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 届出番号 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.user_code?.slice(0, 7) || '' }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
        v-if="
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
            [],
            partnerHybridDetail
          )
        "
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center"
        >
          <span> 許可番号 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:min-h-[4.25rem] tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>{{ partnerHybridDetail.license_number || '' }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 取引先区分 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:flex tw:gap-4 tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
        >
          <span>
            {{ mapPartnerTypeToDisplay(partnerHybridDetail.partner_type ?? []) }}
          </span>
        </div>
      </div>
    </div>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3
      tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:items-center tw:flex tw:justify-center tw:tl:justify-between
      tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[23.45rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="入力内容を修正する"
        @click.prevent="router.back()"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="登録する"
        @click.prevent="handleClickRegister"
      />
    </q-footer>
  </q-card>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import partnerService from 'services/locatedTransaction.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { CHECK_ROLE } from 'src/helpers/common';
import {
  ENTERPRISE_TYPE_ENUM,
  PARTNER_TYPE_ENUM,
  ROLES_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
  STAFF_TYPE_ENUM,
  USER_ROLE_OPTIONS,
} from 'src/helpers/constants';
import toast from 'src/shared/utilities/toast';
import { useAppStore } from 'src/stores/app-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const partnerHybridDetail = ref({});
const { previousRoute } = storeToRefs(useAppStore());
const { getConfirmData } = useConfirmFormStore();
const partnerTypeOptions = [
  {
    label: '仕入先',
    value: PARTNER_TYPE_ENUM.SUPPLIER.toString(),
  },
  {
    label: '出荷先',
    value: PARTNER_TYPE_ENUM.SHIPPER.toString(),
  },
];

// #region functions
const handleClickRegister = async () => {
  const payload = {
    partner_id: partnerHybridDetail.value.id,
    partner_type: partnerHybridDetail.value.partner_type?.map(Number) || [],
  };

  const response = await partnerService.registerPartnerHybrid(payload);

  if (response.code === 0) {
    toast.access(response.payload.message);
    router.push({
      name: 'partner',
    });
  }
};
// #endregion functions

// #region helper functions
const mapEnterpriseTypeStaffTypeToDisplay = (enterprise_type, staff_type) => {
  let role = '';
  switch (enterprise_type) {
    case ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE:
      if (staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
        role = ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE;
      } else {
        role = ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF;
      }
      break;
    case ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE:
      if (staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
        role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE;
      } else {
        role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF;
      }
      break;
    default:
      role = ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE;
  }
  return USER_ROLE_OPTIONS.find(option => option.value === role)?.label || '';
};

const mapPartnerTypeToDisplay = partnerType =>
  partnerType
    .sort()
    .map(
      type =>
        partnerTypeOptions.find(option => option.value === type)?.label || ''
    )
    .join('/');
// #endregion helper functions

onMounted(async () => {
  const confirmData = getConfirmData();
  if (confirmData && previousRoute.value.name === 'partnerHybridDetail') {
    partnerHybridDetail.value = confirmData;
  } else {
    router.push({ name: 'partnerHybridDetail' });
  }
});
</script>
