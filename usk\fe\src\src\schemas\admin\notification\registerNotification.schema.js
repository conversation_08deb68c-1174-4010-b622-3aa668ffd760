import { ROLES_ENUM } from 'helpers/constants';
import ME<PERSON>AG<PERSON> from 'src/helpers/message';

const registerAdminNotification = {
  type: 'object',
  additionalProperties: false,
  required: ['title', 'content', 'roles'],
  properties: {
    title: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        maxLength: 'Title is too long (maximum is 256 characters)',
      },
    },
    content: {
      type: 'string',
      minLength: 1,
      maxLength: 10000,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        maxLength: 'Content is too long (maximum is 10000 characters)',
      },
    },
    roles: {
      type: 'array',
      minItems: 1,
      maxItems: 5,
      items: {
        type: 'string',
      },
      errorMessage: {
        minItems: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
  errorMessage: {
    additionalProperties: 'No additional properties are allowed',
  },
};

export default registerAdminNotification;
