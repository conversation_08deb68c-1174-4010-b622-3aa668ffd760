import boto3
import shutil
import subprocess
import logging
import os
import urllib.parse
import json

from secretsmanager.get_secret_value import GetSecretWrapper

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def run_command(command):
  commandList = command.split(' ')
  try:
    result = subprocess.run(commandList, stdout=subprocess.PIPE);
  except Exception as e:
    logger.error("Exception: {}".format(e))
    return None
  return result

def git_clone(repoName, branch):
  regionName = os.environ['REGION']
  secretName = "{branch}/backlog_git_secrets".format(branch = branch)
  # Create a Secrets Manager client
  session = boto3.session.Session()
  client = session.client(
   service_name = "secretsmanager",
   region_name = regionName
  )
  wrapper = GetSecretWrapper(client)
  secret = wrapper.get_secret(secretName)
  secretJSon = json.loads(secret)
  gitUser = secretJSon["GIT_USER"]
  gitPass = secretJSon["GIT_PASSWORD"]
  gitUrl = os.environ['GIT_URL'].format(git_user = urllib.parse.quote(gitUser), git_pass = urllib.parse.quote(gitPass), repo_name = repoName)
  # gitUrl = os.environ['GIT_URL'].format(repo_name = repoName)
  gitCloneCmd = "git clone -b {branch} {git_url} /tmp/source".format(branch = branch, git_url = gitUrl)
  run_command(gitCloneCmd)
  # Change directory (cd)
  os.chdir("/tmp/source")

def put_s3(repoName, branch):
  s3Bucket = os.environ['S3_BUCKET']
  session = boto3.session.Session()
  regionName = os.environ['REGION']
  client = session.client(
    service_name = "s3",
    region_name = regionName
  )
  # zip source
  # archived = shutil.make_archive('/tmp/source/source', 'zip', "{dir_path}".format(dir_path = "/tmp/source"))
  gitZipCmd = "git archive -o source.zip HEAD"
  run_command(gitZipCmd)
  archived = "/tmp/source/source.zip"
  client.upload_file(archived, s3Bucket, "{branch}/source.zip".format(branch = branch))
  return archived

def read_package_version(repoName, psp):
  packageJsonPath = "{dir_path}/{psp}/src/package.json".format(psp = psp, dir_path = "/tmp/source")
  data = json.load(open(packageJsonPath))
  return data['version']

def start_pipe(pipelineName, version):
  session = boto3.session.Session()
  regionName = os.environ['REGION']
  client = session.client(
    service_name = "codepipeline",
    region_name = regionName
  )
  response = client.get_pipeline(name = pipelineName)
  variables = response["pipeline"]["variables"]
  roleArn = response["pipeline"]["roleArn"]
  stages = response["pipeline"]["stages"]
  artifactStore = response["pipeline"]["artifactStore"]
  versionNotExist = True
  versionRunPipe = []
  for variable in variables:
    if variable["name"] == "VERSION":
      variable["defaultValue"] = version
      versionNotExist = False
  if versionNotExist == True:
    variables += [{
      "name": "VERSION",
      "defaultValue": version
    }]
  for variable in variables:
    versionRunPipe += [{
      "name": variable["name"],
      "value": variable["defaultValue"],
    }]
  client.update_pipeline(pipeline = {
    "name": pipelineName,
    "roleArn": roleArn,
    "artifactStore": artifactStore,
    "stages": stages,
    "variables": variables,
    "pipelineType": "V2",
  })
  client.start_pipeline_execution(name = pipelineName,
    variables = versionRunPipe)

def get_params_store_current():
  session = boto3.session.Session()
  regionName = os.environ['REGION']
  client = session.client(
    service_name = "ssm",
    region_name = regionName
  )
  response = client.get_parameter(
    Name='UNAGIREVISIONCURRENT',
    WithDecryption=False
  )
  Value = response['Parameter']['Value']
  return Value

def update_params_store_current(rev):
  session = boto3.session.Session()
  regionName = os.environ['REGION']
  client = session.client(
    service_name = "ssm",
    region_name = regionName
  )
  client.put_parameter(
    Name='UNAGIREVISIONCURRENT',
    Overwrite=True,
    Value=rev,
  )

def start_build(repoName, revisions):
  packageSourcePath = os.environ['PACKAGE_SOURCE_PATH'].split(';')
  pipelineList = os.environ['PIPELINE_LIST'].split(';')
  pipelineStart = []
  pipelineRuns = []
  rev = get_params_store_current()
  revNew = revisions[0]['rev']
  if rev == revNew:
    return pipelineRuns
  gitDiff = "git diff --name-only {rev}".format(dir_path = "/tmp/source", rev = rev)
  result = run_command(gitDiff)
  for idx, psp in enumerate(packageSourcePath):
    if bytes(psp, 'utf-8') in result.stdout and pipelineList[idx] not in pipelineStart:
      pipelineStart += [pipelineList[idx]]
      version = read_package_version(repoName, psp)
      pipelineRuns += [{
        'pipeline_name': pipelineList[idx],
        'version': version
      }]
  update_params_store_current(revNew)
  return pipelineRuns

def remove_source(repoName):
  deleteRepo = "rm -rf {dir_path}".format(dir_path = "/tmp/source", repo_name = repoName)
  run_command(deleteRepo)
  os.chdir(os.environ["LAMBDA_TASK_ROOT"])

def handler(event, context):
  logger.info(event)
  for message in event['Records']:
    bodyData = json.loads(message['body'])
    contentData = bodyData['content']
    # contentData = event['content']
    repoName = contentData['repository']['name']
    branch = contentData['ref'].replace("refs/heads/", "")
    revisions = contentData['revisions']
    branchCheck = os.environ['BRANCH_CHECK'].split(";")
    repoCheck = os.environ['REPO_CHECK']
    try:
      if branch in branchCheck and repoName == repoCheck:
        git_clone(repoName, branch)
        put_s3(repoName, branch)
        pipelineRuns = start_build(repoName, revisions)
        logger.info(pipelineRuns)
        for pipelineRun in pipelineRuns:
          start_pipe(pipelineName = pipelineRun["pipeline_name"], version = pipelineRun["version"])
    except Exception as e:
      logger.error("Exception: {}. Message Id: {}".format(e, message["messageId"]))
    finally:
      remove_source(repoName)

# if __name__ == "__main__":
#   event = {'id': 320397304, 'project': {'id': 202094, 'projectKey': 'USKD', 'name': 'うなぎトレーサビリティシステム(内部)', 'chartEnabled': True, 'subtaskingEnabled': True, 'projectLeaderCanEditProjectLeader': True, 'useWikiTreeView': True, 'textFormattingRule': 'markdown', 'archived': False}, 'type': 12, 'content': {'change_type': 'update', 'ref': 'refs/heads/develop', 'repository': {'id': 91043, 'name': 'usk-repository', 'description': None}, 'revision_count': 1, 'revision_type': 'commit', 'revisions': [{'rev': '08388de853ee3d2d62f2ba9c4160fd9083ae3904', 'comment': "Merge remote-tracking branch 'remotes/origin/develop_ssv' into develop"}]}, 'notifications': [], 'createdUser': {'id': 403015, 'userId': None, 'name': 'BinhDC@SSV', 'roleType': 2, 'lang': 'en', 'mailAddress': None, 'nulabAccount': {'nulabId': 'hu9leXaAaHjWKNbKTkigattAfdDsjiSSskM3FRRov7zzWOiFZS', 'name': 'Doan Cong Binh', 'uniqueId': 'binhdc'}}, 'created': '2024-09-18T08:41:34Z'}
#   handler(event, None)