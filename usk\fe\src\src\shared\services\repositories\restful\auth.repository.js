import BaseRepository from './base.repository';

class AuthRepository extends BaseRepository {
  constructor() {
    super('auth');
  }

  login(params) {
    return this.client.post('/login', params);
  }

  adminLogin(params) {
    return this.client.post('/admin/login', params);
  }

  loginLink(params) {
    return this.client.post('/login-link', params);
  }

  checkLogin() {
    return this.client.get('/checklogin');
  }

  loginInsteadUserId(params) {
    return this.client.post('/login-instead/user-id', params);
  }

  loginInsteadLink(body) {
    return this.client.post('/login-instead-link', body);
  }

  sendSmsPassword(body) {
    return this.client.post('/sms-password', body);
  }

  resetPassword(body, token) {
    return this.client.post(`/resetpassword/${token}`, body);
  }

  recoveryPassword(body) {
    return this.client.post('/recovery-password', body);
  }
}

export default new AuthRepository();
