import BaseService from 'services/base.service';
import { useAuthStore } from 'stores/auth-store';
import { useAppStore } from 'stores/app-store';

class UserService extends BaseService {
  async checkUserId(userId) {
    const result = await this.dao.checkUserId(userId);
    const { code, payload } = result;
    if (code === 0) {
      return payload;
    }
    return null;
  }

  async linkUserId(body) {
    // const auth = useAuthStore();
    // const { setRole } = useAppStore();
    const { signInLink } = useAuthStore();
    const result = await this.dao.linkUserId(body);
    const { code, payload } = result;
    if (code === 0) {
      // auth.signIn(payload);
      // setRole(payload.role);
      signInLink(payload);
    }
    return payload;
  }

  async addUser(body) {
    const auth = useAuthStore();
    const { setRole } = useAppStore();
    const result = await this.dao.addUser(body);
    const { code, payload } = result;
    if (code === 0) {
      auth.signIn(payload);
      setRole(payload);
    }
    return payload;
  }
}

export default new UserService('user');
