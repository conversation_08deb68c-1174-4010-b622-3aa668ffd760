const accountSchema = {
  tenant_code: {
    type: 'string',
    minLength: 1,
    maxLength: 10,
    errorMessage: {
      _: 'tenant_code is invalid',
    },
  },
  email: {
    type: 'string',
    minLength: 1,
    maxLength: 50,
    // eslint-disable-next-line no-useless-escape
    pattern: '^[^@\s]+@[^@\s]+\.[^@\s]+$',
    errorMessage: {
      _: 'email is invalid',
    },
  },
  name: {
    type: 'string',
    minLength: 1,
    maxLength: 80,
    errorMessage: {
      _: 'name is invalid',
    },
  },
  password: {
    type: 'string',
    minLength: 8,
    maxLength: 16,
    pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
    errorMessage: {
      _: 'password is invalid',
    },
  },
  // This PASSWORD regex will enforce these rules:
  // At least one upper case English letter, (?=.*?[A-Z])
  // At least one lower case English letter, (?=.*?[a-z])
  // At least one digit, (?=.*?[0-9])
  // At least one special character, (?=.*?[#?!@$%^&*-])
  // Minimum eight in length .{8,} (with the anchors)
};

module.exports = {
  accountSchema,
};