<template>
  <div class="tw:relative tw:text-[#333333]" ref="el">
    <router-view />
    <q-inner-loading :showing="loading" class="tw:z-[100000] tw:fixed tw:top-0">
      <q-spinner-pie size="50px" color="primary" />
    </q-inner-loading>
  </div>
</template>

<script setup>
import { useAppStore } from 'stores/app-store';
import { storeToRefs } from 'pinia';
import { watch, useTemplateRef, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useResizeObserver } from '@vueuse/core';

import toast from './shared/utilities/toast';
import { ROLES_ENUM } from './helpers/constants';

const el = useTemplateRef('el');
const heightEl = ref('');

useResizeObserver(el, entries => {
  const entry = entries[0];
  const { height } = entry.contentRect;
  heightEl.value = height;
});
const { loading, mesErr, isNonActive } = storeToRefs(useAppStore());
const { setMesErr, setIsNonActive } = useAppStore();
const router = useRouter();
watch(mesErr, () => {
  if (mesErr.value !== '') {
    const messageClone = mesErr.value;
    toast.error(messageClone);
    setTimeout(() => {
      setMesErr('');
    }, 1000);
  }
});

watch(heightEl, ()=>{
  const footer = document.querySelector('footer');
  if (!footer) {return;}

  const heightChange = window.innerHeight - heightEl.value;
  if (heightChange >= 100){
    footer.classList.remove(['tw:fixed', 'tw:bottom-0']);
    footer.classList.add(['tw:absolute']);
    footer.style.bottom = `-${heightChange}px`;
  } else {
    footer.classList.remove(['tw:absolute']);
    footer.classList.add(['tw:fixed', 'tw:bottom-0']);
    footer.style.bottom = '';
  }
});

watch(isNonActive, () => {
  if (
    isNonActive.value === ROLES_ENUM.ADMIN ||
    isNonActive.value === ROLES_ENUM.SYSTEM_ADMIN
  ) {
    setIsNonActive('');
    router.push({ name: 'adminLogin' });
  }
  if (
    [
      ROLES_ENUM.NORMAL_USER,
    ].includes(isNonActive.value)
  ) {
    setIsNonActive('');
    router.push({ name: 'loginUserId' });
  }
});
</script>
