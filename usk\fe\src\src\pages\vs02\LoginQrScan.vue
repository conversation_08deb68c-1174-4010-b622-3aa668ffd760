<template>
  <div>
    <q-card class="tw:p-6 tw:mt-10 tw:mx-7 tw:tl:mx-0 tw:pb-[14rem]">
      <div class="tw:text-m-design tw:font-normal tw:mt-4 tw:text-[#333333]">
        スキャナーで、QRコードをスキャンしてください。
      </div>
      <div class="tw:mt-48 tw:tl:mt-60">
        <div class="tw:flex tw:items-center tw:justify-center">
          <span class="tw:relative tw:flex tw:w-32 tw:h-32">
            <span
              class="tw:animate-ping tw:absolute tw:h-full tw:w-full tw:inline-flex tw:rounded-full tw:bg-sky-400 tw:opacity-75"
            ></span>
            <span
              class="tw:relative tw:inline-flex tw:rounded-full tw:w-32 tw:h-32 tw:bg-sky-300"
            ></span>
          </span>
        </div>
      </div>
    </q-card>

    <!-- Footer <PERSON><PERSON> -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:mt-4 tw:flex tw:flex-col tw:gap-8 tw:tl:flex-row
      tw:tl:items-center tw:tl:justify-start"
    >
      <div class="tw:text-[#333333] tw:text-m-design tw:font-normal tw:text-left">
        他の方法でログインする
      </div>
      <div
        class="tw:flex tw:justify-center tw:tl:justify-start tw:w-full tw:tl:w-auto tw:gap-4 tw:tl:gap-8"
      >
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
                tw:tl:w-[16.5rem] tw:h-[4.75rem] tw:min-h-[4.75rem] tw:max-h-[4.75rem]
                tw:w-[95.5%]`"
          label="ユーザーID"
          @click.prevent="goLoginUserId"
        />
      </div>
    </q-footer>
  </div>
</template>
<script setup>
import QRBlackSvg from 'assets/QRBlackSvg.svg';
import dayjs from 'boot/dayjs';
import useValidate from 'composables/validate';
import MESSAGE from 'helpers/message';
import loginQrScanSchema from 'schemas/loginQrScan.js';
import toast from 'utilities/toast';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import BaseButton from 'components/base/vs/BaseButton.vue';

const { validateData } = useValidate();
const router = useRouter();

// ===== REF =====
const qrCodeContent = ref('');
const dateStartScan = ref(dayjs());
const intervalFn = ref();

// ===== METHOD =====
const checkTimeScanQrCode = () => {
  const currentTime = dayjs();
  const diffInSeconds = currentTime.diff(dateStartScan.value, 'second');
  if (diffInSeconds > 5) {
    clearInterval(intervalFn.value);
    qrCodeContent.value = '';
    toast.error(MESSAGE.MSG_LIMITS_READTIMEQR_ERROR);
  }
};

const handleScan = async ev => {
  if (!/^[!-~]$/.test(ev.key)) {
    return;
  }
  qrCodeContent.value += ev.key;
  clearInterval(intervalFn.value);
  intervalFn.value = setInterval(checkTimeScanQrCode, 1000);
  const valid = validateData(loginQrScanSchema, {
    qrCode: qrCodeContent.value,
  });

  if (valid) {
    clearInterval(intervalFn.value);
    const qrCode = qrCodeContent.value.slice(-16);
    await router.push({
      name: 'loginLink',
      params: {
        qrCode,
      },
      query: {
        preRoute: 'loginQrScan',
      },
    });
  }
};

const goLoginUserId = () => {
  router.push({ name: 'loginUserId' });
};

// initialization
onMounted(() => {
  document.addEventListener('keydown', handleScan);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleScan);
});

watch(qrCodeContent, (newVal, oldVal) => {
  if (!oldVal) {
    dateStartScan.value = dayjs();
  }
});
</script>
