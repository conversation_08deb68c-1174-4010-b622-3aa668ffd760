<template>
  <div class="tw:p-3 tw:tl:p-6">
    <!-- title -->
    <div class="tw:text-center tw:text-2xl tw:p-5 tw:font-bold tw:tl:text-4xl">
      パスワード再登録完了
    </div>
    <!-- content -->
    <div class="tw:text-sm tw:my-5 tw:tl:text-[1.35rem] tw:tl:leading-[1.92rem]">
      <div>パスワードの再登録が完了しました。</div>
      <div class="tw:mt-3">
        ログイン画面から再ログインしてください
      </div>
    </div>
    <q-btn
      class="tw:w-full tw:mt-5 tw:text-base tw:font-bold tw:text-white
      tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      label="ログイン画面へ"
      type="button"
      outline
      :color="`${colorSub}`"
      @click.prevent="backToLogin"
    />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();

// ===== REF =====

// ===== METHOD =====
const backToLogin = async () => {
  await router.push({ name: 'loginUserId' });
};
</script>
