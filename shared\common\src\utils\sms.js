const { SendMessageCommand } = require('@aws-sdk/client-sqs');
const Env = require('../boot/env');
const { sqsClient } = require('../base/sqsclient');

const sendSMS = async (numberto, contentSMS) => {
  // Set the parameters
  const dataSMS = {
    Message: contentSMS /* required */,
    PhoneNumber: numberto, //PHONE_NUMBER, in the E.164 phone number structure
  };
  const input = { // SendMessageRequest
    QueueUrl: Env.AWS_SQS_SMS_URL, // required
    MessageBody: JSON.stringify(dataSMS, null, 2), // required
  };
  const command = new SendMessageCommand(input);
  await sqsClient.send(command);
}

module.exports = { sendSMS };