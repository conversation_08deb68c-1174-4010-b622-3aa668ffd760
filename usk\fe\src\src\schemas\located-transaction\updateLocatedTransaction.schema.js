import MESSAGE from 'helpers/message';
import { PARTNER_TYPE_ENUM } from 'helpers/constants';

const updateLocatedTransactionSchema = {
  type: 'object',
  additionalProperties: false,
  required: ['partnerType'],
  properties: {
    partnerType: {
      type: 'array',
      minItems: 1,
      items: {
        type: 'number',
        enum: Object.values(PARTNER_TYPE_ENUM),
      },
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export default updateLocatedTransactionSchema;
