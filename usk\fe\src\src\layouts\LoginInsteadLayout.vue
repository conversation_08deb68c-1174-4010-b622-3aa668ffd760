<template>
  <q-layout view="lHh Lpr lFf" class="tw:flex">
    <q-header elevated :class="`tw:bg-white tw:pt-[3px] tw:tl:pt-0 `">
      <q-toolbar
        class="tw:pr-0 tw:pl-0 tw:tl:pl-[0.75rem] tw:flex tw:tl:flex-row tw:flex-col tw:tl:justify-between tw:items-start! tw:tl:items-center"
      >
        <div class="tw:flex tw:flex-row tw:items-center">
          <q-btn
            flat
            dense
            round
            color="black"
            icon="menu"
            aria-label="Menu"
            @click.prevent="toggleLeftDrawer"
          >
            <q-badge
              v-if="hasUnreadNotificationFlag"
              :class="`tw:bg-red-2 tw:top-[-1px]
              tw:px-[5px] tw:text-[10px] tw:leading-[10px]
              tw:min-h-[10px] tw:min-w-[10px]`"
              rounded
              floating
            />
          </q-btn>

          <q-toolbar-title>
            <div class="tw:flex tw:justify-between">
              <div
                class="tw:tl:flex-1 tw:text-[15px] tw:leading-[1.5]
                tw:dt:text-[32px] tw:font-[700] tw:text-[#333333] tw:flex tw:flex-wrap tw:tl:flex-row tw:flex-col tw:tl:justify-between"
              >
                <div
                  class="tw:w-full tw:cursor-pointer tw:flex tw:items-center"
                  @click.prevent="gotoPage('home')"
                >
                  {{ envPrefix }}シラスウナギトレーサビリティ支援システム
                </div>
              </div>
            </div>
          </q-toolbar-title>
        </div>
        <div
          v-if="roleName && roleColor"
          :class="`tw:tl:min-h-[50px] tw:w-full tw:tl:w-[309px] tw:flex
          tw:items-center tw:justify-center tw:text-white tw:text-center
          tw:text-[15px] tw:leading-[1.5] tw:dt:text-[32px] tw:font-[700] tw:py-5 ${roleColor}`"
        >
          {{ roleName }}
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered behavior="mobile">
      <q-list>
        <q-item-label header>
          <img
            :src="CloseSvg"
            alt=""
            class="tw:w-[2.5rem] tw:h-[2.5rem] tw:hover:cursor-pointer"
            @click.prevent="toggleLeftDrawer"
          />
        </q-item-label>

        <EssentialLink />
        <div class="tw:px-4 tw:pb-2 tw:mt-10">
          <span class="tw:text-s-design">お問い合わせ：</span>
          <div>
            <span class="tw:text-xxs-design">一般社団法人</span>
            <span class="tw:text-[1.875rem] tw:font-bold tw:ml-3"
              >全日本持続的養鰻機構</span
            >
          </div>
          <div>
            <span class="tw:text-[1.875rem]">Tel.</span>
            <a
              href="tel:03-5797-7690"
              class="tw:text-blue-3 hover:tw:opacity-80 tw:text-[1.875rem] tw:tl:hidden"
            >
              03-5797-7690
            </a>
            <span class="tw:text-[1.875rem] tw:hidden tw:tl:inline">
              03-5797-7690</span
            >
          </div>
        </div>
      </q-list>
    </q-drawer>

    <div
      class="tw:w-screen tw:flex-1 tw:pt-[3.5rem] tw:tl:pt-[5.3rem] tw:dt:pt-[3rem]"
    >
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        class="tw:w-full tw:h-full"
        ref="scrollAreaRef"
      >
        <q-page-container class="tw:px-3 tw:tl:px-8 tw:pt-0">
          <div
            class="w-content tw:tl:w-[calc(100vw-4rem)] tw:mb-3 tw:dt:mt-[1rem]"
          >
            <div
              class="tw:pt-2 tw:inline-flex tw:items-center tw:hover:cursor-pointer tw:hover:opacity-70"
              @click.prevent="backRoute"
              v-if="
                router.currentRoute.value?.meta?.routeBack &&
                !router.currentRoute.value?.meta?.isBreadcrumb
              "
            >
              <q-icon
                name="arrow_back"
                class="tw:tl:text-[2rem] tw:text-lg tw:text-gray-1"
              />
              <span
                class="tw:ml-2 tw:tl:text-[2rem] tw:font-black tw:text-lg tw:text-gray-1"
              >
                戻る</span
              >
            </div>
            <div
              class="tw:flex tw:justify-between"
              v-if="
                router.currentRoute.value?.meta?.title &&
                !router.currentRoute.value?.meta?.isBreadcrumb
              "
            >
              <div
                class="tw:tl:leading-[4rem] tw:tl:text-[4rem] tw:font-bold tw:text-2xl tw:border-b tw:border-gray tw:pb-6 tw:pt-4 tw:flex-1"
              >
                {{ router.currentRoute.value.meta.title }}
              </div>
            </div>
            <div
              class="tw:flex tw:justify-between"
              v-if="router.currentRoute.value?.meta?.isBreadcrumb"
            >
              <!-- Breadcrumbs -->
              <q-breadcrumbs
                class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:pb-6 tw:flex-1 tw:tl:mt-[2rem] tw:mt-[8rem]"
                active-color="tw:text-blue-3"
              >
                <template v-slot:separator>
                  <q-icon
                    size="1.5em"
                    name="chevron_right"
                    class="tw:text-[#7E8093]"
                  />
                </template>
                <q-breadcrumbs-el
                  class="tw:cursor-pointer"
                  label="トップ"
                  @click.prevent="gotoPage('home')"
                />
                <q-breadcrumbs-el
                  :label="`${router.currentRoute.value.meta.title}`"
                />
              </q-breadcrumbs>
            </div>
            <router-view />
          </div>
        </q-page-container>
      </q-scroll-area>
    </div>
  </q-layout>
</template>

<script setup>
import CloseSvg from 'assets/CloseSvg.svg';
import PolicySvg from 'assets/PolicySvg.svg';
import DescriptionSvg from 'assets/DescriptionSvg.svg';
import LicenseSvg from 'assets/LicenseSvg.svg';
import GradingSvg from 'assets/GradingSvg.svg';
import EssentialLink from 'components/EssentialLink.vue';
import { storeToRefs } from 'pinia';
import { ENTERPRISE_TYPE_ENUM, STAFF_TYPE_ENUM } from 'src/helpers/constants';
import { useAppStore } from 'stores/app-store';
import { onMounted, provide, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from 'src/stores/auth-store';

const roleName = ref('');
const roleColor = ref('');

const envPrefix = ref('');
const { hasUnreadNotificationFlag } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();

const linksList = ref([
  {
    title: 'プライバシーポリシー',
    // vs14 done
    link: 'privacyPolicyVS',
    svg: PolicySvg,
    role: [],
  },
  {
    title: 'マニュアル',
    // vs14 done
    external: true,
    link: `${window.location.origin}/document/usersite/manual.pdf`,
    svg: DescriptionSvg,
    role: [],
  },
  {
    title: 'ライセンス',
    // vs14 done
    link: 'licenseVS',
    svg: LicenseSvg,
    role: [],
  },
  {
    title: '利用規約',
    // vs14 done
    link: 'termsOfUserVS',
    svg: GradingSvg,
    marginBot: true,
    role: [],
  },
]);

const leftDrawerOpen = ref(false);

provide('items', linksList);

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const backRoute = async () => {
  if (router.currentRoute.value.meta.routeBack) {
    router.back();
  }
};

const gotoPage = async name => {
  await router.push({ name });
};

onMounted(() => {
  roleName.value = '';
  roleColor.value = '';

  envPrefix.value = `${process.env.PREFIX}`;

  if (
    user.value?.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
  ) {
    if (user.value.staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
      roleName.value = '取扱事業者';
      roleColor.value = 'tw:bg-[#197A4B]';
    } else {
      roleName.value = '取扱従事者';
      roleColor.value = 'tw:bg-[#197A4B]';
    }
  }

  if (user.value?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    if (user.value.staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
      roleName.value = '採捕事業者';
      roleColor.value = 'tw:bg-[#0055AD]';
    } else {
      roleName.value = '採捕従事者';
      roleColor.value = 'tw:bg-[#0055AD]';
    }
  }
  if (
    user.value?.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
  ) {
    roleName.value = '養鰻事業者';
    roleColor.value = 'tw:bg-[#8B008B]';
  }

  if (
    user.value?.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
  ) {
    linksList.value = linksList.value.filter(
      item => item.link !== 'loginInsteadUserId'
    );
  }
});

// style scroll
const thumbStyle = {
  right: '4px',
  borderRadius: '5px',
  backgroundColor: '#737373',
  width: '2px',
  opacity: 0.75,
};
const barStyle = {
  right: '2px',
  borderRadius: '9px',
  backgroundColor: '#737373',
  width: '4px',
  opacity: 0.2,
};
</script>
<style>
:deep(aside.q-drawer) {
  width: 312px !important;

  @media (min-width: 960px) {
    width: 406px !important;
  }
}

.q-item__label {
  line-height: initial !important;
}

:deep(button[aria-label="Menu"] .q-icon) {
  font-size: 2.25rem !important;
}
</style>
