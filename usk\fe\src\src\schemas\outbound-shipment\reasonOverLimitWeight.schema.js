import MESSAGE from 'helpers/message';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'src/helpers/constants';

const reasonOverLimitWeightSchema = {
  type: 'object',
  additionalProperties: false,
  required: ['typeDiff'],
  properties: {
    typeDiff: {
      type: 'number',
      enum: Object.values(TYPE_DIFFERENCE_WEIGHT_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    reasonDiff: {
      type: 'string',
      maxLength: 300,
      errorMessage: {
        _: MESSAGE.REASON_DIFFERENCE_INVALID,
      },
    },
  },
  if: {
    properties: {
      typeDiff: {
        const: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
      },
    },
  },
  then: {
    required: ['reasonDiff'],
    properties: {
      reasonDiff: {
        type: 'string',
        maxLength: 300,
        minLength: 1,
        errorMessage: {
          maxLength: MESSAGE.REASON_DIFFERENCE_INVALID,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
  },
};

export default reasonOverLimitWeightSchema;
