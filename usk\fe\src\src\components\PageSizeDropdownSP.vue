<template>
  <q-select
    outlined
    :options="pageSizeOptions"
    hide-dropdown-icon
    hide-bottom-space
    popup-content-class="popup-content-class-custom tw:text-m-design"
    class="vs tw:w-[13rem] tw:h-[4.25rem] tw:text-m-design tw:flex tw:justify-center tw:items-center"
    :option-label="opt => `${opt}件`"
    :option-value="opt => opt"
    :display-value="`${modelValue}件`"
    @update:model-value="$emit('update:modelValue', $event)"
    emit-value
    map-options
  >
    <template v-slot:append>
      <q-icon
        name="expand_more"
        class="tw:bg-[#004AB9] tw:h-[4.25rem] tw:w-[4.25rem]"
        color="white"
        size="1.5em"
      />
    </template>
  </q-select>
</template>

<script setup>
import { ref } from 'vue';

const pageSizeOptions = ref([10, 25, 50]);
const props = defineProps({
  modelValue: [String, Number, Object, Array],
});
const emit = defineEmits(['update:modelValue']);

</script>

<style>
.q-field--outlined .q-field__control {
  padding-right: 0px !important;
}
</style>
