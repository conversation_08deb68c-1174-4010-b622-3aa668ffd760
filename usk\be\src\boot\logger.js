const os = require('os');
const { SECRETS } = require('../utils/secrets/secrets');
const { FORMAT_LOG } = require('../utils/format');
const Env = require('./env');
const redactSecrets = require('../utils/secrets')({ redactedText: '***Mask***', keys: SECRETS });

class Logger {
  constructor(logName) {
    this.defaultLayout = {
      ec2_name: os.hostname(),
      logName,
    };
  }

  async writeLog(data, meta = {}) {
    const layout = {
      ...this.defaultLayout,
    };
    let message = { ...layout, ...meta, ...data };
    try {
      const messageLength = Buffer.from(JSON.stringify(message, null, 0)).length;
      if (messageLength > 256000) {
        let dataLog = {};
        const keysNumber = Object.keys(data).filter(key => Number(key) || Number(key) === 0);
        if (keysNumber.length) {
          dataLog = `Array(${keysNumber.length})`
        } else {
          Object.keys(data).forEach(value => {
            FORMAT_LOG(data[value], value, dataLog);
          });
        }
        message = { ...layout, ...meta, ...dataLog };
      }
    } catch (error) {
      console.log(error.message);
    }
    console.log(JSON.stringify(message));
  }

  async debug(message, meta = {}) {
    const content = {
      level: 'DEBUG',
      message,
    };
    await this.writeLog(content, meta);
  }

  async info(message, meta = {}) {
    const content = {
      level: 'INFO',
      message,
    };
    await this.writeLog(content, meta);
  }

  async warn(message, meta = {}) {
    const content = {
      level: 'WARN',
      message,
    };
    await this.writeLog(content, meta);
  }

  async trace(message, meta = {}) {
    const content = {
      level: 'TRACE',
      message,
    };
    await this.writeLog(content, meta);
  }

  async fatal(message, meta = {}) {
    const content = {
      level: 'FATAL',
      message,
    };
    await this.writeLog(content, meta);
  }

  async error(message, meta = {}) {
    const content = {
      level: 'ERROR',
      message,
    };
    await this.writeLog(content, meta);
  }
}

const APP_LOGGER = new Logger('unagi');
const DB_LOGGER = new Logger('db');
DB_LOGGER.query = (data) => {
  DB_LOGGER.info(data, { type: 'SQL' });
};
DB_LOGGER.queryerror = (data) => {
  DB_LOGGER.warn(data, { type: 'SQL-ERROR' });
};

APP_LOGGER.request = async (request) => {
  const { body } = request;
  const data = {
    ...body,
    ...request.query,
    ...request.params,
  };
  const { user } = request;
  const userAgent = request.headers ? request.headers['user-agent'] : '';
  const mobileId = request.headers ? request.headers['mobile-id'] : '';
  const now = new Date();
  const meta = {
    type: 'REQUEST',
    request_id: `${os.hostname()}_${request.id}`,
    url: request.url,
    user_agent: userAgent,
    method: request.method,
    mobileid: mobileId,
    user_id: user?.id,
    request_time: +(now),
    ip: request.ip,
  };
  const message = process.env.NODE_ENV !== 'production' ? data : redactSecrets.map(data);
  await APP_LOGGER.info(message, meta);
};

APP_LOGGER.response = async (request) => {
  let data = {};
  const { user } = request;
  const userAgent = request.headers ? request.headers['user-agent'] : '';
  const mobileId = request.headers ? request.headers['mobile-id'] : '';
  const now = new Date();
  const meta = {
    type: 'RESPONSE',
    request_id: `${os.hostname()}_${request.id}`,
    url: request.url,
    user_agent: userAgent,
    method: request.method,
    mobileid: mobileId,
    user_id: user?.id,
    response_time: +(now),
    ip: request.ip,
  };
  const message = process.env.NODE_ENV !== 'production' ? data : redactSecrets.map(data);
  await APP_LOGGER.info(message, meta);
};

APP_LOGGER.exception = async (data, metaInput, request = {}) => {
  const { user } = request;
  const userAgent = request.headers ? request.headers['user-agent'] : '';
  const mobileId = request.headers ? request.headers['mobile-id'] : '';
  const now = new Date();
  const meta = {
    request_id: `${os.hostname()}_${request.id}`,
    url: request.url,
    user_agent: userAgent,
    method: request.method,
    mobileid: mobileId,
    user_id: user?.id,
    time: +(now),
    ip: request?.ip,
    ...metaInput,
  };
  const message = Env.NODE_ENV !== 'production' ? data : redactSecrets.map(data);
  APP_LOGGER.error(message, meta);
};

APP_LOGGER.business = async (data, metaInput, request = {}) => {
  const { user } = request;
  const userAgent = request.headers ? request.headers['user-agent'] : '';
  const mobileId = request.headers ? request.headers['mobile-id'] : '';
  const now = new Date();
  const meta = {
    request_id: `${os.hostname()}_${request.id}`,
    url: request.url,
    user_agent: userAgent,
    method: request.method,
    mobileid: mobileId,
    user_id: user?.id,
    time: +(now),
    ip: request.ip,
    ...metaInput,
  };
  const message = Env.NODE_ENV !== 'production' ? data : redactSecrets.map(data);
  APP_LOGGER.warn(message, meta);
};

module.exports = {
  APP_LOGGER,
  DB_LOGGER,
};
