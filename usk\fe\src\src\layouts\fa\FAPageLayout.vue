<template>
  <q-layout view="lHh Lpr lFf" class="tw:flex">
    <q-header
      elevated
      :class="`tw:bg-white tw:pt-[3px] tw:tl:pt-0 tw:h-[6.5rem] tw:min-h-[6.5rem] tw:tl:h-[3.5rem]
      tw:tl:min-h-[3.5rem]`"
    >
      <q-toolbar
        class="tw:pr-0 tw:pl-0 tw:tl:pl-[0.75rem] tw:tl:h-[3.5rem]
        tw:tl:min-h-[3.5rem] tw:h-[6.5rem] tw:min-h-[6.5rem] tw:flex
        tw:tl:flex-row tw:flex-col tw:tl:justify-between tw:items-start! tw:tl:items-center"
      >
        <div class="tw:flex tw:flex-row tw:items-center">
          <q-btn
            flat
            dense
            round
            color="black"
            class="tw:mt-[0.6rem]"
            icon="menu"
            aria-label="Menu"
            @click.prevent="toggleLeftDrawer"
          >
            <q-badge
              v-if="hasUnreadNotificationFlag"
              :class="`tw:bg-red-2 tw:top-[-1px]
              tw:px-[5px] tw:text-[10px] tw:leading-[10px]
              tw:min-h-[10px] tw:min-w-[10px]`"
              rounded
              floating
            />
          </q-btn>

          <q-toolbar-title>
            <div class="tw:flex tw:justify-between">
              <div
                class="tw:tl:flex-1 tw:text-[15px] tw:leading-[1.5]
                tw:dt:text-[32px] tw:font-[700] tw:text-[#333333] tw:flex tw:flex-wrap tw:tl:flex-row tw:flex-col tw:tl:justify-between"
              >
                <div
                  class="tw:w-full tw:cursor-pointer tw:flex tw:items-center"
                  @click.prevent="
                    router.push({
                      name: 'fisheriesDepartmentAdmin',
                      query: { page: 1, rowsPerPage: 10 },
                    })
                  "
                >
                  {{ envPrefix }}シラスウナギトレーサビリティ支援システム
                </div>
              </div>
            </div>
          </q-toolbar-title>
        </div>
        <div
          :class="`tw:w-full tw:tl:w-[309px] tw:flex height-header
          tw:items-center tw:justify-center tw:text-white tw:text-center
          tw:text-[15px] tw:leading-[1.5] tw:dt:text-[32px] tw:font-[700] tw:bg-[#00BACF]`"
        >
          水産庁
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered behavior="mobile">
      <q-list>
        <q-item-label header>
          <img
            :src="CloseSvg"
            alt=""
            class="tw:w-[2.5rem] tw:h-[2.5rem] tw:hover:cursor-pointer"
            @click.prevent="toggleLeftDrawer"
          />
        </q-item-label>

        <div class="tw:px-4 tw:font-normal tw:pb-2 tw:border-b tw:border-gray">
          <div class="tw:py-1 tw:text-m-design tw:font-bold tw:truncate">
            {{ user?.name }}
          </div>
          <div>
            <div
              class="tw:w-full tw:text-m-design tw:text-blue tw:hover:cursor-pointer tw:underline"
              @click.prevent="logout"
            >
              ログアウト
            </div>
          </div>
        </div>
        <EssentialLink />

        <div class="tw:px-4 tw:pb-2 tw:mt-10">
          <span class="tw:text-s-design">お問い合わせ：</span>
          <div>
            <span class="tw:text-xxs-design">一般社団法人</span>
            <span class="tw:text-[1.875rem] tw:font-bold tw:ml-3"
              >全日本持続的養鰻機構</span
            >
          </div>
          <div>
            <span class="tw:text-[1.875rem]">Tel.</span>
            <a
              href="tel:03-5797-7690"
              class="tw:text-blue-3 hover:tw:opacity-80 tw:text-[1.875rem] tw:tl:hidden"
            >
              03-5797-7690
            </a>
            <span class="tw:text-[1.875rem] tw:hidden tw:tl:inline">
              03-5797-7690</span
            >
          </div>
        </div>
      </q-list>
    </q-drawer>

    <div class="tw:w-screen tw:flex-1">
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        class="tw:w-full tw:h-full tw:bg-[#F1F7FF]"
        ref="scrollAreaRef"
      >
        <q-page-container
          class="tw:pt-0 tw:bg-[#F7F7F9] tw:flex tw:justify-center tw:flex-1"
        >
          <div
            class="tw:w-full tw:px-4 tw:tl:pt-[4.5rem] tw:pt-[10rem] tw:flex tw:flex-col"
          >
            <div
              class="tw:flex tw:justify-between"
              v-if="router.currentRoute.value?.meta?.isBreadcrumb"
            >
              <!-- Breadcrumbs -->
              <q-breadcrumbs
                class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:flex-1"
                active-color="tw:text-blue-3"
              >
                <template v-slot:separator>
                  <q-icon
                    size="1.5em"
                    name="chevron_right"
                    class="tw:text-[#7E8093]"
                  />
                </template>
                <q-breadcrumbs-el
                  class="tw:cursor-pointer"
                  label="トップ"
                  @click.prevent="gotoPage('home')"
                />
                <q-breadcrumbs-el
                  v-if="router.currentRoute.value.meta.preTitle"
                  class="tw:cursor-pointer"
                  :label="`${router.currentRoute.value.meta.preTitle}`"
                  @click.prevent="
                    gotoPage(`${router.currentRoute.value.meta.preRoute}`)
                  "
                />
                <q-breadcrumbs-el
                  v-if="router.currentRoute.value.meta.mainRoute"
                  class="tw:cursor-pointer"
                  :label="`${router.currentRoute.value.meta.title}`"
                  @click.prevent="goBackWithQuery"
                />
                <q-breadcrumbs-el
                  v-else
                  class="tw:cursor-pointer"
                  :label="`${router.currentRoute.value.meta.title}`"
                />

                <div
                  v-if="router.currentRoute.value.meta.tag"
                  class="tw:ml-2 tw:text-[#333333] tw:flex tw:items-center tw:justify-center"
                >
                  <span class="tw:text-[#7E8093] tw:pb-1 tw:pl-4 tw:pr-5"
                    >|</span
                  >
                  {{
                    router.currentRoute.value.meta.tag
                      ? router.currentRoute.value.meta.tag
                      : ""
                  }}
                </div>
              </q-breadcrumbs>
            </div>
            <div class="tw:flex-1">
              <router-view />
            </div>
          </div>
        </q-page-container>
      </q-scroll-area>
    </div>
  </q-layout>
</template>

<script setup>
import { computed, onMounted, provide, ref, watch } from 'vue';
import EssentialLink from 'components/EssentialLink.vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from 'stores/auth-store';
import { SHOW_DEFAULT_SCAN_QR_ENUM } from 'src/helpers/constants';
import authService from 'services/auth.service';
import { useLocalStorage } from 'composables/localstorage';
import PolicySvg from 'assets/PolicySvg.svg';
import DescriptionSvg from 'assets/DescriptionSvg.svg';
import LicenseSvg from 'assets/LicenseSvg.svg';
import GradingSvg from 'assets/GradingSvg.svg';
import CloseSvg from 'assets/CloseSvg.svg';

const envPrefix = ref('');

const { setLoading, setLoadingManual, setSettingUser } = useAppStore();
const { settingUser, hasUnreadNotificationFlag } = storeToRefs(useAppStore());
const { adminProxyUserSignOut } = useAuthStore();
const { user } = storeToRefs(useAuthStore());
const { previousQuery } = storeToRefs(useAppStore());
const router = useRouter();
const route = useRoute();
const scrollAreaRef = ref(null);
const linksList = computed(() => [
  {
    title: 'プライバシーポリシー',
    link: 'privacyPolicyFA',
    marginTop: true,
    svg: PolicySvg,
    role: [],
  },
  {
    title: 'マニュアル',
    external: true,
    link: `${window.location.origin}/document/fasite/manual.pdf`,
    svg: DescriptionSvg,
    role: [],
  },
  {
    title: 'ライセンス',
    link: 'licenseFA',
    svg: LicenseSvg,
    role: [],
  },
  {
    title: '利用規約',
    link: 'termsOfUserFA',
    svg: GradingSvg,
    marginBot: true,
    role: [],
  },
]);

const leftDrawerOpen = ref(false);

provide('items', linksList);

const toggleLeftDrawer = async () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const gotoPage = async name => {
  await router.push({ name });
};

const logout = async () => {
  setLoadingManual(true);
  setLoading(true);
  const { cacheAdminToken } = useLocalStorage();

  // if cacheAdminToken is not null, it means user is in proxy mode (admin login as user)
  if (cacheAdminToken.value) {
    adminProxyUserSignOut();
    router.push({
      name: 'adminUserManager',
    });
  } else {
    await authService.logout();
    if (
      settingUser.value?.qr_scan_init === SHOW_DEFAULT_SCAN_QR_ENUM.USE_SCAN
    ) {
      setSettingUser(null);
      router.push({
        name: 'loginQrScan',
      });
    } else {
      setSettingUser(null);
      router.push({
        name: 'loginUserId',
      });
    }
  }

  setLoading(false);
  setLoadingManual(false);
};

const animateScroll = () => {
  scrollAreaRef.value.setScrollPosition('vertical', 0);
};

router.beforeEach((to, from) => {
  const { setPreviousQuery } = useAppStore();

  setPreviousQuery(from.query);

  return true;
});

const goBackWithQuery = () => {
  const routeName = router.currentRoute.value.meta.mainRoute;
  router.push({
    name: routeName,
    query: previousQuery.value || {},
  });
};

watch(
  () => route.name,
  () => {
    animateScroll();
  }
);

// style scroll
const thumbStyle = {
  right: '4px',
  borderRadius: '5px',
  backgroundColor: '#737373',
  width: '2px',
  opacity: 0.75,
};
const barStyle = {
  right: '2px',
  borderRadius: '9px',
  backgroundColor: '#737373',
  width: '4px',
  opacity: 0.2,
};

onMounted(() => {
  envPrefix.value = `${process.env.PREFIX}`;
});
</script>
<style scoped>
:deep(aside.q-drawer) {
  width: 30rem !important;
}

:deep(.q-item__label) {
  line-height: initial !important;
}

:deep(.q-scrollarea__content) {
  display: flex;
}
</style>
