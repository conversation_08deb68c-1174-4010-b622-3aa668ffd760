<template>
  <ExportShipmentPDF />
  <div class="tw:flex tw:flex-col tw:h-full">
    <h2 class="tw:text-l-design tw:font-bold tw:text-center tw:mb-5">
      輸出向け取引報告一覧
    </h2>
    <ExpansionSearchItem v-model:expanded="isSearchExpanded">
      <SearchForm />
    </ExpansionSearchItem>
    <div
      v-if="!checkConditionIsEmpty(searchQueryConditions)"
      class="tw:mt-4 tw:text-xs-design tw:flex tw:items-center tw:gap-4 tw:flex-wrap"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]"
        >検索条件</span
      >
      <span
        v-if="searchQueryConditions.code"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchQueryConditions.code }}
      </span>
      <span
        v-if="searchQueryConditions.enterpriseName"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchQueryConditions.enterpriseName }}
      </span>
      <span
        v-if="searchQueryConditions.startDate || searchQueryConditions.endDate"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{
          searchQueryConditions.startDate
            ? FORMAT_DATE(searchQueryConditions.startDate)
            : ''
        }}
        ~
        {{
          searchQueryConditions.endDate
            ? FORMAT_DATE(searchQueryConditions.endDate)
            : ''
        }}
      </span>
    </div>

    <q-card class="tw:flex tw:p-4 tw:my-4 tw:flex-col tw:h-full">
      <!-- filter end -->
      <!-- sort btn start -->
      <div
        class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:items-start tw:border-neutral-500 tw:pb-3"
      >
        <div class="tw:flex tw:tl:flex-row tw:flex-col tw:space-y-4">
          <div
            class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]"
          >
            <span class="tw:text-xs-design">表示件数</span>
            <PageSizeDropdownSP v-model="pagination.rowsPerPage" />
          </div>
        </div>
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:pt-5 tw:tl:pt-0 tw:w-full tw:tl:w-auto"
        >
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block">
        <q-table
          class="tw:border tw:border-[#D2D2D2] tw:border-collapse"
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="pagination"
        >
          <template v-slot:header="props">
            <q-tr
              :props="props"
              class="tw:text-s-design tw:border tw:border-[#D2D2D2]"
              :class="`tw:bg-[#E2E3EA]`"
            >
              <q-th
                @click="handleClickSort(props.cols[0].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2] tw:dt:w-[20rem] tw:tl:w-[20rem]"
              >
                {{ props.cols[0].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc'
                      ? 'north'
                      : 'south'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[1].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc'
                      ? 'north'
                      : 'south'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[2].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:dt:w-[12rem] tw:tl:w-[12rem] tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[2].label }}
                <q-icon
                  @click="handleClickSort(props.cols[2].name)"
                  v-if="getSortOrder(props.cols[2].name)"
                  :name="
                    getSortOrder(props.cols[2].name) === 'asc'
                      ? 'north'
                      : 'south'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                class="tw:text-left tw:font-bold tw:text-s-design tw:border-r tw:border-[#D2D2D2] tw:dt:w-[5rem] tw:tl:w-[5rem]"
              >
                {{ props.cols[3].label }}
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr class="tw:w-full" :props="props">
              <q-td
                key="code"
                :props="props"
                class="text-center tw:text-[1.6rem] tw:border-r tw:border-[#D2D2D2]"
              >
                <div
                  class="text-left tw:truncate tw:dt:w-[20rem] tw:tl:w-[20rem]"
                >
                  {{ maskCodeString(props.row.code) }}
                </div>
              </q-td>
              <q-td
                key="name"
                :props="props"
                class="text-left tw:border-r tw:border-[#D2D2D2] tw:text-[1.6rem]"
              >
                <div
                  :class="`tw:truncate tw:tl:max-w-[580px] tw:dt:max-w-[660px]`"
                >
                  {{ props.row.name }}
                </div>
              </q-td>
              <q-td
                key="date"
                :props="props"
                class="tw:border-r tw:border-[#D2D2D2] tw:text-[1.6rem]"
              >
                <div class="tw:truncate tw:dt:w-[12rem] tw:tl:w-[12rem]">
                  {{ FORMAT_DATE(props.row.date) }}
                </div>
              </q-td>
              <q-td
                key="download"
                :props="props"
                class="tw:border-r tw:border-[#D2D2D2] tw:text-[1.6rem]"
              >
                <div
                  class="tw:truncate tw:text-center tw:text-[#004AB9]"
                  @click="handleClickDownload(props.row)"
                >
                  <q-btn flat round icon="save_alt" size="1rem" class="tw:pt-0.5" />
                </div>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div class="tw:w-full tw:text-center tw:text-m-design">
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <!-- start table for smartphone -->
      <div class="tw:block tw:tl:hidden">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-5 tw:mt-4"
          card-class="tw:w-full"
          :rows="sortedRows"
          row-key="index"
          hide-pagination
          hide-header
          :columns="columns"
          v-model:pagination="pagination"
        >
          <template v-slot:item="props">
            <div class="tw:w-full">
              <q-card flat bordered>
                <q-card-section class="tw:text-left tw:cursor-pointer">
                  <div
                    class="tw:flex-1 tw:flex tw:items-center tw:justify-between"
                  >
                    <div>
                      <strong class="tw:text-m-design tw:text-[#004AB9]">
                        {{ maskCodeString(props.row.code) }}</strong
                      >
                      <div class="tw:text-[#333333] tw:text-m-design">
                        <span> {{ props.row.name }}</span>
                      </div>
                      <div class="tw:text-[#333333] tw:text-m-design">
                        <span>{{ FORMAT_DATE(props.row.date) }}</span>
                      </div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </template>
        </q-table>
      </div>
    </q-card>
  </div>
</template>
<script setup>
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import ExportShipmentPDF from 'src/components/pdf/ExportShipmentPDF.vue';
import { useLocalStorage } from 'composables/localstorage';
import useValidate from 'composables/validate';
import { isEqual } from 'lodash';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import { FORMAT_DATE, maskCodeString, FORMAT_DATE_TIME_CSV } from 'src/helpers/common';
import searchExportShipmentCondition from 'src/schemas/fa/searchExportShipmentCondition.schema';
import exportFaService from 'src/shared/services/fa/export.fa.service';
import { computed, nextTick, onMounted, provide, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import MESSAGE from 'src/helpers/message';
import toast from 'src/shared/utilities/toast';
import html2pdf from 'html2pdf.js';
import shippingService from 'src/shared/services/shipping.service';

import SearchForm from './components/SearchForm.vue';

// #region variable
const { validateData, errors } = useValidate();
const hasDiffPageIndex = ref(false);
const router = useRouter();
const sortBySelectedSP = ref('');
const isSearchExpanded = ref(false);
const { listPageQueryParams } = useLocalStorage();
// because quasar does not support mutli sort, we use this variable to manage sort conditions
const multiSortConditions = ref([]);
// default pagination for table
const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: 1,
  rowsPerPage: listPageQueryParams.value?.partner?.rowsPerPage || 10,
});

// for manage state of search form
const searchForm = ref({
  code: '',
  enterpriseName: '',
  startDate: '',
  endDate: '',
});
// for manage state of search form data (validated)
const searchFormData = ref({
  code: '',
  enterpriseName: '',
  startDate: '',
  endDate: '',
});
// for table columns structure
const columns = [
  {
    name: 'code',
    label: '荷口番号',
    field: 'code',
    sortable: true,
    align: 'left',
  },
  {
    name: 'name',
    label: '届出事業者名',
    field: 'name',
    sortable: true,
    align: 'left',
  },
  {
    name: 'date',
    label: '出荷登録日',
    field: 'date',
    sortable: true,
  },
  {
    name: 'download',
    label: 'DL',
    field: 'download',
    sortable: true,
  },
];
const partnerHybridList = ref([]);
const pageIndex = ref(1);
const totalPage = ref(1);
const exportShipmentPDFProvideData = ref({
  rootOrigins: [],
  distributionOrigins: [],
  catchingOrigins: [],
});
// #region variable

const mappingData = (shipping = {}, lstPdf = []) => {
  const { shippingInfo } = shipping;
  lstPdf.push(shipping);
  const lst = [];
  if (!shippingInfo) {
    return;
  }
  shippingInfo.forEach(value => {
    mappingData(value, lst);
  });

  if (lst.length) {
    lstPdf.push(lst);
  }
};

const getDataExport = async id => {
  const result = await shippingService.exportShippingDetail({
    id,
  });

  const lstPdf = [];
  mappingData(result.payload, lstPdf);
  lstPdf.forEach(value => {
    if (value.shippingInfo) {
      value.sum = value.shippingInfo.reduce((acc, curr) => +acc + (+Number(curr) || 0), 0);
    }
  });
  exportShipmentPDFProvideData.value = {
    distributionOrigins: [lstPdf],
  };

  nextTick(async () => {
    const pdfFileData = document.getElementById('export-shipment-pdf-file-data');
    html2pdf()
      .from(pdfFileData)
      .set({
        filename: `輸出向け取引記録_${FORMAT_DATE_TIME_CSV()}.pdf`,
        margin: [0.5, 0.3, 0.6, 0.3],
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 4 },
        jsPDF: { unit: 'in', format: 'a3', orientation: 'landscape' },
      })
      .save();
    toast.access(MESSAGE.MSG_DOWNLOAD_EXPORTPDF_INFO);
  });
};

// #region function
const handleClickDownload = async row => {
  await getDataExport(row.id);
};

const getExportShipmentList = async () => {
  // do not pass sortBy and descending to server because sorting is done on client side
  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
    sortBy: undefined,
    descending: undefined,
  };
  const response = await exportFaService.getExportShipmentList(query);
  if (response.code === 0) {
    partnerHybridList.value = response.payload.items.map(item => ({
      id: item.id,
      code: item.code,
      name: item.starting_user.name,
      date: item.shipping_date,
    }));

    // calculate total page
    totalPage.value = Math.ceil(
      (response.payload.total_item ?? 1) / pagination.value.rowsPerPage
    );
    // check if page index is different from the value server return
    // if different, replace query to the new page index (value server return)
    if (response.payload.page !== pageIndex.value) {
      hasDiffPageIndex.value = true;
      router.replace({
        query: {
          ...pagination.value,
          sortBy: formatSortConditionsToQuery(),
          descending: undefined,
          page: response.payload.page,
          ...formatSearchFormToQuery(searchForm.value),
        },
      });
    }
  }
};

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }

  router.push({
    query: {
      ...pagination.value,
      sortBy: formatSortConditionsToQuery(),
      descending: undefined,
      ...formatSearchFormToQuery(searchFormData.value),
    },
  });
};
// #endregion function

// #region helper function
const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const formatSearchFormToQuery = formData => ({
  code: formData.code?.trim() || undefined,
  enterpriseName: formData.enterpriseName?.trim() || undefined,
  startDate: formData.startDate || undefined,
  endDate: formData.endDate || undefined,
});

const formatSortConditionsToQuery = () =>
  multiSortConditions.value
    .map(item => `${item.key}.${item.order}`)
    .join(',') || undefined;

const checkConditionIsEmpty = condition =>
  Object.values(condition).every(
    value => value === '' || value === undefined || value === null
  );
// #endregion helper function

// #region computed
const sortedRows = computed(() => {
  const rows = [...partnerHybridList.value];
  return rows.sort((a, b) => {
    for (const { key, order } of multiSortConditions.value) {
      const aVal = a[key] ?? '';
      const bVal = b[key] ?? '';
      // Normalize comparison
      if (aVal < bVal) {
        return order === 'asc' ? -1 : 1;
      }
      if (aVal > bVal) {
        return order === 'asc' ? 1 : -1;
      }
    }

    return 0;
  });
});

const searchQueryConditions = computed(() => ({
  code: router.currentRoute.value.query.code,
  enterpriseName: router.currentRoute.value.query.enterpriseName,
  startDate: router.currentRoute.value.query.startDate,
  endDate: router.currentRoute.value.query.endDate,
}));
// #endregion computed

// #region watch
watch(
  () => pagination.value.rowsPerPage,
  () => {
    if (pagination.value.rowsPerPage) {
      pageIndex.value = 1;
      router.push({
        query: {
          ...pagination.value,
          sortBy: formatSortConditionsToQuery(),
          descending: undefined,
          page: 1,
          ...formatSearchFormToQuery(searchFormData.value),
        },
      });
    }
  }
);

provide('exportShipmentPDFProvideData', exportShipmentPDFProvideData);

watch(
  () =>
    router.currentRoute.value.query.code +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.startDate +
    router.currentRoute.value.query.endDate,
  async () => {
    if (router.currentRoute.value.query.rowsPerPage) {
      listPageQueryParams.value.partner = {
        rowsPerPage: +router.currentRoute.value.query.rowsPerPage,
      };
    }
    pageIndex.value = +router.currentRoute.value.query.page;

    pagination.value = {
      ...pagination.value,
      page: +router.currentRoute.value.query.page,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage,
    };
    searchForm.value = {
      code: router.currentRoute.value.query.code,
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      startDate: router.currentRoute.value.query.startDate,
      endDate: router.currentRoute.value.query.endDate,
    };
    searchFormData.value = {
      ...searchForm.value,
    };

    // Use this for sm specific case
    // if hasDiffPageIndex is false, get data
    // if hasDiffPageIndex is true, do nothing because the query has been replaced
    if (!hasDiffPageIndex.value) {
      await getExportShipmentList();
    } else {
      hasDiffPageIndex.value = false;
    }
  }
);
watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.page,
  () => {
    pageIndex.value = +router.currentRoute.value.query.page || 1;
    pagination.value = {
      ...pagination.value,
      page: +router.currentRoute.value.query.page,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage,
    };
    if (router.currentRoute.value.query.sortBy) {
      multiSortConditions.value = router.currentRoute.value.query.sortBy
        .split(',')
        .map(item => {
          const [key, order] = item.split('.');
          return { key, order: order || 'asc' };
        });
    } else {
      multiSortConditions.value = [];
    }
  }
);
// #endregion watch

// #region provide
const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      code: '',
      enterpriseName: '',
      startDate: '',
      endDate: '',
    };
    errors.value = {};
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getExportShipmentList();
      this.isClose = true;
    } else {
      const payload = {
        code: searchForm.value.code?.trim() || undefined,
        enterpriseName: searchForm.value.enterpriseName?.trim() || undefined,
        startDate: searchForm.value.startDate || undefined,
        endDate: searchForm.value.endDate || undefined,
      };

      const validate = validateData(searchExportShipmentCondition, payload);
      if (validate) {
        pageIndex.value = 1;
        await router.push({
          query: {
            ...pagination.value,
            sortBy: formatSortConditionsToQuery(),
            descending: undefined,
            page: 1,
            ...formatSearchFormToQuery(searchForm.value),
          },
        });
        this.isClose = true;
      } else {
        this.isClose = false;
      }
    }
  },
  isClose: false,
  title: '輸出向け取引報告をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: 'ユーザーID順', value: 'userCode' },
    { label: '事業者名順', value: 'name' },
  ],
  handleClickSortByItem: async option => {
    multiSortConditions.value = [{ key: option.value, order: 'asc' }];
    await router.push({
      query: {
        ...pagination.value,
        sortBy: formatSortConditionsToQuery(),
        descending: undefined,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        sortBy: formatSortConditionsToQuery(),
        descending: undefined,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);

onMounted(async () => {
  isSearchExpanded.value = false;
  if (!router.currentRoute.value?.query?.page) {
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        descending: undefined,
        sortBy: formatSortConditionsToQuery(),
      },
    });

    if (checkConditionIsEmpty(searchQueryConditions.value)) {
      await getExportShipmentList();
    }
  } else {
    pageIndex.value = +router.currentRoute.value.query.page;
    pagination.value = {
      ...pagination.value,
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage || 10,
    };
    searchForm.value = { ...searchQueryConditions.value };
    searchFormData.value = { ...searchQueryConditions.value };
    if (
      // sortBy=name.asc,type.desc
      router.currentRoute.value.query.sortBy
    ) {
      multiSortConditions.value = router.currentRoute.value.query.sortBy
        .split(',')
        .map(item => {
          const [key, order] = item.split('.');
          return { key, order: order || 'asc' };
        });
      if (multiSortConditions.value.length === 1) {
        const sortByOption = sortByDropdownProvideData.sortByOptions.find(
          option => option.value === multiSortConditions.value[0].key
        );
        sortBySelectedSP.value = sortByOption;
      }
    }
    await getExportShipmentList();
  }
});
</script>
