const BaseService = require('../base/serviceFn');
const { ControlledException } = require('../base/errors');
const {
  OPTION_TYPE,
  PARTNER_TYPE_ENUM,
  USER_STATUS_ENUM,
  ALLOC_STATUS,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
} = require('../helpers/enum');
const { s3Client } = require('../utils/s3client');
const { getSignedUrl: getSignedUrlS3 } = require('@aws-sdk/s3-request-presigner');
const { MESSAGE } = require('../utils/message');
const { GetObjectCommand } = require('@aws-sdk/client-s3');
const Env = require('../boot/env');
const dayjs = require('dayjs');

class CommonService extends BaseService {
  // ====== 1. Private method ==========

  // ====== 2. Public method ==========
  async getOptions(user, query) {
    const connect = this.DB.READ;
    const { type } = query;
    switch (type) {
      case OPTION_TYPE.USER_SHIPPER: {
        // get all partners of user with partner_type = SHIPPER
        const partnerList = await connect.partners.findMany({
          where: {
            user_id: user.id,
            hidden_flag: false,
            partner_type: {
              has: PARTNER_TYPE_ENUM.SHIPPER,
            },
            delete_flag: false,
            partner: {
              status: {
                not: USER_STATUS_ENUM.PENDING,
              },
            },
          },
          select: {
            partner: {
              select: {
                id: true,
                name: true,
                name_kana_nospace: true,
                enterprise_id: true,
                enterprise: {
                  select: {
                    enterprise_name: true,
                    enterprise_code: true,
                  },
                },
                enterprise_type: true,
                staff_type: true,
              },
            },
          },
          orderBy: {
            partner: {
              name: 'asc',
            },
          },
        });
        const result = partnerList.map((item) => item.partner);

        // format data to return
        const userList = result.map((item) => ({
          value: item.id,
          label: item.name,
          description: item.enterprise.enterprise_name,
          enterprise_id: item.enterprise_id,
          enterprise_code: item.enterprise.enterprise_code,
          enterprise_type: item.enterprise_type,
        }));

        // check if field enable_export_function
        const enableExportFunction = await connect.users.findFirst({
          where: {
            id: user.id,
            delete_flag: false,
          },
          select: {
            enable_export_function: true,
          },
        });
        if (enableExportFunction?.enable_export_function) {
          const foreignFarmUsers = await connect.users.findMany({
            where: {
              enterprise_type: ENTERPRISE_TYPE_ENUM.FOREIGN,
              status: {
                not: USER_STATUS_ENUM.PENDING,
              },
              delete_flag: false,
            },
            select: {
              id: true,
              name: true,
              enterprise_id: true,
              enterprise: {
                select: {
                  enterprise_name: true,
                  enterprise_code: true,
                },
              },
              enterprise_type: true,
              staff_type: true,
            },
          });

          // format foreign farm users
          const foreignFarmUsersList = foreignFarmUsers.map((item) => ({
            value: item.id,
            label: item.name || item.enterprise.enterprise_name,
            description: '',
            enterprise_id: item.enterprise_id,
            enterprise_code: item.enterprise.enterprise_code,
            enterprise_type: item.enterprise_type,
          }));

          return this.SUCCESS(userList.concat(foreignFarmUsersList).flat());
        }

        return this.SUCCESS(userList.flat());
      }
      case OPTION_TYPE.USER_SUPPLIER: {
        // get all partners of user with partner_type = SUPPLIER
        const partnerList = await connect.partners.findMany({
          where: {
            user_id: user.id,
            hidden_flag: false,
            partner_type: {
              has: PARTNER_TYPE_ENUM.SUPPLIER,
            },
            delete_flag: false,
            partner: {
              status: {
                not: USER_STATUS_ENUM.PENDING,
              },
            },
          },
          select: {
            partner: {
              select: {
                id: true,
                name: true,
                name_kana_nospace: true,
                enterprise_id: true,
                enterprise: {
                  select: {
                    enterprise_name: true,
                    enterprise_code: true,
                  },
                },
                enterprise_type: true,
                staff_type: true,
              },
            },
          },
          orderBy: {
            partner: {
              name: 'asc',
            },
          },
        });
        const result = partnerList.map((item) => item.partner);

        // format data to return
        const userList = result.map((item) => ({
          value: item.id,
          label: item.name,
          description: item.enterprise.enterprise_name,
          enterprise_id: item.enterprise_id,
          enterprise_code: item.enterprise.enterprise_code,
          enterprise_type: item.enterprise_type,
        }));

        // check if user is DISTRIBUTE_ENTERPRISE
        if (
          user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
          user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE
        ) {
          const foreignFarmUsers = await connect.users.findMany({
            where: {
              enterprise_type: {
                in: [ENTERPRISE_TYPE_ENUM.FOREIGN, ENTERPRISE_TYPE_ENUM.FARM],
              },
              status: {
                not: USER_STATUS_ENUM.PENDING,
              },
              delete_flag: false,
            },
            select: {
              id: true,
              name: true,
              enterprise_id: true,
              enterprise: {
                select: {
                  enterprise_name: true,
                  enterprise_code: true,
                },
              },
              enterprise_type: true,
              staff_type: true,
            },
          });

          // format foreign farm users
          const foreignFarmUsersList = foreignFarmUsers.map((item) => ({
            value: item.id,
            label: item.name || item.enterprise.enterprise_name,
            description: '',
            enterprise_id: item.enterprise_id,
            enterprise_code: item.enterprise.enterprise_code,
            enterprise_type: item.enterprise_type,
          }));

          return this.SUCCESS(userList.concat(foreignFarmUsersList).flat());
        }

        return this.SUCCESS(userList.flat());
      }
      default:
        throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
  }

  async getSignedUrlS3(isAdmin = true) {
    const getObjectCommand = new GetObjectCommand({
      Bucket: Env.S3_BUCKET_NAME_DOCUMENT,
      Key: `document/document/${isAdmin ? 'adminsite' : 'usersite'}/manual.pdf`,
    });
    const url = await getSignedUrlS3(s3Client, getObjectCommand, {
      expiresIn: 3600,
    });
    return this.SUCCESS({ url });
  }

  async getCodeSuffix(user, query) {
    const connect = this.DB.READ;
    const { code_group_key } = query;

    // Get the usage statistics for each code_suffix
    const suffixStats = await connect.code_suffixes_alloc.groupBy({
      by: ['code_suffix'],
      where: {
        code_group_key,
      },
      _count: {
        code_suffix: true,
      },
    });

    // Generate all possible suffixes from 000 to 999
    // This will create an array of strings like ['000', '001', ..., '999']
    const allSuffixes = Array.from({ length: 1000 }, (_, i) => i.toString().padStart(3, '0'));

    // Map the usage statistics to a more accessible format
    const usageMap = new Map(suffixStats.map((s) => [s.code_suffix, s._count.code_suffix]));

    // Find the first unused suffix
    let selectedSuffix = allSuffixes.find((suffix) => !usageMap.has(suffix));

    // If all suffixes are used, find the one with the minimum count
    if (!selectedSuffix) {
      let minCount = Infinity;
      for (const suffix of allSuffixes) {
        const count = usageMap.get(suffix) ?? 0;
        if (count < minCount) {
          minCount = count;
          selectedSuffix = suffix;
        }
      }
    }

    if (!selectedSuffix) throw new ControlledException();

    // Create a new record for the selected suffix
    const newRecord = await connect.code_suffixes_alloc.create({
      data: {
        code_group_key,
        code_suffix: selectedSuffix,
        alloc_status: ALLOC_STATUS.DRAFT,
        created_by_id: user.id,
        created_on: dayjs().toDate(),
        latest_updated_by_id: user.id,
        latest_updated_on: dayjs().toDate(),
      },
    });
    return this.SUCCESS(newRecord);
  }
}

module.exports = CommonService;
