const { PrismaClient } = require('@prisma/client');
const { faker } = require('@faker-js/faker/locale/ja');

const { hash } = require('../utils/crypto');
const { ROLES_ENUM, ENTERPRISE_TYPE_ENUM } = require('../helpers/enum');
const prisma = new PrismaClient();

async function main() {
  const DEFAULT_PASSWORD = await hash('Ab123456a@');
  await prisma.$transaction(async (tx) => {
    // clear all data in the database
    await tx.readers.deleteMany();
    await tx.notifications.deleteMany();
    await tx.provinces.deleteMany();
    await tx.inventories_history.deleteMany();
    await tx.inventories.deleteMany();
    await tx.the_origins.deleteMany();
    await tx.licenses.deleteMany();
    await tx.settings.deleteMany();
    await tx.users.deleteMany();
    await tx.partners.deleteMany();
    await tx.enterprises.deleteMany();

    // reset sequence for all tables
    await tx.$executeRaw`ALTER SEQUENCE "public"."readers_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."notifications_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."provinces_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."settings_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."inventories_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."inventories_history_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."the_origins_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."licenses_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."users_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."enterprises_id_seq" RESTART WITH 1`;
    await tx.$executeRaw`ALTER SEQUENCE "public"."partners_id_seq" RESTART WITH 1`;

    //add sample data for province table
    await tx.provinces.createMany({
      data: [
        {
          name: '愛知県',
          delete_flag: false,
        },
        {
          name: '秋田県',
          delete_flag: false,
        },
        {
          name: '青森県',
          delete_flag: false,
        },
        {
          name: '千葉県',
          delete_flag: false,
        },
      ],
    });

    // add sample data for enterprises table
    const enterpriseData = [
      {
        enterprise_code: faker.string.numeric(7),
        enterprise_name: 'Enterprise 1',
        enterprise_name_kana: 'Enterprise 1 kana',
        type: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        delete_flag: false,
        enterprise_name_nospace: 'Enterprise1',
        enterprise_name_kana_nospace: 'Enterprise1kana'
      },
      {
        enterprise_code: faker.string.numeric(7),
        enterprise_name: 'Enterprise 2',
        enterprise_name_kana: 'Enterprise 2 kana',
        type: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
        delete_flag: false,
        enterprise_name_nospace: 'Enterprise2',
        enterprise_name_kana_nospace: 'Enterprise2kana'
      },
      {
        enterprise_code: faker.string.numeric(7),
        enterprise_name: 'Enterprise 3',
        enterprise_name_kana: 'Enterprise 3 kana',
        type: ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
        delete_flag: false,
        enterprise_name_nospace: 'Enterprise3',
        enterprise_name_kana_nospace: 'Enterprise3kana'
      },
    ];
    await tx.enterprises.createMany({
      data: enterpriseData,
    });

    // add sample data for licenses table
    await tx.licenses.createMany({
      data: [
        {
          license_code: faker.string.alpha(16),
          user_id: 1,
          expiry_date: faker.date.between({
            from: '2025-01-01',
            to: '2025-10-01',
          }),
          delete_flag: false,
        },
        {
          license_code: faker.string.alpha(16),
          user_id: 2,
          expiry_date: faker.date.between({
            from: '2025-01-01',
            to: '2025-10-01',
          }),
          delete_flag: false,
        },
        {
          license_code: faker.string.alpha(16),
          user_id: 3,
          expiry_date: faker.date.between({
            from: '2025-01-01',
            to: '2025-10-01',
          }),
          delete_flag: false,
        },
      ],
    });

    // add sample data for users table
    await tx.users.createMany({
      data: [
        {
          user_code: enterpriseData[0].enterprise_code + '-0000',
          enterprise_id: 1,
          qr_code: faker.string.alpha(10),
          phone: '09012345678',
          password: DEFAULT_PASSWORD,
          delete_flag: false,
          role: ROLES_ENUM.CATCH_STAFF,
          license_id: 1,
        },
        {
          user_code: enterpriseData[1].enterprise_code + '-0000',
          enterprise_id: 2,
          qr_code: faker.string.alpha(10),
          phone: '09012345678',
          password: DEFAULT_PASSWORD,
          delete_flag: false,
          role: ROLES_ENUM.DISTRIBUTE_ENTERPRISE,
          license_id: 2,
        },
        {
          user_code: enterpriseData[2].enterprise_code + '-0000',
          enterprise_id: 3,
          qr_code: faker.string.alpha(10),
          phone: '09012345678',
          password: DEFAULT_PASSWORD,
          delete_flag: false,
          role: ROLES_ENUM.EEL_FARMING_ENTERPRISE,
          license_id: 3,
        },
      ],
    });

    // add sample data for settings table
    await tx.settings.createMany({
      data: [
        {
          user_id: 1,
          unit_type: 3, // both
          unit_per_gram: 0.2,
          price_per_gram: [1000, 2000, 3000],
          destination_id: 2,
          display_shipment_weight: true,
          display_actual_received: true,
          display_proxy_function: true,
          display_import_export_function: false,
          qr_scan_init: 1,
          delete_flag: false,
        },
        {
          user_id: 2,
          unit_type: 1, // only weight
          unit_per_gram: 0.2,
          price_per_gram: [1000, 2000, 3000],
          destination_id: 3,
          display_shipment_weight: true,
          display_actual_received: true,
          display_proxy_function: true,
          display_import_export_function: false,
          qr_scan_init: 1,
          delete_flag: false,
        },
        {
          user_id: 3,
          unit_type: 3, // both
          unit_per_gram: 0.2,
          price_per_gram: [1000, 2000, 3000],
          destination_id: 2,
          display_shipment_weight: true,
          display_actual_received: true,
          display_proxy_function: true,
          display_import_export_function: false,
          qr_scan_init: 1,
          delete_flag: false,
        },
      ],
    });

    // add sample data for partners table
    await tx.partners.createMany({
      data: [
        // enterprise 1 (catch enterprise) is a partner of enterprise 2 (distribute enterprise)
        {
          enterprise_id: 1,
          partner_id: 2,
          hidden_flag: false,
          partner_type: [1],
          delete_flag: false,
        },
        // enterprise 1 (catch enterprise) is a partner of enterprise 3 (eel farming enterprise)
        {
          enterprise_id: 1,
          partner_id: 3,
          partner_type: [1, 2],
          delete_flag: false,
          hidden_flag: false,
        },
        // enterprise 2 (distribute enterprise) is a partner of enterprise 3 (eel farming enterprise)
        {
          enterprise_id: 2,
          partner_id: 3,
          hidden_flag: false,
          partner_type: [1, 2],
          delete_flag: false,
        },
        // enterprise 2 (distribute enterprise) is a partner of enterprise 1 (catch enterprise)
        {
          enterprise_id: 2,
          partner_id: 1,
          hidden_flag: false,
          partner_type: [1, 2],
          delete_flag: false,
        },
        // enterprise 3 (eel farming enterprise) is a partner of enterprise 2 (distribute enterprise)
        {
          enterprise_id: 3,
          partner_id: 2,
          hidden_flag: false,
          partner_type: [1, 2],
          delete_flag: false,
        },
      ],
    });

    // add sample data for inventories, the_origins table with individual data
    const inventoryData = [];
    const theOriginsData = [];
    for (let index = 0; index < 15; index++) {
      const shippingInfo = {
        shipping_gross_weight: faker.number.int({ min: 9000, max: 10000 }),
        shipping_tare_weight: faker.number.int({ min: 300, max: 500 }),
        shipping_date: faker.date.between({
          from: '2023-01-01',
          to: '2023-10-01',
        }),
      };
      const arrivalInfo = {
        arrival_gross_weight: faker.number.int({ min: 9000, max: 10000 }),
        arrival_tare_weight: faker.number.int({ min: 300, max: 500 }),
        arrival_date: faker.date.between({
          from: '2024-01-01',
          to: '2024-10-01',
        }),
      };

      const shipping_net_weight =
        shippingInfo.shipping_gross_weight - shippingInfo.shipping_tare_weight;
      const shipping_quantity = Math.round(shipping_net_weight * 0.2);
      const arrival_net_weight =
        arrivalInfo.arrival_gross_weight - arrivalInfo.arrival_tare_weight;
      const arrival_quantity = Math.round(arrival_net_weight * 0.2);

      theOriginsData.push({
        code: faker.string.numeric(16),
        qr_code: faker.string.alphanumeric(16),
        shipping_date: shippingInfo.shipping_date,
        shipping_gross_weight: shippingInfo.shipping_gross_weight,
        shipping_tare_weight: shippingInfo.shipping_tare_weight,
        shipping_net_weight,
        shipping_quantity,
        arrival_date: arrivalInfo.arrival_date,
        arrival_gross_weight: arrivalInfo.arrival_gross_weight,
        arrival_tare_weight: arrivalInfo.arrival_tare_weight,
        arrival_net_weight,
        arrival_quantity,
        delete_flag: false,
        inventory_id: index + 1,
        destination_enterprise_id: 2,
        starting_enterprise_id: 1,
        ingredient: [
          {
            inventory_id: null,
            shipping_gross_weight: shippingInfo.shipping_gross_weight,
            shipping_tare_weight: shippingInfo.shipping_tare_weight,
            shipping_net_weight,
            shipping_quantity,
            description: 'From catch enterprise',
          },
        ],
        setting: {
          unit_type: 3,
          unit_per_gram: 0.2,
          display_shipment_weight: true,
          display_actual_received: true,
        },
      });

      inventoryData.push({
        quantity_inventory: arrival_quantity,
        net_weight_total: arrival_net_weight,
        quantity_total: arrival_quantity,
        net_weight_inventory: arrival_net_weight,
        gross_weight_inventory: arrivalInfo.arrival_gross_weight,
        tare_weight_inventory: arrivalInfo.arrival_tare_weight,
        latest_arrival_date: arrivalInfo.arrival_date,
        enterprise_id: 2,
        created_on: new Date(),
        is_not_reset_edit: false,
        delete_flag: false,
      });
    }
    await tx.inventories.createMany({
      data: inventoryData,
    });
    await tx.the_origins.createMany({
      data: theOriginsData,
    });

    // add sample data for inventories, the_origins table with group data
    const groupName = ['A', 'B', 'C'];
    const inventoryGroupData = [];
    const theOriginsGroupData = [];
    for (let index = 0; index < 3; index++) {
      const inventoryGroupInfo = {
        group_name: groupName[index],
        net_weight_inventory: 0,
        quantity_inventory: 0,
        net_weight_total: 0,
        quantity_total: 0,
        gross_weight_inventory: 0,
        tare_weight_inventory: 0,
        latest_arrival_date: new Date(),
        created_on: new Date(),
        enterprise_id: 2,
        is_not_reset_edit: false,
        delete_flag: false,
      };

      for (let j = 0; j < 4; j++) {
        const shippingInfo = {
          shipping_gross_weight: (j + 1) * 1000,
          shipping_tare_weight: faker.number.int({ min: 100, max: 150 }),
          shipping_date: faker.date.between({
            from: '2023-01-01',
            to: '2023-10-01',
          }),
        };
        const arrivalInfo = {
          arrival_gross_weight: (j + 1) * 800,
          arrival_tare_weight: faker.number.int({ min: 100, max: 150 }),
          arrival_date: faker.date.between({
            from: '2024-01-01',
            to: '2024-10-01',
          }),
        };

        const shipping_net_weight =
          shippingInfo.shipping_gross_weight -
          shippingInfo.shipping_tare_weight;
        const shipping_quantity = Math.round(shipping_net_weight * 0.2);
        const arrival_net_weight =
          arrivalInfo.arrival_gross_weight - arrivalInfo.arrival_tare_weight;
        const arrival_quantity = Math.round(arrival_net_weight * 0.2);

        inventoryGroupInfo.net_weight_inventory += arrival_net_weight;
        inventoryGroupInfo.quantity_inventory += arrival_quantity;
        inventoryGroupInfo.net_weight_total += arrival_net_weight;
        inventoryGroupInfo.quantity_total += arrival_quantity;
        inventoryGroupInfo.gross_weight_inventory +=
          arrivalInfo.arrival_gross_weight;
        inventoryGroupInfo.tare_weight_inventory +=
          arrivalInfo.arrival_tare_weight;

        theOriginsGroupData.push({
          code: faker.string.numeric(16),
          qr_code: faker.string.alphanumeric(16),
          shipping_date: shippingInfo.shipping_date,
          shipping_gross_weight: shippingInfo.shipping_gross_weight,
          shipping_tare_weight: shippingInfo.shipping_tare_weight,
          shipping_net_weight,
          shipping_quantity,
          arrival_date: arrivalInfo.arrival_date,
          arrival_gross_weight: arrivalInfo.arrival_gross_weight,
          arrival_tare_weight: arrivalInfo.arrival_tare_weight,
          arrival_net_weight,
          arrival_quantity,
          delete_flag: false,
          inventory_id: index + 16,
          destination_enterprise_id: 2,
          starting_enterprise_id: 1,
          ingredient: [
            {
              inventory_id: null,
              shipping_gross_weight: shippingInfo.shipping_gross_weight,
              shipping_tare_weight: shippingInfo.shipping_tare_weight,
              shipping_net_weight,
              shipping_quantity,
              description: 'From catch enterprise',
            },
          ],
          setting: {
            unit_type: 3,
            unit_per_gram: 0.2,
            display_shipment_weight: true,
            display_actual_received: true,
          },
        });
      }

      inventoryGroupData.push(inventoryGroupInfo);
    }
    await tx.inventories.createMany({
      data: inventoryGroupData,
    });
    await tx.the_origins.createMany({
      data: theOriginsGroupData,
    });
  });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
