import MESSAGE from 'helpers/message';

const searchAllShippingList = {
  additionalProperties: false,
  type: 'object',
  properties: {
    pageIndex: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_INDEX_INVALID,
      },
    },
    pageSize: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_SIZE_INVALID,
      },
    },
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    code: {
      type: 'string',
      pattern: '^\\d+$',
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    destination: {
      type: 'string',
    },
    enterpriseName: {
      type: 'string',
    },
    startDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
    sortBy: {
      type: 'string',
      enum: ['id', 'shipping_net_weight', 'enterprise_name', 'name'],
    },
    note1: {
      type: 'string',
    },
    note2: {
      type: 'string',
    },
  },
};

export default searchAllShippingList;
