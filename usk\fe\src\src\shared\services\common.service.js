import BaseService from 'services/base.service';

class CommonService extends BaseService {
  async getOptions(params) {
    try {
      return this.dao.getOptions(params);
    } catch (error) {
      return null;
    }
  }

  async getCodeSuffix(params) {
    try {
      return this.dao.getCodeSuffix(params);
    } catch (error) {
      return null;
    }
  }
}

export default new CommonService('common');
