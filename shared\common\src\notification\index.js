// ======== IMPORT ========================
const {
  getNotificationListSchema,
  getNotificationDetailSchema,
  checkHasUnreadNotificationSchema,
  getNotificationListManagerSchema,
  registerNotificationSchema,
  getNotificationDetailManagerSchema,
  deleteNotificationSchema,
} = require('./schema');
const { apiKeyVerify, authAdmin, auth, authEnterprise } = require('../base/authorized');
// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
// Get notification list
async function getNotificationList(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.getNotificationList(request.user, request.query);
  return reply.send(response);
}

// Get notification detail
async function getNotificationDetail(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.getNotificationDetail(request.user, request.params.id);
  return reply.send(response);
}

// Check has unread notification
async function checkHasUnreadNotification(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.checkHasUnreadNotification(request.user);
  return reply.send(response);
}

// Get notification list for admin site manager
async function getNotificationListManager(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.getNotificationListManager(request.query);
  return reply.send(response);
}

// Register notification for admin site manager
async function registerNotificationManager(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.registerNotificationManager(request.user, request.body);
  return reply.send(response);
}

// Get notification detail for admin site manager
async function getNotificationDetailManager(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.getNotificationDetailManager(request.params.id);
  return reply.send(response);
}

async function deleteNotificationManager(request, reply) {
  const notificationService = request.diScope.resolve('notificationService');
  const response = await notificationService.deleteNotificationManager(request.user, request.params.id);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/list',
    { schema: getNotificationListSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getNotificationList
  );
  fastify.get(
    '/detail/:id',
    { schema: getNotificationDetailSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getNotificationDetail
  );
  fastify.get(
    '/unread',
    { schema: checkHasUnreadNotificationSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    checkHasUnreadNotification
  );
  fastify.get(
    '/manager/list',
    { schema: getNotificationListManagerSchema, onRequest: [apiKeyVerify, auth, authAdmin] },
    getNotificationListManager
  );
  fastify.post(
    '/manager/register',
    { schema: registerNotificationSchema, onRequest: [apiKeyVerify, auth, authAdmin] },
    registerNotificationManager
  );
  fastify.get(
    '/manager/detail/:id',
    { schema: getNotificationDetailManagerSchema, onRequest: [apiKeyVerify, auth, authAdmin] },
    getNotificationDetailManager
  );
  fastify.delete(
    '/manager/delete/:id',
    { schema: deleteNotificationSchema, onRequest: [apiKeyVerify, auth, authAdmin] },
    deleteNotificationManager
  );
};
// ===== 3. Router Registration ============