<template>
  <q-dialog v-model="model" persistent>
    <q-card class="tw:w-[70rem] tw:max-w-[70rem]">
      <div class="tw:flex tw:flex-col">
        <div class="tw:font-bold tw:text-m-design tw:bg-[#004AB9] tw:text-white tw:p-5">
          出荷量の差異
        </div>
        <div class="tw:text-m-design tw:font-normal tw:px-5 tw:pt-5">
          在庫量を大きく越える出荷量が指定されています。 差異発生の理由をチェックしてください
        </div>
        <div class="tw:text-m-design tw:p-5 tw:pt-0">
          <div>
            <div class="tw:flex tw:items-center">
              <span> 差異の理由 </span>
              <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
                必須
              </q-badge>
            </div>
            <div class="tw:flex tw:gap-4 tw:py-1">
              <q-radio
                v-for="(item, index) in reasonOptions"
                v-model="form.typeDiff"
                :key="index"
                :val="item.value"
                size="3.75rem"
                class="tw:transform tw:-translate-x-3"
              >
                <span class="tw:text-m-design tw:text-[#333333]">
                  {{ item.label }}
                </span>
              </q-radio>
            </div>
          </div>
          <div>
            <div class="tw:flex tw:items-center">
              <span> 「その他」を選択した理由 </span>
              <q-badge class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2">
                必須
              </q-badge>
            </div>
             <div class="tw:flex tw:gap-4 tw:py-1">
              <q-input
                :class="[
                    form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER ? 'tw:bg-[#CACACA] tw:border tw:border-[#D2D2D2]'
                    : 'tw:bg-[#FFFFFF] tw:border tw:border-[#CBCBCB]',
                    'tw:w-full ',
                ]"
                :disable="form.typeDiff !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER"
                input-class="tw:text-m-design"
                v-model="form.reasonDiff"
                outlined
                autocomplete="nope"
                type="textarea"
                maxlength="300"
                no-error-icon
                hide-bottom-space
                :error="!!errors.reasonDiff"
                :error-message="errors.reasonDiff"
              />
            </div>
          </div>
        </div>
        <div class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-end tw:p-5 tw:pt-1 tw:gap-2">
          <BaseButton
            class="tw:flex tw:justify-center tw:items-center tw:mr-4 tw:rounded-full tw:font-bold tw:text-xs-design tw:tl:w-[16rem] tw:w-full"
            :class="`tw:text-[#004AB9]`"
            outline
            label="出荷量を修正する"
            @click.prevent="handleClickCancel"
          />
          <BaseButton
            type="button"
            class="tw:flex tw:justify-center tw:items-center tw:text-white tw:font-bold tw:tl:w-[19rem] tw:w-full tw:rounded-full tw:text-xs-design"
            :class="`tw:bg-[#004AB9]`"
            @click.prevent="handleClickSubmit"
            label="差異の理由を登録する"
          />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import useValidate from 'src/composables/validate';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'src/helpers/constants';
import reasonOverLimitWeightSchema from 'src/schemas/outbound-shipment/reasonOverLimitWeight.schema';
import { ref, watch } from 'vue';

const model = defineModel();
const emit = defineEmits(['onClickCancel', 'onClickSubmit']);
const { validateData, errors } = useValidate();
const reasonOptions = ref([
  { label: '計量誤差', value: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR },
  { label: 'その他', value: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER },
]);
const form = ref({
  typeDiff: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR,
  reasonDiff: '',
});

// #region function
const handleClickCancel = () => {
  emit('onClickCancel');
};

const handleClickSubmit = () => {
  const payload = {
    ...form.value,
  };
  const valid = validateData(reasonOverLimitWeightSchema, payload);
  if (valid) {
    emit('onClickSubmit', payload);
  }
};
// #endregion function

watch(
  () => form.value.typeDiff,
  newValue => {
    if (newValue !== TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER) {
      form.value.reasonDiff = '';
    }
  }
);
</script>
