<template>
  <q-input
    v-model="model"
    mask="time"
    bg-color="white"
    outlined
    hide-bottom-space
    input-class="tw:tl:text-[2rem] tw:tl:leading-[2rem]
      tw:text-base tw:font-normal"
    autocomplete="nope"
    no-error-icon
  >
    <template v-slot:append>
      <q-icon name="access_time" class="cursor-pointer">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-time v-model="model" :color="colorMain">
            <div class="row items-center justify-end">
              <q-btn
                v-close-popup
                label="閉じる"
                flat
                :class="`tw:bg-${colorSub}`"
                class="tw:text-white"
              />
            </div>
          </q-time>
        </q-popup-proxy>
      </q-icon>
    </template>
  </q-input>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';

const { colorMain, colorSub } = storeToRefs(useAppStore());

const model = defineModel();
</script>
