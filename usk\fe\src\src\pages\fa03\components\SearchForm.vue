<template>
  <form>
    <div
      class="tw:pb-5 tw:tl:pb-0 tw:grid tw:grid-rows-1 tw:grid-cols-1 tw:tl:grid-cols-2
      tw:gap-5 tw:tl:gap-8 tw:px-[.625rem]"
    >
      <div>
        <BaseLabel label="荷口番号（ハイフン無し）" />
        <BaseInput
          v-model="searchFormProvideData.form.value.code"
          outlined
          input-class="tw:text-[#333333] tw:pb-1 tw:text-m-design"
          maxlength="16"
          inputmode="numeric"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.code"
          :error-message="searchFormProvideData.errors.value.code"
        />
      </div>
      <div>
        <BaseLabel label="届出事業者名" />
        <BaseInput
          v-model="searchFormProvideData.form.value.enterpriseName"
          outlined
          input-class="tw:text-[#333333] tw:pb-1 tw:text-m-design"
          maxlength="256"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.enterpriseName"
          :error-message="searchFormProvideData.errors.value.enterpriseName"
        />
      </div>
      <div>
          <BaseLabel label="出荷登録日" />
          <div class="tw:flex tw:gap-1 tw:items-center">
            <div class="tw:flex-1">
              <BaseDatePicker
                v-model="searchFormProvideData.form.value.startDate"
                :error="!!searchFormProvideData.errors.value.startDate"
                :error-message="searchFormProvideData.errors.value.startDate"
                input-class="tw:text-[#333333] tw:pb-1 tw:text-m-design"
              />
            </div>
            <span>～</span
            >
            <div class="tw:flex-1">
              <BaseDatePicker
                v-model="searchFormProvideData.form.value.endDate"
                :error="!!searchFormProvideData.errors.value.endDate"
                :error-message="searchFormProvideData.errors.value.endDate"
                input-class="tw:text-[#333333] tw:pb-1 tw:text-m-design"
              />
            </div>
          </div>
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import BaseDatePicker from 'src/components/base/vs/BaseDatePicker.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import {
  inject,
} from 'vue';
// #endregion

// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion

</script>
