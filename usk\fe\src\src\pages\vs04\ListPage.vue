<template>
  <q-page class="h-full">
    <!-- filter start -->
    <div>
      <ExpansionSearchItem v-model:expanded="isSearchExpanded">
        <SearchForm />
      </ExpansionSearchItem>
    </div>
    <div
      v-if="
        searchConditions.enterpriseName ||
        searchConditions.name ||
        searchConditions.code ||
        searchConditions.licenseNumber
      "
      class="tw:mt-4 tw:mb-4 tw:text-xs-design tw:flex tw:flex-wrap tw:items-center tw:space-x-2"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]"
        >検索条件</span
      >
      <span
        v-if="searchConditions.enterpriseName"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.enterpriseName }}
      </span>
      <span
        v-if="searchConditions.name"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.name }}
      </span>
      <span
        v-if="searchConditions.code"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.code }}
      </span>
      <span
        v-if="searchConditions.licenseNumber"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.licenseNumber }}
      </span>
    </div>
    <!-- filter end -->
    <q-card class="tw:p-4 tw:mt-8 tw:mb-[5rem] tw:bg-white">
      <div
        class="tw:flex tw:justify-between tw:items-start tw:tl:items-center tw:tl:mb-2 tw:py-4 tw:flex-col tw:tl:flex-row"
      >
        <!-- tabs item -->
        <div class="tw:tl:w-[60%] tw:w-full">
          <TabsItemNotifi />
        </div>

        <div class="tw:flex tw:flex-1 tw:ml-[-1.5rem]">
          <div class="tw:flex-1"></div>
          <q-checkbox
            v-model="hiddenFlag"
            label="非表示のユーザーを表示"
            class="tw:py-2 tw:min-w-[8rem]"
            size="xl"
          />
        </div>
      </div>
      <!-- sort btn start -->
      <div
        class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:border-neutral-500 tw:pt-1"
      >
        <div
          class="tw:flex tw:tl:flex-row tw:flex-col tw:items-start tw:space-y-4"
        >
          <div
            class="tw:flex tw:flex-none tw:items-center tw:space-x-2 tw:h-[4.25rem]"
          >
            <span class="tw:text-xs-design">表示件数</span>
            <PageSizeDropdownSP v-model="pageSize" />
          </div>
          <SortByDropdownSP
            v-model="sortModel"
            class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden tw:mb-[1rem] tw:tl:mb-0"
          />
        </div>
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0 tw:w-full"
        >
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block q-py-sm">
        <q-table
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="paginationComputed"
          :rows-per-page-options="[50]"
        >
          <template v-slot:header="props">
            <q-tr
              :props="props"
              class="tw:text-s-design"
              :class="`tw:bg-[#E2E3EA]`"
            >
              <q-th
                @click="handleClickSort(props.cols[0].name)"
                class="tw:font-bold tw:text-s-design tw:text-left tw:border-r tw:border-[#D2D2D2] tw:tl:w-[35%] tw:dt:w-[30%]"
              >
                {{ props.cols[0].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:font-bold tw:text-s-design tw:text-left tw:border-r tw:border-[#D2D2D2] tw:tl:w-[35%] tw:dt:w-[30%]"
              >
                {{ props.cols[1].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[2].name)"
                class="tw:font-bold tw:text-s-design tw:text-left tw:border-r tw:border-[#D2D2D2] tw:tl:w-[10%]"
              >
                {{ props.cols[2].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[2].name)"
                  :name="
                    getSortOrder(props.cols[2].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[3].name)"
                class="tw:font-bold tw:text-s-design tw:text-left tw:border-r tw:border-[#D2D2D2] tw:tl:w-[10%]"
              >
                {{ props.cols[3].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[3].name)"
                  :name="
                    getSortOrder(props.cols[3].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                class="tw:font-bold tw:text-s-design tw:text-left tw:border-r tw:border-[#D2D2D2] tw:tl:min-w-[5%] tw:tl:w-[10%]"
              >
                {{ props.cols[4].label }}
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr class="tw:cursor-pointer tw:w-full" :props="props">
              <q-td
                key="partner.name"
                :props="props"
                @click.prevent="handleClickRow(props.row)"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div
                  :class="`tw:truncate tw:font-bold tw:max-w-[30vw] tw:underline tw:underline-offset-1 tw:text-[#004AB9]`"
                >
                  {{ props.row?.partner?.name }}
                </div>
              </q-td>
              <q-td
                key="partner.enterprise.enterprise_name"
                :props="props"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:max-w-[30vw]">
                  {{ props.row?.partner?.enterprise?.enterprise_name }}
                </div>
              </q-td>
              <q-td
                key="partner.enterprise.enterprise_code"
                :props="props"
                class="text-left border-r border-b border-td-color tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate">
                  {{ props.row.partner?.enterprise?.enterprise_code }}
                </div>
              </q-td>
              <q-td
                key="partner_type"
                :props="props"
                class="text-left border-r border-b border-td-color tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate">
                  {{ props.row.partner_type }}
                </div>
              </q-td>
              <q-td
                key=""
                :props="props"
                class="text-center border-r border-b border-td-color tw:text-s-design"
              >
                <div class="tw:flex tw:justify-center tw:items-center">
                  <img
                    v-if="props.row.hidden_flag"
                    :src="EyeSlashSvg"
                    alt="slash"
                    class="tw:hover:cursor-pointer tw:w-[2.25rem] tw:h-[2.25rem]"
                    @click.stop="toggleHidden(props.row)"
                  />
                  <img
                    v-else
                    alt="eye"
                    :src="EyeSvg"
                    class="tw:hover:cursor-pointer tw:w-[2.25rem] tw:h-[2.25rem] tw:text-[#004AB9]!"
                    @click.stop="toggleHidden(props.row)"
                  />
                </div>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div
              class="tw:w-full tw:text-center tw:text-s-design"
            >
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>

      <!-- start table for smartphone -->
      <div class="tw:block tw:tl:hidden q-py-sm">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-1"
          card-class="tw:w-full"
          :rows="partnerList"
          v-model:pagination="paginationComputed"
          row-key="index"
          hide-pagination
          hide-header
        >
          <template v-slot:item="props">
            <div class="tw:w-full">
              <q-card flat bordered>
                <q-card-section class="tw:text-left">
                  <div
                    class="tw:flex-1 tw:flex tw:items-center tw:justify-between"
                  >
                    <div
                      class="tw:w-full tw:cursor-pointer"
                      @click="handleClickRow(props.row)"
                    >
                      <strong
                        v-if="
                          CHECK_ROLE(
                            [ROLES_ENUM.NORMAL_USER],
                            [],
                            [STAFF_TYPE_ENUM.STAFF],
                            props.row?.partner,
                          )
                        "
                        class="tw:text-s-design tw:text-[#004AB9] tw:max-w-[70vw] tw:block tw:break-words"
                      >
                        {{ props.row?.partner?.name }}({{
                          props.row?.partner?.enterprise?.enterprise_name
                        }})</strong
                      >
                      <strong
                        v-else
                        class="tw:text-s-design tw:text-[#004AB9] tw:max-w-[70vw] tw:block tw:break-words"
                      >
                        {{ props.row?.partner?.name }}</strong
                      >
                      <div
                        class="tw:text-s-design tw:flex tw:flex-1 tw:max-w-[70vw] tw:break-words"
                      >
                        届出番号：
                        {{ props.row.partner?.enterprise?.enterprise_code }}
                      </div>
                      <div
                        class="tw:text-s-design tw:flex tw:flex-1 tw:max-w-[70vw] tw:break-words"
                      >
                        取引先区分：{{ convertStatus(props.row.partner_type) }}
                      </div>
                    </div>
                    <img
                      v-if="props.row.hidden_flag"
                      :src="EyeSlashSvg"
                      class="tw:hover:cursor-pointer tw:w-[2.25rem] tw:flex-none tw:h-[2.25rem]"
                      @click.prevent="toggleHidden(props.row)"
                      alt="eye-slash"
                    />
                    <img
                      v-else
                      :src="EyeSvg"
                      class="tw:hover:cursor-pointer tw:flex-none tw:w-[2.25rem] tw:h-[2.25rem]"
                      @click.prevent="toggleHidden(props.row)"
                      alt="eye"
                    />
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </template>
          <template v-slot:no-data="">
            <div class="tw:w-full tw:text-center tw:text-s-design">
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <!-- table end -->
    </q-card>
    <div
      class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0 tw:w-full tw:pb-[20rem]"
    >
      <PaginationNotifi />
    </div>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
      tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="トップに戻る"
        @click.prevent="goToPage('home')"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[21.2rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="取引先を登録する"
        @click.prevent="goToPage('partnerHybrid')"
      />
    </q-footer>
  </q-page>
</template>

<script setup>
// import
import { computed, onMounted, ref, watch, provide } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import locatedTransactionService from 'services/locatedTransaction.service';
import useValidate from 'composables/validate';
import searchAllLocatedTransaction from 'schemas/searchAllLocatedTransaction';
import { PARTNER_TYPE_ENUM } from 'helpers/constants';
import EyeSlashSvg from 'assets/EyeSlashSvg.svg';
import EyeSvg from 'assets/EyeSvg.svg';
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import { isEqual, orderBy } from 'lodash';
import { useLocalStorage } from 'composables/localstorage';
import TabsItemNotifi from 'src/components/TabsItemNotifi.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import SortByDropdownSP from 'src/components/SortByDropdownSP.vue';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { CHECK_ROLE } from 'src/helpers/common';
import { ROLES_ENUM, STAFF_TYPE_ENUM } from 'src/helpers/constants';

import SearchForm from './components/SearchForm.vue';

// #region variable
const router = useRouter();
const { validateData, errors } = useValidate();
const { listPageQueryParams } = useLocalStorage();
const isSearchExpanded = ref(false);
const route = useRoute();
const isFirstLoad = ref(true);

const searchConditions = computed(() => {
  const query = route.query;
  return {
    enterpriseName: query.enterpriseName || '',
    name: query.name || '',
    code: query.code || '',
    licenseNumber: query.licenseNumber || '',
  };
});
const sortModel = ref({});
// pagination state
const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: listPageQueryParams.value?.partner?.page || 1,
  limit: listPageQueryParams.value?.partner?.limit || 10,
});
// store list of partner
const partnerList = ref([]);
// total page default is 1
const totalPage = ref(1);
// page size default is 10
const pageSize = ref(router.currentRoute.value.query.limit || 10);
// page index default is 1
const pageIndex = ref(listPageQueryParams.value?.partner?.page || 1);
// for manage state of active tab
const activeTab = ref('all');
// for manage state of sort by selected label
const sortBySelectedLabel = ref('登録が新しい順');
// for manage state of has different page index
// for manage state of partner type
const tabs = [
  {
    label: '全て',
    value: 'all',
  },
  {
    label: '仕入先',
    value: PARTNER_TYPE_ENUM.SUPPLIER.toString(),
  },
  {
    label: '出荷先',
    value: PARTNER_TYPE_ENUM.SHIPPER.toString(),
  },
];
const columns = [
  {
    name: 'partner.name',
    label: '事業者名',
    align: 'left',
    field: 'name',
    sortable: true,
  },
  {
    name: 'partner.enterprise.enterprise_name',
    label: '届出事業者名',
    align: 'left',
    field: 'enterprise_name',
    sortable: true,
  },
  {
    name: 'partner.enterprise.enterprise_code',
    align: 'left',
    label: '届出番号',
    field: 'enterprise_code',
    sortable: true,
  },
  {
    name: 'partner_type',
    align: 'left',
    label: '取引先区分',
    field: 'partner_type',
    sortable: true,
  },
  {
    name: '',
    align: 'center',
    label: '',
    field: '',
    sortable: true,
  },
];
// for manage state of hidden flag
const hiddenFlag = ref(listPageQueryParams.value.partner?.hiddenFlag || false);
// for manage state of search form
const searchForm = ref({
  enterpriseName: '',
  name: '',
  code: '',
  licenseNumber: '',
});
// for manage state of search form data (validated)
const searchFormData = ref({
  enterpriseName: '',
  name: '',
  code: '',
  licenseNumber: '',
});
const multiSortConditions = ref([]);

// #endregion variable

// #region computed
const paginationComputed = computed(() => ({
  page: pagination.value.page,
  rowsPerPage: pagination.value.limit,
}));

const sortedRows = computed(() => {
  const rows = [...partnerList.value];
  const data = rows.map(row => ({
    ...row,
    partner_type: convertStatus(row.partner_type) || '',
  }));
  const lstKey = [];
  const lstOrder = [];
  for (const { key, order } of multiSortConditions.value) {
    lstKey.push(key);
    lstOrder.push(order);
  }
  return orderBy(data, lstKey, lstOrder);
});

// #endregion

// #region function
// convert status to string for display
// (ex: 1,2 => 仕入先/出荷先)
const convertStatus = status =>
  tabs
    .reduce((init, t) => {
      if (status.includes(+t.value)) {
        init.push(t.label);
      }
      return init;
    }, [])
    .join('/');

const formatSearchFormToQuery = form => ({
  enterpriseName: form.enterpriseName?.trim() || undefined,
  name: form.name?.trim() || undefined,
  code: form.code?.trim() || undefined,
  licenseNumber: form.licenseNumber?.trim() || undefined,
});

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }
};

const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

// get data from server
const getData = async () => {
  const result = await locatedTransactionService.searchAll({
    ...pagination.value,
    partnerType: activeTab.value === 'all' ? undefined : activeTab.value,
    code: searchFormData.value.code || undefined,
    name: searchFormData.value.name || undefined,
    enterpriseName: searchFormData.value.enterpriseName || undefined,
    licenseNumber: searchFormData.value.licenseNumber || undefined,
    hiddenFlag: hiddenFlag.value ? true : undefined,
  });
  if (result.code === 0) {
    partnerList.value = result.payload?.items ?? [];
    if (window.innerWidth < 960) {
      let key;
      const order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case '':
          key = 'id';
          break;
        case 'name':
          key = 'partner.name';
          break;
        case 'enterprise_name':
          key = 'partner.enterprise.enterprise_name';
          break;
        case 'enterprise_code':
          key = 'partner.enterprise.enterprise_code';
          break;
        default:
          break;
      }

      partnerList.value = orderBy(partnerList.value, [key], [order]);
    }

    totalPage.value = Math.ceil(
      (result.payload.total_item ?? 1) / pagination.value.limit
    );

    // check if page index is different from the value server return
    // if different, replace query to the new page index (value server return)
    if (+result.payload.page !== +pageIndex.value || totalPage.value < pageIndex.value) {
      router.replace({
        query: {
          ...pagination.value,
          ...formatSearchFormToQuery(searchFormData.value),
          page: result.payload.page,
          hiddenFlag: hiddenFlag.value,
          partnerType: activeTab.value,
        },
      });
    }
  }
};

const goToPage = async pageName => {
  await router.push({ name: pageName });
};

const handleClickRow = async row => {
  await router.push({
    name: 'partnerDetail',
    params: {
      id: row.id,
    },
  });
};

const toggleHidden = async row => {
  await locatedTransactionService.toggleHidden({}, row.id);
  await getData();
};
// #endregion function

// #region watch
// watch hidden flag
// if hidden flag change, change params => get data
watch(hiddenFlag, async () => {
  isFirstLoad.value = true;
  await router.push({
    query: {
      ...pagination.value,
      ...formatSearchFormToQuery(searchFormData.value),
      page: 1,
      hiddenFlag: hiddenFlag.value,
      partnerType: activeTab.value,
    },
  });
});

// watch page size
// if page size change, change params => get data
watch(pageSize, async () => {
  pagination.value.limit = pageSize.value;
  totalPage.value = Math.ceil(partnerList.value.length / +pageSize.value);
  if (listPageQueryParams.value.partner) {
    listPageQueryParams.value.partner.rowsPerPage = +pageSize.value;
  } else {
    listPageQueryParams.value.partner = {
      rowsPerPage: +pageSize.value,
    };
  }

  pagination.value.limit = pageSize.value;
  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
    hiddenFlag: hiddenFlag.value,
    partnerType: activeTab.value,
  };
  if (!isFirstLoad.value) {
    query.page = 1;
  }
  router.push({
    query: {
      ...query,
    },
  });
});

// #region watch to get report list
// watch change query params
// if query params change, get notification list
watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.descending +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.limit +
    router.currentRoute.value.query.name +
    router.currentRoute.value.query.code +
    router.currentRoute.value.query.hiddenFlag +
    router.currentRoute.value.query.partnerType +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.licenseNumber,
  async () => {
    if (router.currentRoute.value.query.limit) {
      listPageQueryParams.value.partner.limit =
        +router.currentRoute.value.query.limit;
    }

    if (router.currentRoute.value.query.page) {
      listPageQueryParams.value.partner.page =
        +router.currentRoute.value.query.page;
    }

    activeTab.value = router.currentRoute.value.query.partnerType;
    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;
    hiddenFlag.value = router.currentRoute.value.query.hiddenFlag === 'true';
    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        )?.label
      : '';
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      name: router.currentRoute.value.query.name,
      code: router.currentRoute.value.query.code,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
    };
    searchFormData.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      name: router.currentRoute.value.query.name,
      code: router.currentRoute.value.query.code,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
    };

    listPageQueryParams.value.partner = {
      ...listPageQueryParams.value.partner,
      ...searchFormData.value,
      sortBy: router.currentRoute.value.query.sortBy,
      hiddenFlag: router.currentRoute.value.query.hiddenFlag === 'true',
    };
    // if hasDiffPageIndex is false, get data
    // if hasDiffPageIndex is true, do nothing because the query has been replaced
    if (window.innerWidth < 960) {
      let key;
      const order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case '':
          key = 'id';
          break;
        case 'name':
          key = 'partner.name';
          break;
        case 'enterprise_name':
          key = 'partner.enterprise.enterprise_name';
          break;
        case 'enterprise_code':
          key = 'partner.enterprise.enterprise_code';
          break;
        default:
          break;
      }

      partnerList.value = orderBy(partnerList.value, [key], [order]);
    }

    if (isFirstLoad.value) {
      await getData();
      isFirstLoad.value = false;
    }
  }
);

// #region provide
const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      enterpriseName: '',
      name: '',
      code: '',
      licenseNumber: '',
    };
    errors.value = {};
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getData();
      this.isClose = true;
      return;
    }

    searchForm.value = {
      ...searchForm.value,
      enterpriseName: searchForm.value.enterpriseName?.replace(/\s+/g, ''),
      name: searchForm.value.name?.replace(/\s+/g, ''),
      code: searchForm.value.code,
      licenseNumber: searchForm.value.licenseNumber,
    };

    const data = {
      ...searchForm.value,
      enterpriseName: searchForm.value.enterpriseName || undefined,
      name: searchForm.value.name || undefined,
      code: searchForm.value.code || undefined,
      licenseNumber: searchForm.value.licenseNumber || undefined,
    };

    const validate = validateData(searchAllLocatedTransaction, {
      ...data,
    });
    if (!validate) {
      this.isClose = false;
      return;
    }
    isFirstLoad.value = true;
    pageIndex.value = 1;
    await router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(data),
        page: 1,
        hiddenFlag: hiddenFlag.value,
        partnerType: activeTab.value,
      },
    });
    this.isClose = true;
  },
  isClose: true,
  title: '取引先をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);

const tabsItemProvideData = {
  activeTab,
  tabOptions: tabs,
  handleClickTabItem(tabItem) {
    isFirstLoad.value = true;
    router.push({
      query: {
        partnerType: tabItem.value,
        ...formatSearchFormToQuery(searchFormData.value),
        ...pagination.value,
        page: 1,
        hiddenFlag: hiddenFlag.value,
      },
    });
  },
};
provide('tabsItemProvideData', tabsItemProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    {
      label: '事業者名順',
      value: 'name',
    },
    {
      label: '届出事業者名順',
      value: 'enterprise_name',
    },
    {
      label: '届出番号順',
      value: 'enterprise_code',
    },
  ],
  sortBySelectedLabel,
  handleClickSortByItem: async option => {
    sortBySelectedLabel.value = option.label;
    pagination.value.sortBy = option.value;
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
        hiddenFlag: hiddenFlag.value,
        partnerType: activeTab.value,
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        hiddenFlag: hiddenFlag.value,
        partnerType: activeTab.value,
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);
// #endregion provide

onMounted(async () => {
  if (!listPageQueryParams.value.partner) {
    listPageQueryParams.value.partner = {};
  }
  activeTab.value = router.currentRoute.value.query.partnerType || 'all';
  if (!router.currentRoute.value?.query?.page) {
    sortModel.value = {
      value: listPageQueryParams.value.partner.sortBy,
      label:
        sortByDropdownProvideData.sortByOptions.find(
          item => item.value === listPageQueryParams.value.partner.sortBy
        )?.label || '',
    };
    isFirstLoad.value = true;
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(listPageQueryParams.value.partner),
        partnerType: activeTab.value,
        sortBy: listPageQueryParams.value.partner.sortBy,
        hiddenFlag: listPageQueryParams.value.partner.hiddenFlag || false,
      },
    });
  } else {
    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;
    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        )?.label
      : '';
    sortModel.value = {
      value: router.currentRoute.value.query.sortBy,
      label: sortBySelectedLabel.value,
    };
    hiddenFlag.value = router.currentRoute.value.query.hiddenFlag === 'true';
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      name: router.currentRoute.value.query.name,
      code: router.currentRoute.value.query.code,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
    };
    searchFormData.value = {
      enterpriseName: router.currentRoute.value.query.enterpriseName,
      name: router.currentRoute.value.query.name,
      code: router.currentRoute.value.query.code,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
    };
    await getData();
    isFirstLoad.value = false;
  }
});
</script>
<style scoped>
:deep(.q-checkbox__label) {
  font-size: 2rem;
  line-height: 1.5rem;
}
</style>
