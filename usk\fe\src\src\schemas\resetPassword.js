import MESSAGE from 'helpers/message';

const resetPassword = {
  additionalProperties: false,
  type: 'object',
  required: ['password', 'passwordConfirm'],
  properties: {
    password: {
      type: 'string',
      maxLength: 256,
      minLength: 1,
      pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
      },
    },
    passwordConfirm: {
      type: 'string',
      maxLength: 256,
      minLength: 1,
      const: {
        $data: '1/password',
      },
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_NOTMATCH_PASSWARD_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      password: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      passwordConfirm: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default resetPassword;
