const {
  getProfileSchema,
  updatePhoneSchema
} = require('./schema');
const { auth, apiKeyVerify } = require('../base/authorized');
// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
async function getProfileHandler(request, reply) {
  const { query, user } = request;
  query.userId = user.id;
  const profileService = request.diScope.resolve('profileService');
  const response = await profileService.getProfile(query);
  return reply.send(response);
}

async function updatePhoneHandler(request, reply) {
  const { query, body, user } = request;
  query.userId = user.id;
  query.phone = body.phone;
  const profileService = request.diScope.resolve('profileService');
  const response = await profileService.updatePhone(query);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  // TODO add validate user
  fastify.get('/', { schema: getProfileSchema, onRequest: [apiKeyVerify, auth] }, getProfileHandler);
  fastify.post('/update', { schema: updatePhoneSchema, onRequest: [apiKeyVerify, auth] }, updatePhoneHandler);
};