<template>
  <q-pagination
    v-if="provideData.totalPage.value > 1"
    v-model="provideData.pageIndex.value"
    :max="provideData.totalPage.value"
    :max-pages="5"
    boundary-links
    :boundary-numbers="false"
    icon-first="keyboard_double_arrow_left"
    icon-last="keyboard_double_arrow_right"
    outline
    padding=".5rem .875rem"
    :color="colorMain"
    active-design="unelevated"
    :active-color="colorMain"
    active-text-color="white"
    @update:model-value="provideData.onChangedPageIndex"
  ></q-pagination>
</template>
<script setup>
import { useAppStore } from 'stores/app-store';
import { storeToRefs } from 'pinia';
import { inject } from 'vue';

const { colorMain } = storeToRefs(useAppStore());
const provideData = inject('paginationItemProvideData');
</script>
