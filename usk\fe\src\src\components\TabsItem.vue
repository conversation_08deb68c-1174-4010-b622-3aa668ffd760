<template>
  <q-tabs
    :class="`tw:border tw:rounded-xl tw:text-${colorMain} tw:max-w-[21rem] tw:mr-2`"
    v-model="activeTab"
    :active-class="`tw:bg-${colorMain} tw:rounded-lg tw:text-white`"
    indicator-color="transparent"
  >
    <q-tab
      v-for="tab in provideData.tabOptions"
      :key="tab"
      :name="tab.value"
      :label="tab.label"
      class="tw:w-[7rem] tw:text-[5rem]"
      content-class="tw:font-bold tw:leading-[5.5rem] tw:text-[5rem]"
      @click.prevent="provideData.handleClickTabItem(tab)"
    />
  </q-tabs>
</template>

<script setup>
import { useAppStore } from 'stores/app-store';
import { storeToRefs } from 'pinia';
import { inject, ref } from 'vue';

const { colorMain } = storeToRefs(useAppStore());
const provideData = inject('tabsItemProvideData');

const activeTab = provideData.activeTab || ref(provideData.tabOptions[0].value);
</script>
