// ======== IMPORT ========================
const {
  getOptionsSchema,
  getPresignedUrlSchema,
  getCodeSuffixSchema,
} = require('./schemas');
const { auth, apiKeyVerify, authEnterprise } = require('../base/authorized');

// ===== 1. Injection ==============
// ===== 2. Router Functions ==============
async function getOptionsHandler(request, reply) {
  const { query, user } = request;
  const commonService = request.diScope.resolve('commonService');
  const response = await commonService.getOptions(user, query);
  return reply.send(response);
}

async function getPresignedUrlHandler(request, reply) {
  const { query } = request;
  const commonService = request.diScope.resolve('commonService');
  const response = await commonService.getSignedUrlS3(query.isAdmin);
  return reply.send(response);
}

async function getCodeSuffixHandler(request, reply) {
  const { user, query } = request;
  const commonService = request.diScope.resolve('commonService');
  const response = await commonService.getCodeSuffix(user, query);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/get-options',
    {
      schema: getOptionsSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    getOptionsHandler
  );

  fastify.get(
    '/presigned-url',
    {
      schema: getPresignedUrlSchema,
      onRequest: [apiKeyVerify],
    },
    getPresignedUrlHandler
  );

  fastify.get(
    '/get-code-suffix',
    {
      schema: getCodeSuffixSchema,
      onRequest: [apiKeyVerify, auth],
    },
    getCodeSuffixHandler
  );
};
