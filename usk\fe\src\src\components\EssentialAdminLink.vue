<template>
  <q-item
    @click.prevent="pushRoute(item)"
    v-for="item in items"
    :key="item"
    :class="`${item.borderTop ? 'tw:border-t tw:border-gray' : ''}
  ${item.borderBot ? 'tw:border-b tw:border-gray' : ''}
  ${item.marginTop ? 'tw:mt-8' : ''}
  ${item.marginBot ? 'tw:mb-8' : ''}
  ${
    !item?.role
      ? ''
      : CHECK_ROLE(item.role, item.type, item.staff, user) &&
        [undefined, true].includes(item.subCond)
      ? ' '
      : ' tw:hidden'
  }`"
    clickable
  >
    <q-item-section v-if="item.icon" avatar>
      <q-icon :name="item.icon" class="tw:text-[2.25rem] tw:fill-[#7E8093]" />
    </q-item-section>
    <div v-else class="tw:pr-3 tw:flex tw:items-center">
      <img
        v-if="item.svg"
        :src="item.svg"
        class="tw:w-[2.25rem] tw:h-[2.25rem] tw:fill-[#7E8093]"
      />
    </div>

    <q-item-section class="tw:flex-row tw:justify-between tw:items-center">
      <q-item-label class="tw:tl:text-[1.25rem] tw:text-[1rem]">{{
        item.title
      }}</q-item-label>
      <span
        v-if="item.noticeFlag"
        class="tw:w-[10px] tw:h-[10px] tw:mr-4 tw:rounded-full tw:bg-red-2"
      />
    </q-item-section>
  </q-item>
</template>

<script setup>
import { inject } from 'vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { CHECK_ROLE } from 'helpers/common';
import { useAuthStore } from 'src/stores/auth-store';

const items = inject('items');
const router = useRouter();
const { user } = storeToRefs(useAuthStore());
const pushRoute = async item => {
  if (!item || typeof item !== 'object') {
    return;
  }

  const { external, link, filename = 'ユーザーマニュアル.pdf' } = item;

  if (external && link) {
    const tag = document.createElement('a');
    tag.href = link;
    tag.target = '_blank';
    tag.download = filename;
    document.body.appendChild(tag);
    tag.click();
    document.body.removeChild(tag);
  } else if (link) {
    const currentRoute = router.currentRoute.value;
    if (currentRoute.name === link) {
      window.location.href = router.resolve({ name: link }).href;
    } else {
      router.push({ name: link });
    }
  }
};
</script>
