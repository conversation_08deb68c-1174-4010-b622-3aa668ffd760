import BaseRepository from './base.repository';

class OutboundShipmentRepository extends BaseRepository {
  constructor() {
    super('outbound');
  }

  registerOutboundShipment(body) {
    return this.client.post('/register', body);
  }

  checkPermissionForProxyOutboundShipment(proxyUserId) {
    return this.client.get(`/proxy-check-permission/${proxyUserId}`);
  }

  registerProxyOutboundShipment(body) {
    return this.client.post('/proxy-register', body);
  }

  getOutboundShipmentDetail(query) {
    return this.client.get('/detail', query);
  }

  getInventoryListOptions(query) {
    return this.client.get('/inventory-list-options', query);
  }
}

export default new OutboundShipmentRepository();
