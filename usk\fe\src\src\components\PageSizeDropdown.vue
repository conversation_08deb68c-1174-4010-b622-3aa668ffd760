<template>
  <q-select class="" outlined :options="pageSizeOptions"  />
</template>

<script setup>
import { ref } from 'vue';

const pageSizeOptions = ref([10, 25, 50]);
</script>
<style scoped>
:deep(.q-field__native) {
  height: 2.25rem;
  min-height: 2.25rem;
}

:deep(.q-field__control) {
  min-height: 2.25rem;
  padding-left: 1rem;
}

:deep(.q-field__marginal) {
  height: 2.25rem;
}

@media (min-width: 960px) {
  :deep(.q-field__native) {
    height: 2.5rem;
  }

  :deep(.q-field__control) {
    height: 2.5rem;
  }

  :deep(.q-field__marginal) {
    height: 2.5rem;
  }
}
</style>
