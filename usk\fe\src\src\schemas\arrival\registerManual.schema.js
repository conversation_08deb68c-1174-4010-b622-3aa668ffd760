import { UNIT_TYPE_SETTING_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';

export const registerManualStep1 = {
  type: 'object',
  additionalProperties: false,
  required: ['date', 'mainCode', 'subCode', 'supplier', 'volumeType'],
  properties: {
    mainCode: {
      type: 'string',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    subCode: {
      type: 'string',
      minLength: 1,
      pattern: '^\\d{3}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    supplier: {
      type: 'string',
      minLength: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_SUPPLIER_ERROR,
      },
    },
    date: {
      type: 'string',
      format: 'slash-date',
      minLength: 1,
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    volumeType: {
      type: 'integer',
      enum: Object.values(UNIT_TYPE_SETTING_ENUM),
      errorMessage: {
        enum: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export const registerManualStep2 = {
  type: 'object',
  additionalProperties: false,
  properties: {
    grossWeight: {
      type: 'number',
      exclusiveMinimum: 0,
      maximum: 9999999.99,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeight: {
      type: 'number',
      minimum: 0,
      exclusiveMaximum: {
        $data: '1/grossWeight',
      },
      maximum: 9999999.99,
      errorMessage: {
        minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
        exclusiveMaximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    quantity: {
      type: 'integer',
      minimum: 1,
      maximum: 99999,
      errorMessage: {
        minimum: MESSAGE.MSG_LIMITS_QUANTITY_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};
