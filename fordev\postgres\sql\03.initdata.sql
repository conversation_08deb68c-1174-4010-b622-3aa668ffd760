-- -- insert default data tbl users
-- INSERT INTO public.users
-- (user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, "status", province_id)
-- VALUES('admin', NULL, NULL, 'admin', NULL, '', '54cf442114ed03851cc48925c6181a5b.ebabc7bbd347bd809bea91011c76c76948ef39ff255b82c08b92603fdd0769a6dd2566c138dde83e7ea2a937bc1bdbcca3de25adb3fd817c9232a1040d9d6501', '00', 0, NULL, '2024-09-24 03:13:39.991', NULL, false, NULL, NULL, NULL, NULL, NULL, 'admin', NULL, 0, 0);

-- -- insert default data tbl enterprises
-- INSERT INTO public.enterprises
-- (enterprise_code, enterprise_name, enterprise_name_kana, enterprise_name_nospace, enterprise_name_kana_nospace, "type", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('9999999', '海外', NULL, '海外', NULL, 99, 0, NULL, '2024-09-24 03:57:50.411', NULL, false);
-- INSERT INTO public.enterprises
-- (enterprise_code, enterprise_name, enterprise_name_kana, enterprise_name_nospace, enterprise_name_kana_nospace, "type", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('9990000', '養殖物（人工種苗）', NULL, '養殖物（人工種苗）', NULL, 98, 0, NULL, '2024-09-24 03:57:50.411', NULL, false);

-- -- Insert users and link them to enterprises
-- INSERT INTO public.users
-- (user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, status, province_id)
-- VALUES('9999999-0000', NULL, (SELECT id FROM public.enterprises WHERE enterprise_code = '9999999'), '', NULL, '', 'password_hash_1', '00', 0, NULL, '2024-09-24 04:00:00.000', NULL, false, NULL, NULL, NULL, NULL, NULL, '', NULL, 0, 0);

-- INSERT INTO public.users
-- (user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, status, province_id)
-- VALUES('9990000-0000', NULL, (SELECT id FROM public.enterprises WHERE enterprise_code = '9990000'), '', NULL, '', 'password_hash_2', '00', 0, NULL, '2024-09-24 04:00:00.000', NULL, false, NULL, NULL, NULL, NULL, NULL, '', NULL, 0, 0);

-- -- insert default data tbl sys_settings
-- INSERT INTO public.sys_settings
-- (setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('edit_deadline_for_arrival', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
-- INSERT INTO public.sys_settings
-- (setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('catching_reserve_period', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
-- INSERT INTO public.sys_settings
-- (setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('weight_alert_threshold', '20', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
-- insert default data tbl sys_settings
INSERT INTO public.sys_settings
(setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES('edit_deadline_for_arrival_shipping', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
INSERT INTO public.sys_settings
(setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES('catching_reserve_period', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
INSERT INTO public.sys_settings
(setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES('weight_alert_threshold', '20', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);

INSERT INTO provinces (id, name, setting, region_id)
VALUES
(1, '北海道', '{"is_show_info": 0}', 1),
(2, '青森県', '{"is_show_info": 0}', 2),
(3, '岩手県', '{"is_show_info": 0}', 2),
(4, '宮城県', '{"is_show_info": 0}', 2),
(5, '秋田県', '{"is_show_info": 0}', 2),
(6, '山形県', '{"is_show_info": 0}', 2),
(7, '福島県', '{"is_show_info": 0}', 2),
(8, '茨城県', '{"is_show_info": 0}', 3),
(9, '栃木県', '{"is_show_info": 0}', 3),
(10, '群馬県', '{"is_show_info": 0}', 3),
(11, '埼玉県', '{"is_show_info": 0}', 3),
(12, '千葉県', '{"is_show_info": 0}', 3),
(13, '東京都', '{"is_show_info": 0}', 3),
(14, '神奈川県', '{"is_show_info": 0}', 3),
(15, '新潟県', '{"is_show_info": 0}', 4),
(16, '富山県', '{"is_show_info": 0}', 4),
(17, '石川県', '{"is_show_info": 0}', 4),
(18, '福井県', '{"is_show_info": 0}', 4),
(19, '山梨県', '{"is_show_info": 0}', 4),
(20, '長野県', '{"is_show_info": 0}', 4),
(21, '岐阜県', '{"is_show_info": 0}', 4),
(22, '静岡県', '{"is_show_info": 0}', 4),
(23, '愛知県', '{"is_show_info": 0}', 4),
(24, '三重県', '{"is_show_info": 0}', 5),
(25, '滋賀県', '{"is_show_info": 0}', 5),
(26, '京都府', '{"is_show_info": 0}', 5),
(27, '大阪府', '{"is_show_info": 0}', 5),
(28, '兵庫県', '{"is_show_info": 0}', 5),
(29, '奈良県', '{"is_show_info": 0}', 5),
(30, '和歌山県', '{"is_show_info": 0}', 5),
(31, '鳥取県', '{"is_show_info": 0}', 6),
(32, '島根県', '{"is_show_info": 0}', 6),
(33, '岡山県', '{"is_show_info": 0}', 6),
(34, '広島県', '{"is_show_info": 0}', 6),
(35, '山口県', '{"is_show_info": 0}', 6),
(36, '徳島県', '{"is_show_info": 0}', 7),
(37, '香川県', '{"is_show_info": 0}', 7),
(38, '愛媛県', '{"is_show_info": 0}', 7),
(39, '高知県', '{"is_show_info": 0}', 7),
(40, '福岡県', '{"is_show_info": 0}', 8),
(41, '佐賀県', '{"is_show_info": 0}', 8),
(42, '長崎県', '{"is_show_info": 0}', 8),
(43, '熊本県', '{"is_show_info": 0}', 8),
(44, '大分県', '{"is_show_info": 0}', 8),
(45, '宮崎県', '{"is_show_info": 0}', 8),
(46, '鹿児島県', '{"is_show_info": 1}', 8),
(47, '沖縄県', '{"is_show_info": 0}', 8);

INSERT INTO regions (id, region_name, created_by_id, latest_updated_by_id, created_on, delete_flag)
VALUES
(1, '北海道', 0, 0, NOW(), FALSE),
(2, '東北',   0, 0, NOW(), FALSE),
(3, '関東',   0, 0, NOW(), FALSE),
(4, '中部',   0, 0, NOW(), FALSE),
(5, '近畿',   0, 0, NOW(), FALSE),
(6, '中国',   0, 0, NOW(), FALSE),
(7, '四国',   0, 0, NOW(), FALSE),
(8, '九州',   0, 0, NOW(), FALSE);