<template>
  <q-input
    v-model="model"
    mask="time"
    bg-color="white"
    ref="inputRef"
    outlined
    hide-bottom-space
    input-class="tw:tl:text-[2rem] tw:tl:leading-[2rem]
      tw:text-base tw:font-normal"
    autocomplete="nope"
    no-error-icon
    class="vs icon"
  >
    <template v-slot:append>
      <q-icon name="access_time" class="cursor-pointer" :size="computedFontSize">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-time v-model="model">
            <div class="row items-center justify-end">
              <q-btn
                v-close-popup
                label="閉じる"
                flat
                class="tw:text-white"
              />
            </div>
          </q-time>
        </q-popup-proxy>
      </q-icon>
    </template>
  </q-input>
</template>
<script setup>
import { ref, onMounted } from 'vue';

const model = defineModel();
const inputRef = ref(null);
const computedFontSize = ref('16px');

onMounted(() => {
  const el = inputRef.value?.$el?.querySelector('.q-field__control');

  if (el) {
    computedFontSize.value = getComputedStyle(el).height;
  }
});
</script>
