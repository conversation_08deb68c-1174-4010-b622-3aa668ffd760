<template>
  <div class="tw:w-[40%] tw:min-w-[550px] tw:m-auto">
    <!-- title -->
    <div
      class="tw:text-center tw:text-2xl tw:py-5 tw:font-bold"
    >
      ユーザーIDでログイン
    </div>
    <div class="tw:leading-[2rem] tw:mt-4 tw:text-base">
      ユーザーIDとパスワードを入力して、ログインボタンを押してください。
    </div>
    <!-- content -->
    <div class="tw:mt-3">
      <span class="tw:text-base">
        ユーザーID<span class="tw:text-red">*</span>
      </span>
      <q-input
        class="tw:mt-3"
        outlined
        v-model.trim="userId"
        autocomplete="on"
        lazy-rules
        maxlength="256"
        :error="!!errors.userid"
        :error-message="errors.userid"
        no-error-icon
      />
      <div class="tw:mt-5">
        <span class="tw:text-base">
          パスワード<span class="tw:text-red">*</span>
        </span>
        <q-input
          class="tw:mt-3"
          outlined
          v-model="password"
          autocomplete="on"
          :type="showPassword ? 'text' : 'password'"
          lazy-rules
          maxlength="256"
          :error="!!errors.password"
          :error-message="errors.password"
          no-error-icon
        >
          <template v-slot:append>
            <q-icon
              :name="showPassword ? 'visibility' : 'visibility_off'"
              @click.prevent="showPassword = !showPassword"
            />
          </template>
        </q-input>
      </div>
      <div class="text-center">
        <q-btn
          class="tw:w-full tw:mt-5 tw:font-bold tw:text-white tw:text-base"
          label="ログイン"
          type="button"
          :class="`tw:bg-${colorSub}`"
          @click.prevent="login"
        />
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useAuthStore } from 'stores/auth-store';
import { useRouter } from 'vue-router';
import authService from 'services/auth.service';
import useValidate from 'composables/validate';
import loginUserSchema from 'schemas/loginUser';

const { colorSub } = storeToRefs(useAppStore());
const { userLink } = storeToRefs(useAuthStore());

const router = useRouter();
const { errors, validateData } = useValidate();

// ===== REF =====
const userId = ref('');
const password = ref('');
const showPassword = ref(false);

// ===== METHOD =====

const login = async () => {
  const formData = {
    userid: userId.value,
    password: password.value,
  };
  const valid = validateData(loginUserSchema, formData);
  if (!valid) {
    return;
  }
  const response = await authService.adminLogin(formData);

  if (response) {
    await router.push({
      name: 'adminUserManager',
    });
  }
};

onMounted(() => {
  if (userLink.value?.user_code) {
    userId.value = userLink.value?.user_code;
  }
});
</script>
