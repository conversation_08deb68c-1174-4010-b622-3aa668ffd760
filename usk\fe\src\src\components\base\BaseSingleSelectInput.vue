<template>
  <q-select
    class="`tw:tl:text-[2rem] tw:tl:leading-[2rem]
      tw:text-base tw:font-normal`"
    use-input
    outlined
    input-debounce="0"
    no-error-icon
    clearable
    hide-bottom-space
    input-class="tw:text-m-design tw:font-normal"
    popup-content-class="popup-content-class-custom"
  >
    <template v-slot:no-option>
      <q-item>
        <q-item-section class="text-grey">データが見つかりません。</q-item-section>
      </q-item>
    </template>
    <template v-for="(_, name) in $slots" v-slot:[name]="scope" :key="name">
      <slot :name="name" :props="scope" />
    </template>
  </q-select>
</template>
<script setup></script>
<style scoped>
:deep(.q-field__native span) {
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 400;
}

@media (min-width: 960px) {
  :deep(.q-field__native span) {
    font-size: 2rem;
    line-height: 2rem;
  }
}
</style>
