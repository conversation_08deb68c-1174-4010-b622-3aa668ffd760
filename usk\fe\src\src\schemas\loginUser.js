import MESSAGE from 'helpers/message';

const loginUserSchema = {
  type: 'object',
  required: [
    'userid',
    'password',
  ],
  properties: {
    userid: {
      type: 'string',
      minLength: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    password: {
      type: 'string',
      minLength: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      userid: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      password: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  additionalProperties: false,
};

export default loginUserSchema;
