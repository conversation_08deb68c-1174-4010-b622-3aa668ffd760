import MESSAGE from 'helpers/message';

const searchExportShipmentCondition = {
  type: 'object',
  additionalProperties: false,
  properties: {
    code: {
      type: 'string',
      maxLength: 16,
      pattern: '^[0-9]{1,16}$',
      errorMessage: {
        pattern: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    enterpriseName: {
      type: 'string',
      maxLength: 256,
    },
    startDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_DATE_CONSTRAINT_ERROR,
      },
    },
    endDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
  },
};

export default searchExportShipmentCondition;
