version: 0.2
phases:
  install:
    commands:
      # - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &
      # - timeout 15 sh -c "until docker info; do echo .; sleep 1; done"
  pre_build:
    commands:
      - pwd
      - ls
      - cd shared/common
      - ls
      - echo Logging in to Amazon ECR...
      - aws --version
      - AWS_DEFAULT_REGION=ap-northeast-1
      - echo $AWS_DEFAULT_REGION
      - echo $REPOSITORY_PATH
      - echo $REPOSITORY_NAME
      - echo $VERSION
      - aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin $REPOSITORY_PATH
      - REPOSITORY_URI=$REPOSITORY_PATH/$REPOSITORY_NAME
      - IMAGE_TAG_NAME=common-backend-image_$VERSION
  build:
    commands:
      - echo Build started on `date`
      - docker build -t $IMAGE_TAG_NAME .
      - echo $REPOSITORY_URI:$IMAGE_TAG_NAME
      - docker tag $IMAGE_TAG_NAME $REPOSITORY_URI:$IMAGE_TAG_NAME
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      - docker push $REPOSITORY_URI:$IMAGE_TAG_NAME
      - echo Writing image definition file...
      - echo Done
