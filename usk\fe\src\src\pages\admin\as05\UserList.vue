<template>
  <div class="tw:mx-10">
    <!-- #region start filter -->
    <div
      class="tw:grid tw:grid-cols-1 tw:dt:grid-cols-4 tw:lg:grid-cols-2 tw:mt-2 tw:gap-3"
    >
      <!-- province -->
      <div class="tw:font-normal tw:text-base">
        <div class="tw:mb-1">都道府県</div>
        <q-select
          outlined
          map-options
          emit-value
          v-model="searchForm.provinceId"
          :options="optionsRegions"
          class="tw:text-base"
          :error="!!errors.provinceId"
          :error-message="errors.provinceId"
          no-error-icon
        >
          <template v-slot:option="scope">
            <div>
              <div
                class="text-weight-bold text-black q-pl-sm q-pt-xs q-pb-xs"
                style="pointer-events: none"
              >
                {{ scope.opt.label }}
              </div>

              <div v-for="child in scope.opt.children" :key="child.value">
                <q-item
                  dense
                  clickable
                  v-ripple
                  v-close-popup
                  @click="scope.toggleOption(child)"
                  :class="{
                    'bg-light-blue-1': searchForm.provinceId === child.value,
                  }"
                >
                  <q-item-section>
                    <q-item-label>{{ child.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </div>
            </div>
          </template>

          <template v-slot:selected-item>
            <q-item v-if="selectedProvince" class="tw:p-0">
              <q-item-section>
                <q-item-label>{{ selectedProvince.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
      <!-- enterpriseName -->
      <div class="tw:text-base">
        <div class="tw:mb-1">届出事業者名</div>
        <q-input
          v-model.trim="searchForm.enterpriseName"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="50"
          :error="!!errors.enterpriseName"
          :error-message="errors.enterpriseName"
          no-error-icon
        />
      </div>
      <!-- user name -->
      <div class="tw:text-base">
        <div class="tw:mb-1">事業者名</div>
        <q-input
          v-model.trim="searchForm.userName"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="50"
          :error="!!errors.userName"
          :error-message="errors.userName"
          no-error-icon
        />
      </div>
      <!-- user ID -->
      <div class="tw:text-base">
        <div class="tw:mb-1">ユーザーID</div>
        <q-input
          v-model.trim="searchForm.userId"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="12"
          :error="!!errors.userId"
          :error-message="errors.userId"
          no-error-icon
        />
      </div>
    </div>
    <div
      class="tw:grid tw:grid-cols-1 tw:dt:grid-cols-4 tw:lg:grid-cols-2 tw:mt-2 tw:gap-3"
    >
      <!-- status -->
      <div class="tw:text-base">
        <div class="tw:mb-1">状態</div>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionsStatus"
          v-model="searchForm.status"
          class="tw:text-base"
          :error="!!errors.status"
          :error-message="errors.status"
          no-error-icon
        />
      </div>
      <!-- status register -->
      <div class="tw:text-base">
        <div class="tw:mb-1">アカウント登録状況</div>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionsStatusRegister"
          v-model="searchForm.statusRegister"
          class="tw:text-base"
          :error="!!errors.statusRegister"
          :error-message="errors.statusRegister"
          no-error-icon
        />
      </div>
      <!-- role user -->
      <div class="tw:text-base">
        <div class="tw:mb-1">事業者/従事者区分</div>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionsUserType"
          v-model="searchForm.userType"
          :error="!!errors.userType"
          :error-message="errors.userType"
          no-error-icon
        />
      </div>
      <!-- license number -->
      <div class="tw:text-base">
        <div class="tw:mb-1">許可番号</div>
        <q-input
          v-model.trim="searchForm.licenseNumber"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.licenseNumber"
          :error-message="errors.licenseNumber"
          no-error-icon
        />
      </div>
    </div>
    <div class="tw:grid tw:grid-cols-1 tw:dt:grid-cols-10 tw:mt-2 tw:gap-2">
      <!-- update date -->
      <div class="tw:col-span-1 tw:dt:col-span-3 tw:text-base">
        <div class="tw:mb-1">更新日</div>
        <div class="tw:flex tw:gap-2 tw:mb-1 tw:items-center">
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchForm.startUpdateDate"
              :error="!!errors.startUpdateDate"
              :error-message="errors.startUpdateDate"
              input-class="tw:text-base"
            />
          </div>
          <span class="tw:text-[0.77rem] tw:mb-[18px]"> ～ </span>
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchForm.endUpdateDate"
              :error="!!errors.endUpdateDate"
              :error-message="errors.endUpdateDate"
              input-class="tw:text-base"
            />
          </div>
        </div>
      </div>
      <!-- expiry date  -->
      <div class="tw:col-span-1 tw:dt:col-span-3 tw:text-base">
        <div class="tw:mb-1">採捕許可有効期限</div>
        <div class="tw:flex tw:gap-2 tw:mb-1 tw:items-center">
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchForm.startExpiryDate"
              :error="!!errors.startExpiryDate"
              :error-message="errors.startExpiryDate"
              input-class="tw:text-base"
            />
          </div>
          <span class="tw:text-[0.77rem] tw:mb-[18px]"> ～ </span>
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchForm.endExpiryDate"
              :error="!!errors.endExpiryDate"
              :error-message="errors.endExpiryDate"
              input-class="tw:text-base"
            />
          </div>
        </div>
      </div>
      <!-- note 1 -->
      <div class="tw:col-span-1 tw:dt:col-span-2 tw:text-base">
        <div class="tw:mb-1">備考１</div>
        <q-input
          v-model.trim="searchForm.note1"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.note1"
          :error-message="errors.note1"
          no-error-icon
        />
      </div>
      <!-- note 2 -->
      <div class="tw:col-span-1 tw:dt:col-span-2 tw:text-base">
        <div class="tw:mb-1">備考２</div>
        <q-input
          v-model.trim="searchForm.note2"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.note2"
          :error-message="errors.note2"
          no-error-icon
        />
      </div>
    </div>
    <!-- #endregion end filter -->
    <!-- #region start button search -->
    <div
      class="tw:flex tw:flex-row-reverse tw:gap-4 tw:pt-2 tw:border-b tw:border-gray tw:pb-4"
    >
      <BaseButton
        padding="0.75rem"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
        label="検索"
        @click.prevent="submitFormSearch"
      />
      <BaseButton
        outline
        padding="0.75rem"
        :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
        label="クリア"
        @click.prevent="clearFormSearch"
      />
    </div>
    <!-- #endregion end button search -->
    <!-- #region start content -->
    <!-- button -->
    <div class="tw:flex tw:justify-between tw:mt-2">
      <div class="tw:flex tw:gap-3 tw:justify-center tw:items-center">
        <PageSizeDropdown v-model="pageSize" />
        <q-checkbox v-model="showPending" label="非表示のユーザーを表示" @click="handleShowPending" />
      </div>
      <div class="tw:flex tw:gap-3">
        <!-- import csv -->
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-56 tw:text-[1rem]`"
          label="ユーザー設定CSVを取込"
          @click.prevent="openImport"
        /><!-- add user -->
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
          label="ユーザーを追加"
          @click.prevent="openRegister"
        />
      </div>
    </div>
    <q-table
      :rows="sortedRows"
      :columns="columns"
      row-key="user_code"
      hide-pagination
      v-model:pagination="pagination"
      class="tw:py-3"
      :table-header-class="[
        `tw:bg-${colorMain} tw:text-left tw:text-base tw:text-[white]`,
      ]"
    >
      <template v-slot:header="props">
        <q-tr
          :props="props"
          class="tw:text-[white]"
          :class="[`tw:bg-${colorMain}`]"
        >
          <q-th
            v-for="col in props.cols"
            :key="col.name"
            :class="[
              col.headerClasses,
              col.align === 'center' ? 'tw:text-center' : 'tw:text-left',
              'tw:cursor-pointer',
            ]"
            @click="handleClickSort(col.name)"
          >
            {{ col.label }}
            <q-icon
              v-if="getSortOrder(col.name)"
              :name="
                getSortOrder(col.name) === 'asc'
                  ? 'arrow_upward'
                  : 'arrow_downward'
              "
              size="16px"
              class="tw:ml-1"
            />
          </q-th>
        </q-tr>
      </template>
      <template v-slot:body="props">
        <q-tr
          class="tw:cursor-pointer"
          :props="props"
          @click.prevent="userDetail(props.row.id)"
        >
          <q-td key="enterpriseName" :props="props" class="tw:min-w-56">
            <div class="tw:truncate tw:max-w-[15rem]">
              {{ props.row?.enterprise?.enterprise_name }}
            </div>
            <q-tooltip>
              {{ props.row?.enterprise?.enterprise_name }}
            </q-tooltip>
          </q-td>
          <q-td key="user_name" :props="props" class="tw:min-w-56">
            <div class="tw:truncate tw:max-w-[15rem]">
              {{ props.row?.name }}
            </div>
            <q-tooltip>
              {{ props.row?.name }}
            </q-tooltip>
          </q-td>
          <q-td key="user_code" :props="props" class="tw:max-w-52">
            <div class="tw:truncate tw:w-[10rem]">
              {{ props.row?.user_code }}
            </div>
            <q-tooltip>
              {{ props.row?.user_code }}
            </q-tooltip>
          </q-td>
          <q-td
            key="status"
            :props="props"
            class="tw:w-20"
            :class="{
              'tw:text-[#F4511E]': props.row?.status === 2,
              'tw:text-[#FFAE00]': props.row?.status === 1,
              'tw:text-[#3F83F8]': props.row?.status === 0,
            }"
          >
            {{ showStatus(props.row?.status) }}
            <q-tooltip>
              {{ showStatus(props.row?.status) }}
            </q-tooltip>
          </q-td>
          <q-td key="role" :props="props" class="">
            <div class="tw:truncate tw:w-[10rem]">
              {{ showRole(props.row) }}
            </div>
            <q-tooltip>
              {{ showRole(props.row) }}
            </q-tooltip>
          </q-td>
          <q-td key="license_number" :props="props" class="">
            <div class="tw:truncate tw:w-[10rem]">
              {{ props.row?.license_number }}
            </div>
            <q-tooltip>
              {{ props.row?.license_number }}
            </q-tooltip>
          </q-td>
          <q-td key="update_date" :props="props" class="tw:text-center tw:w-40">
            {{ FORMAT_DATE(props.row?.latest_updated_on) }}
            <q-tooltip>
              {{ FORMAT_DATE(props.row?.latest_updated_on) }}
            </q-tooltip>
          </q-td>
          <q-td key="expiry_date" :props="props" class="tw:text-center tw:w-40">
            {{ FORMAT_DATE(props.row.license?.expiry_date) }}
            <q-tooltip>
              {{ FORMAT_DATE(props.row.license?.expiry_date) }}
            </q-tooltip>
          </q-td>
        </q-tr>
      </template>
      <template v-slot:no-data="">
        <div class="tw:w-full tw:text-center tw:text-base tw:leading-[2rem]">
          データが見つかりません。
        </div>
      </template>
    </q-table>
    <!-- footer content -->
    <div class="tw:flex tw:justify-between">
      <!-- export csv -->
      <div class="tw:flex tw:gap-3">
        <q-btn
          v-if="listUser.length > 0"
          padding="0.6rem"
          class="tw:text-base tw:font-[700] tw:tl:leading-[2rem] tw:text-white"
          :class="`tw:bg-${colorSub}`"
          label="検索条件のユーザー設定CSVを出力"
          @click="exportCsvUser"
        />
        <q-btn
          padding="0.6rem"
          class="tw:text-base tw:font-[700] tw:tl:leading-[2rem] tw:text-white"
          :class="`tw:bg-${colorSub}`"
          label="一括「無許可」化"
          @click="goToPage('adminBatchNonActiveUpdate')"
        />
      </div>
      <!-- pagination start -->
      <div class="tw:flex tw:justify-end">
        <PaginationItem />
      </div>
    </div>
    <!-- #endregion start content -->
  </div>
</template>
<script setup>
// #region import
import { onMounted, ref, watch, computed, provide } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import useValidate from 'composables/validate';
import BaseDatePicker from 'components/base/BaseDatePicker.vue';
import PageSizeDropdown from 'components/PageSizeDropdown.vue';
import {
  OPTIONS_STATUS_USER,
  USER_ROLE_OPTIONS,
  ENTERPRISE_TYPE_ENUM,
  OPTIONS_STATUS_REGISTER_ACCOUNT,
  STATUS_USER_ENUM,
} from 'src/helpers/constants';
import searchUserList from 'src/schemas/admin/user/searchUserList';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import {
  exportCSV,
  FORMAT_DATE,
  FORMAT_DATE_TIME_CSV,
  showStatus,
  showRole,
} from 'src/helpers/common';
import { useRouter } from 'vue-router';
import PaginationItem from 'components/PaginationItem.vue';
import BaseButton from 'src/components/base/BaseButton.vue';
import { useLocalStorage } from 'composables/localstorage';
import { isEqual } from 'lodash';
import regionsService from 'src/shared/services/regions.service';

// #endregion import

// #region variable
const router = useRouter();
const { colorMain, colorSub } = storeToRefs(useAppStore());
const { setAdminRegisterUser, setLoadingManual, setLoading } = useAppStore();
const { errors, validateData } = useValidate();
const { listPageQueryParams } = useLocalStorage();
const showPending = ref(false);
const page = ref(1);
const multiSortConditions = ref([]);
const columns = [
  {
    name: 'enterpriseName',
    label: '届出事業者名',
    align: 'left',
    field: 'enterpriseName',
    headerClasses: 'tw:text-base tw:min-w-56',
  },
  {
    name: 'user_name',
    label: '事業者名',
    align: 'left',
    field: 'user_name',
    headerClasses: 'tw:text-base tw:w-52',
  },
  {
    name: 'user_code',
    label: 'ユーザーID',
    align: 'left',
    field: 'user_code',
    headerClasses: 'tw:text-base tw:w-32',
  },
  {
    name: 'status',
    align: 'left',
    label: '状態',
    field: 'status',
    headerClasses: 'tw:text-base tw:w-48',
  },
  {
    name: 'role',
    align: 'left',
    label: '事業者/従事者区分',
    field: 'role',
    headerClasses: 'tw:text-base tw:w-48',
  },
  {
    name: 'license_number',
    align: 'left',
    label: '許可番号',
    field: 'license_number',
    headerClasses: 'tw:text-base tw:w-40',
  },
  {
    name: 'update_date',
    align: 'center',
    label: '更新日',
    field: 'update_date',
    headerClasses: 'tw:text-base tw:w-40',
  },
  {
    name: 'expiry_date',
    align: 'left',
    label: '採捕許可有効期限',
    field: 'expiry_date',
    headerClasses: 'tw:text-base tw:w-40',
  },
];
const pagination = ref({
  page: page.value,
  rowsPerPage: listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10,
});

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }
};

const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const sortedRows = computed(() => {
  const rows = [...listUser.value];

  return rows.sort((a, b) => {
    for (const { key, order } of multiSortConditions.value) {
      let aVal = '',
        bVal = '';

      switch (key) {
        case 'enterpriseName':
          aVal = a.enterprise?.enterprise_name || '';
          bVal = b.enterprise?.enterprise_name || '';
          break;
        case 'user_name':
          aVal = a.name || '';
          bVal = b.name || '';
          break;
        case 'user_code':
          aVal = a.user_code || '';
          bVal = b.user_code || '';
          break;
        case 'status':
          aVal = a.status ?? '';
          bVal = b.status ?? '';
          break;
        case 'role':
          aVal = showRole(a) || '';
          bVal = showRole(b) || '';
          break;
        case 'license_number':
          aVal = a.license_number || '';
          bVal = b.license_number || '';
          break;
        case 'update_date':
          aVal = a.latest_updated_on || '';
          bVal = b.latest_updated_on || '';
          break;
        case 'expiry_date':
          aVal = a.license?.expiry_date || '';
          bVal = b.license?.expiry_date || '';
          break;
        default:
          aVal = a[key] ?? '';
          bVal = b[key] ?? '';
      }

      // Convert dates if applicable
      const isDate = key.includes('date');
      if (isDate) {
        aVal = new Date(aVal).getTime() || 0;
        bVal = new Date(bVal).getTime() || 0;
      }

      // Normalize comparison
      if (aVal < bVal) {
        return order === 'asc' ? -1 : 1;
      }
      if (aVal > bVal) {
        return order === 'asc' ? 1 : -1;
      }
    }

    return 0;
  });
});
const totalPage = ref(1);
const pageSize = ref(
  listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10
);
const pageIndex = ref(1);
const hasDiffPageIndex = ref(false);
const optionsRegions = ref([]);
const selectedProvince = ref(null);

const searchForm = ref({
  provinceId: '',
  enterpriseName: '',
  userName: '',
  userId: '',
  status: '',
  statusRegister: '',
  licenseNumber: '',
  userType: '',
  startUpdateDate: '',
  endUpdateDate: '',
  startExpiryDate: '',
  endExpiryDate: '',
  note1: '',
  note2: '',
});
const searchFormData = ref({
  provinceId: '',
  enterpriseName: '',
  userName: '',
  userId: '',
  status: '',
  statusRegister: '',
  licenseNumber: '',
  userType: '',
  startUpdateDate: '',
  endUpdateDate: '',
  startExpiryDate: '',
  endExpiryDate: '',
  note1: '',
  note2: '',
});

const headersJP = ref([]);

const headersJPDefault = ref([
  'URL+カードID',
  'トレサビシステムユーザーID',
  '初期パスワード',
  '事業者種別',
  '届出番号',
  '事業者/従事者区分',
  '許可番号',
  '氏名',
  '氏名（カナ）',
  '都道府県',
  '連絡先等',
  '許可の有効期間（開始）',
  '許可の有効期間（終了）',
  '状態',
  'アカウント登録状況',
  '備考1',
  '備考2',
]);

const header = ref([
  'urlcardid',
  'user_code',
  'init_password',
  'enterprise_type',
  'enterprise_code',
  'staff_type',
  'license_number',
  'name',
  'name_kana',
  'province_name',
  'phone',
  'expiry_date_start',
  'expiry_date',
  'status',
  'account_registration_status',
  'note_1',
  'note_2',
]);

const headerDefault = ref([
  'urlcardid',
  'user_code',
  'init_password',
  'enterprise_type',
  'enterprise_code',
  'staff_type',
  'license_number',
  'name',
  'name_kana',
  'province_name',
  'phone',
  'expiry_date_start',
  'expiry_date',
  'status',
  'account_registration_status',
  'note_1',
  'note_2',
]);
const optionsStatus = ref(
  OPTIONS_STATUS_USER.map(item => ({
    label: item.label,
    value: item.value.toString(),
  }))
);
const optionsStatusRegister = ref(
  OPTIONS_STATUS_REGISTER_ACCOUNT.map(item => ({
    label: item.label,
    value: item.value.toString(),
  }))
);
const optionsUserType = ref(USER_ROLE_OPTIONS);
const listUser = ref([]);
const dataExport = ref([]);

// #region function
const getProvinceLabelById = (provinceId, options) => {
  for (const region of options) {
    const found = region.children.find(p => p.value === provinceId);
    if (found) {
      return found.label;
    }
  }
  return '';
};

const formatSearchFormToQuery = value => {
  const query = {
    provinceId: value.provinceId ? +value.provinceId : undefined,
    enterpriseName: value.enterpriseName.trim() || undefined,
    userName: value.userName.trim() || undefined,
    userId: value.userId.trim() || undefined,
    status: value.status ? +value.status : undefined,
    statusRegister: value.statusRegister ? +value.statusRegister : undefined,
    licenseNumber: value.licenseNumber.trim() || undefined,
    userType: value.userType || undefined,
    startUpdateDate: value.startUpdateDate || undefined,
    endUpdateDate: value.endUpdateDate || undefined,
    startExpiryDate: value.startExpiryDate || undefined,
    endExpiryDate: value.endExpiryDate || undefined,
    note1: value.note1.trim() || undefined,
    note2: value.note2.trim() || undefined,
  };
  return query;
};

const formatPaginationToQuery = value => {
  const query = {
    page: value.page,
    rowsPerPage: listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10,
  };
  return query;
};

const getListUser = async (isExport = false) => {
  setLoadingManual(true);
  setLoading(true);
  const searchCondition = {
    provinceId: searchFormData.value.provinceId || undefined,
    enterpriseName: searchFormData.value.enterpriseName || undefined,
    userName: searchFormData.value.userName || undefined,
    userId: searchFormData.value.userId || undefined,
    status: searchFormData.value.status || undefined,
    statusRegister: searchFormData.value.statusRegister || undefined,
    licenseNumber: searchFormData.value.licenseNumber || undefined,
    userType: searchFormData.value.userType || undefined,
    startUpdateDate: searchFormData.value.startUpdateDate || undefined,
    endUpdateDate: searchFormData.value.endUpdateDate || undefined,
    startExpiryDate: searchFormData.value.startExpiryDate || undefined,
    endExpiryDate: searchFormData.value.endExpiryDate || undefined,
    note1: searchFormData.value.note1 || undefined,
    note2: searchFormData.value.note2 || undefined,
    showPending: showPending.value,
  };

  const result = await userAdminService.getUserList(searchCondition);
  const provinceCustomHeader = [];
  if (result) {
    const filteredItems =
      result.items?.filter(item => {
        if (!showPending.value) {
          return item.status !== 2;
        }
        return true;
      }) || [];

    if (!isExport) {
      pageIndex.value = 1;
      page.value = 1;
      listUser.value = filteredItems;
      totalPage.value = Math.ceil(
        filteredItems.length / pagination.value.rowsPerPage
      );
    } else {
      headersJP.value = [...headersJPDefault.value];
      header.value = [...headerDefault.value];
      dataExport.value = filteredItems.map(item => {
        if (
          item.province_custom_data &&
          item.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
        ) {
          item.province_custom_data = JSON.parse(
            JSON.stringify(item.province_custom_data)
          );

          Object.keys(item.province_custom_data).forEach(key => {
            if (!provinceCustomHeader.includes(key)) {
              provinceCustomHeader.push(key);
              headersJP.value.push(key);
              header.value.push(key);
            }
          });
        }
        const data = {
          urlcardid: item?.license?.license_code
            ? `${process.env.SITE_URL}/login/${item?.license?.license_code}`
            : '',
          user_code: item.user_code,
          init_password:
            item?.account_registration_status === 0 ? item.password : '',
          enterprise_type: item.enterprise_type,
          enterprise_code: `'${item.user_code.split('-')[0]}`,
          staff_type: item.staff_type,
          license_number: item.license_number,
          name: item.name,
          name_kana: item.name_kana || '',
          province_name: getProvinceLabelById(
            item.province_id,
            optionsRegions.value
          ),
          phone: item.phone,
          expiry_date_start:
            FORMAT_DATE(item?.license?.expiry_date_start, 'YYYY-MM-DD') || '',
          expiry_date:
            FORMAT_DATE(item?.license?.expiry_date, 'YYYY-MM-DD') || '',
          status: item.status,
          account_registration_status: item.account_registration_status,
          note_1: item.note_1,
          note_2: item.note_2,
          ...item.province_custom_data,
        };

        return data;
      });
    }
    setLoadingManual(false);
    setLoading(false);
  }
};

const submitFormSearch = async () => {

  if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
    await getListUser();
    return;
  }
    if (+searchForm.value.status === STATUS_USER_ENUM.PENDING) {
    showPending.value = true;
  }

  const data = {
    provinceId: searchForm.value.provinceId || undefined,
    enterpriseName: searchForm.value.enterpriseName.trim() || undefined,
    userName: searchForm.value.userName.trim() || undefined,
    userId: searchForm.value.userId.trim() || undefined,
    status:
      searchForm.value.status === '' ? undefined : searchForm.value.status,
    statusRegister:
      searchForm.value.statusRegister === ''
        ? undefined
        : searchForm.value.statusRegister,
    userType: searchForm.value.userType || undefined,
    startUpdateDate: searchForm.value.startUpdateDate || undefined,
    endUpdateDate: searchForm.value.endUpdateDate || undefined,
    startExpiryDate: searchForm.value.startExpiryDate || undefined,
    endExpiryDate: searchForm.value.endExpiryDate || undefined,
    licenseNumber: searchForm.value.licenseNumber.trim() || undefined,
    note1: searchForm.value.note1.trim() || undefined,
    note2: searchForm.value.note2.trim() || undefined,
  };

  const validate = validateData(searchUserList, {
    ...data,
  });

  if (validate) {
    pageIndex.value = 1;
    await router.push({
      query: {
        ...formatSearchFormToQuery(searchForm.value),
        page: 1,
      },
    });
  }
};

const clearFormSearch = () => {
  searchForm.value = {
    provinceId: '',
    enterpriseName: '',
    userName: '',
    userId: '',
    status: '',
    statusRegister: '',
    licenseNumber: '',
    userType: '',
    startUpdateDate: '',
    endUpdateDate: '',
    startExpiryDate: '',
    endExpiryDate: '',
    note1: '',
    note2: '',
  };

  errors.value = {};
};

const exportCsvUser = async () => {
  await getListUser(true);
  exportCSV(
    dataExport.value,
    header.value,
    headersJP.value,
    `ユーザー設定_${FORMAT_DATE_TIME_CSV('YYYYMMDDHHmm')}`
  );
};

const userDetail = userId => {
  localStorage.setItem('adminUserListPage', pagination.value.page);

  const currentQuery = router.currentRoute.value.query;
  localStorage.setItem('adminUserListQuery', JSON.stringify(currentQuery));

  router.push({
    name: 'adminUserDetail',
    params: {
      id: userId,
    },
  });
};

const openRegister = () => {
  router.push({
    name: 'adminUserRegister',
  });
};

const openImport = () => {
  router.push({
    name: 'adminUserImport',
  });
};

const goToPage = pageName => {
  localStorage.setItem('adminUserListPage', pagination.value.page);

  router.push({
    name: pageName,
  });
};

// #end region function

// #region computed
// #endregion

// #region watch
watch(pageSize, async () => {
  pagination.value.rowsPerPage = +pageSize.value;

  totalPage.value = Math.ceil(listUser.value.length / +pageSize.value);

  listPageQueryParams.value.adminUserManager = {
    rowsPerPage: +pageSize.value,
  };
});

watch(listPageQueryParams.value?.adminUserManager?.rowsPerPage, async () => {
  pageSize.value =
    +listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10;
  pagination.value.rowsPerPage =
    +listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10;
  totalPage.value = Math.ceil(listUser.value.length / +pageSize.value);
});

watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.descending +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.provinceId +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.userName +
    router.currentRoute.value.query.userId +
    router.currentRoute.value.query.status +
    router.currentRoute.value.query.statusRegister +
    router.currentRoute.value.query.licenseNumber +
    router.currentRoute.value.query.userType +
    router.currentRoute.value.query.startUpdateDate +
    router.currentRoute.value.query.endUpdateDate +
    router.currentRoute.value.query.startExpiryDate +
    router.currentRoute.value.query.endExpiryDate +
    router.currentRoute.value.query.note1 +
    router.currentRoute.value.query.note2,
  async () => {
    if (listPageQueryParams.value?.adminUserManager?.rowsPerPage) {
      listPageQueryParams.value.adminUserManager = {
        rowsPerPage:
          +listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10,
      };
    }

    if (!router.currentRoute.value?.query?.page) {
      router.replace({
        query: {},
      });
    } else {
      pageSize.value =
        +listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10;
      pageIndex.value = +router.currentRoute.value.query.page;
      pagination.value = {
        page: router.currentRoute.value.query.page,
        rowsPerPage:
          listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10,
        sortBy: router.currentRoute.value.query.sortBy,
        descending: router.currentRoute.value.query.descending === 'true',
      };
      searchForm.value = {
        provinceId: router.currentRoute.value.query.provinceId || '',
        enterpriseName: router.currentRoute.value.query.enterpriseName || '',
        userName: router.currentRoute.value.query.userName || '',
        userId: router.currentRoute.value.query.userId || '',
        status: router.currentRoute.value.query.status || '',
        statusRegister: router.currentRoute.value.query.statusRegister || '',
        licenseNumber: router.currentRoute.value.query.licenseNumber || '',
        userType: router.currentRoute.value.query.userType || '',
        startUpdateDate: router.currentRoute.value.query.startUpdateDate || '',
        endUpdateDate: router.currentRoute.value.query.endUpdateDate || '',
        startExpiryDate: router.currentRoute.value.query.startExpiryDate || '',
        endExpiryDate: router.currentRoute.value.query.endExpiryDate || '',
        note1: router.currentRoute.value.query.note1 || '',
        note2: router.currentRoute.value.query.note2 || '',
      };
      searchFormData.value = {
        provinceId: router.currentRoute.value.query.provinceId || '',
        enterpriseName: router.currentRoute.value.query.enterpriseName || '',
        userName: router.currentRoute.value.query.userName || '',
        userId: router.currentRoute.value.query.userId || '',
        status: router.currentRoute.value.query.status || '',
        statusRegister: router.currentRoute.value.query.statusRegister || '',
        licenseNumber: router.currentRoute.value.query.licenseNumber || '',
        userType: router.currentRoute.value.query.userType || '',
        startUpdateDate: router.currentRoute.value.query.startUpdateDate || '',
        endUpdateDate: router.currentRoute.value.query.endUpdateDate || '',
        startExpiryDate: router.currentRoute.value.query.startExpiryDate || '',
        endExpiryDate: router.currentRoute.value.query.endExpiryDate || '',
        note1: router.currentRoute.value.query.note1 || '',
        note2: router.currentRoute.value.query.note2 || '',
      };

      if (!hasDiffPageIndex.value) {
        await getListUser();
      } else {
        hasDiffPageIndex.value = false;
      }
    }
  }
);

// watch(showPending, async () => {
//   pagination.value.page = 1;
//   await getListUser();
// });

const handleShowPending = async () => {
  pagination.value.page = 1;
  await getListUser();
};

watch(() => {
  const provinceId =
    searchForm.value.provinceId || router.currentRoute.value.query.provinceId;

  if (!provinceId || optionsRegions.value.length === 0) {
    selectedProvince.value = null;
    return;
  }

  for (const region of optionsRegions.value) {
    const found = region.children.find(p => p.value === Number(provinceId));
    if (found) {
      selectedProvince.value = found;
      return;
    }
  }

  selectedProvince.value = null;
});
// #endregion watch

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
  },
};
provide('paginationItemProvideData', paginationItemProvideData);
// #endregion provide

// onMounted
onMounted(async () => {
  const listRegions = await regionsService.getRegionWithProvinces();
  optionsRegions.value = listRegions.payload.items.map(item => ({
    label: item.region_name,
    children: item.province.map(province => ({
      label: province.name,
      value: province.id,
    })),
  }));
  setAdminRegisterUser({
    enterpriseType: '',
    enterpriseCode: '',
    enterpriseName: '',
    enterpriseNameKana: '',
    password: '',
    typeStaffOrEnterprise: 0,
    provinceId: '',
    phone: '',
    licenseNumber: '',
    startExpiryDate: FORMAT_DATE(Date.now()),
    endExpiryDate: FORMAT_DATE(Date.now()),
    status: 0,
    note1: '',
    note2: '',
  });

  pageSize.value =
    +listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10;
  pageIndex.value = +router.currentRoute.value.query.page;
  pagination.value = {
    ...formatPaginationToQuery(pagination.value),
    page: +router.currentRoute.value.query.page,
    rowsPerPage:
      +listPageQueryParams.value?.adminUserManager?.rowsPerPage || 10,
    sortBy: router.currentRoute.value.query.sortBy,
    descending: router.currentRoute.value.query.descending === 'true',
  };
  searchForm.value = {
    provinceId:
      router.currentRoute.value.query.provinceId !== undefined
        ? router.currentRoute.value.query.provinceId
        : '',
    enterpriseName: router.currentRoute.value.query.enterpriseName || '',
    userName: router.currentRoute.value.query.userName || '',
    userId: router.currentRoute.value.query.userId || '',
    status:
      router.currentRoute.value.query.status !== undefined
        ? router.currentRoute.value.query.status
        : '',
    statusRegister:
      router.currentRoute.value.query.statusRegister !== undefined
        ? router.currentRoute.value.query.statusRegister
        : '',
    status:
      router.currentRoute.value.query.status !== undefined
        ? router.currentRoute.value.query.status
        : '',
    statusRegister:
      router.currentRoute.value.query.statusRegister !== undefined
        ? router.currentRoute.value.query.statusRegister
        : '',
    licenseNumber: router.currentRoute.value.query.licenseNumber || '',
    userType: router.currentRoute.value.query.userType || '',
    startUpdateDate: router.currentRoute.value.query.startUpdateDate || '',
    endUpdateDate: router.currentRoute.value.query.endUpdateDate || '',
    startExpiryDate: router.currentRoute.value.query.startExpiryDate || '',
    endExpiryDate: router.currentRoute.value.query.endExpiryDate || '',
    note1: router.currentRoute.value.query.note1 || '',
    note2: router.currentRoute.value.query.note2 || '',
  };
  searchFormData.value = {
    provinceId:
      router.currentRoute.value.query.provinceId !== undefined
        ? router.currentRoute.value.query.provinceId
        : '',
    enterpriseName: router.currentRoute.value.query.enterpriseName || '',
    userName: router.currentRoute.value.query.userName || '',
    userId: router.currentRoute.value.query.userId || '',
    statusRegister:
      router.currentRoute.value.query.statusRegister !== undefined
        ? router.currentRoute.value.query.statusRegister
        : '',
    status:
      router.currentRoute.value.query.status !== undefined
        ? router.currentRoute.value.query.status
        : '',
    statusRegister:
      router.currentRoute.value.query.statusRegister !== undefined
        ? router.currentRoute.value.query.statusRegister
        : '',
    licenseNumber: router.currentRoute.value.query.licenseNumber || '',
    userType: router.currentRoute.value.query.userType || '',
    startUpdateDate: router.currentRoute.value.query.startUpdateDate || '',
    endUpdateDate: router.currentRoute.value.query.endUpdateDate || '',
    startExpiryDate: router.currentRoute.value.query.startExpiryDate || '',
    endExpiryDate: router.currentRoute.value.query.endExpiryDate || '',
    note1: router.currentRoute.value.query.note1 || '',
    note2: router.currentRoute.value.query.note2 || '',
  };
  const savedPage = localStorage.getItem('adminUserListPage');

  if (savedPage) {
    const parsedPage = parseInt(savedPage, 10);

    if (parsedPage > totalPage.value || parsedPage < 1) {
      pagination.value.page = 1;
      pageIndex.value = 1;
    } else {
      pagination.value.page = parsedPage;
      pageIndex.value = parsedPage;
    }

    localStorage.removeItem('adminUserListPage');
  }

  if (+searchForm.value.status === STATUS_USER_ENUM.PENDING) {
    showPending.value = true;
  }
  await getListUser();
});
</script>
