const { sharedSchema } = require('../validations/sharedSchema');
const { MESSAGE } = require('../utils/message');
const {
  UNIT_TYPE_SETTING_ENUM,
  INVENTORY_TYPE_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
} = require('../helpers/enum');

const registerOutboundShipmentSchema = {
  summary: 'Register Outbound Shipment',
  description: 'Register Outbound Shipment',
  tags: ['Outbound Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: [
      'code',
      'shipper',
      'date',
      'gross_weight',
      'tare_weight',
      'ingredient',
      'setting',
      'inventory_type',
      'code_suffix_id',
    ],
    properties: {
      code: {
        type: 'string',
        minLength: 16,
        maxLength: 16,
        errorMessage: {
          minLength: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
          maxLength: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
        },
      },
      shipper: {
        type: 'number',
      },
      date: {
        type: 'string',
        format: 'slash-date',
        minLength: 0,
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      gross_weight: {
        type: 'number',
        exclusiveMinimum: 0,
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        },
      },
      tare_weight: {
        type: 'number',
        minimum: 0,
        maximum: {
          $data: '1/grossWeight',
        },
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
          maximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        },
      },
      ingredient: {
        type: 'array',
        items: {
          type: 'object',
          required: ['shipping_inventory_id', 'gross_weight', 'tare_weight'],
          properties: {
            shipping_inventory_id: {
              type: 'number',
            },
            gross_weight: {
              type: 'number',
            },
            tare_weight: {
              type: 'number',
            },
          },
          errorMessage: {
            required: {
              shipping_inventory_id: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
              gross_weight: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
              tare_weight: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
            },
          },
          additionalProperties: false,
        },
      },
      setting: {
        type: 'object',
        required: ['display_shipment_weight'],
        properties: {
          display_shipment_weight: {
            type: 'boolean',
          },
        },
        errorMessage: {
          required: {
            display_shipment_weight: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          },
        },
        additionalProperties: false,
      },
      type_diff: {
        type: 'number',
        enum: Object.values(TYPE_DIFFERENCE_WEIGHT_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      reason_diff: {
        type: 'string',
        minLength: 1,
        maxLength: 300,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      inventory_type: {
        type: 'number',
        enum: Object.values(INVENTORY_TYPE_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      code_suffix_id: {
        type: 'number',
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
  },
};

const checkPermissionForProxyOutboundShipmentSchema = {
  summary: 'Check Permission For Proxy Outbound Shipment',
  description: 'Check Permission For Proxy Outbound Shipment',
  tags: ['Outbound Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    type: 'object',
    additionalProperties: false,
    required: ['proxyUserId'],
    properties: {
      proxyUserId: {
        type: 'number',
      },
    },
    errorMessage: {
      required: {
        proxyUserId: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

const registerProxyOutboundShipmentSchema = {
  summary: 'Register Proxy Outbound Shipment',
  description: 'Register Proxy Outbound Shipment',
  tags: ['Outbound Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: [
      'code',
      'date',
      'volume_type',
      'gross_weight',
      'tare_weight',
      'quantity',
      'setting',
      'code_suffix_id',
      'session_token',
    ],
    properties: {
      code: {
        type: 'string',
        pattern: '^\\d{16}$',
        errorMessage: {
          pattern: MESSAGE.MSG_INVALID_OUTBOUND_CODE_ERROR,
        },
      },
      date: {
        type: 'string',
        format: 'slash-date',
        minLength: 0,
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      volume_type: {
        type: 'integer',
        enum: Object.values(UNIT_TYPE_SETTING_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      gross_weight: {
        type: 'number',
        exclusiveMinimum: 0,
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        },
      },
      tare_weight: {
        type: 'number',
        minimum: 0,
        maximum: {
          $data: '1/grossWeight',
        },
        checkFormatDecimal: true,
        errorMessage: {
          checkFormatDecimal: MESSAGE.MSG_INVALID_DECIMAL_ERROR,
          minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
          maximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        },
      },
      quantity: {
        type: 'integer',
        minimum: 1,
        errorMessage: {
          type: MESSAGE.MSG_INVALID_INTEGER_ERROR,
          minimum: MESSAGE.MSG_LIMITS_QUANTITY_MIN_ERROR,
        },
      },
      setting: {
        type: 'object',
        required: ['display_shipment_weight', 'price'],
        properties: {
          display_shipment_weight: {
            type: 'boolean',
          },
          price: {
            type: 'number',
          },
        },
        errorMessage: {
          required: {
            display_shipment_weight: MESSAGE.MSG_REQUIRED_DISPLAYSHIPMENTWEIGHT_ERROR,
            prise: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          },
        },
        additionalProperties: false,
      },
      session_token: {
        type: 'string',
      },
      code_suffix_id: {
        type: 'number',
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
  },
};

const getInventoryListOptionsSchema = {
  summary: 'Get inventory list',
  description: 'Get inventory list to build options for outbound shipment',
  tags: ['Outbound Feature'],
  security: [{ bearToken: [] }],
  query: {
    additionalProperties: false,
    type: 'object',
    required: [],
    properties: {
      sortBy: {
        type: 'string',
        errorMessage: {
          _: 'Invalid sortBy value.',
        },
      },
      page: {
        type: 'number',
        errorMessage: {
          _: 'Invalid page value. It should be a number',
        },
      },
      rowsPerPage: {
        type: 'number',
        errorMessage: {
          _: 'Invalid limit value. It should be a number',
        },
      },
      type: {
        type: 'integer',
        enum: Object.values(INVENTORY_TYPE_ENUM),
      },
    },
  },
  response: sharedSchema.response,
};

module.exports = {
  registerOutboundShipmentSchema,
  registerProxyOutboundShipmentSchema,
  checkPermissionForProxyOutboundShipmentSchema,
  getInventoryListOptionsSchema,
};
