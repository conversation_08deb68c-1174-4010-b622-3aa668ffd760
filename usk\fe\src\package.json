{"name": "シラスウナギ追跡システム", "version": "0.0.0", "description": "シラスウナギ追跡システム", "productName": "シラスウナギ追跡システム", "author": "<PERSON>", "private": true, "scripts": {"dev": "quasar dev", "lint": "eslint --ext .js,.vue ./", "test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@quasar/extras": "^1.16.4", "@vueuse/core": "^10.11.0", "@vueuse/integrations": "^10.11.0", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "ajv-keywords": "^5.1.0", "axios": "^1.10.0", "cleave.js": "^1.6.0", "dayjs": "^1.11.12", "decimal.js": "^10.4.3", "dompurify": "^3.1.7", "encoding-japanese": "^2.2.0", "eslint-plugin-import": "^2.32.0", "html2pdf.js": "^0.10.3", "imask": "^7.6.1", "jsqr": "^1.4.0", "linkify-html": "^4.1.3", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "papaparse": "^5.5.3", "pinia": "^3.0.3", "postcss": "^8.5.6", "qrcode": "^1.5.4", "quasar": "^2.16.0", "register-service-worker": "^1.7.2", "sprintf-js": "^1.1.3", "tailwindcss": "^4.1.10", "vue": "^3.5.17", "vue-i18n": "^11.1.6", "vue-router": "^4.5.1"}, "devDependencies": {"@quasar/app-vite": "^2.1.0", "@tailwindcss/postcss": "^4.1.10", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "postcss": "^8.5.5", "vite-plugin-checker": "^0.9.3", "vite-plugin-pwa": "^1.0.0", "workbox-build": "^7.3.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0"}, "overrides": {"glob": "^10.0.0", "rimraf": "^5.0.0", "inflight": "npm:@npmcli/promise-spawn@^6.0.0", "sourcemap-codec": "npm:@jridgewell/sourcemap-codec@^1.4.8"}, "engines": {"node": "^20 || ^22", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}