
const { sharedSchema } = require('../../validations');

const getSystemSettingsSchema = {
  summary: 'Get system settings',
  description: 'Get system settings',
  tags: ['Admin', 'System Settings'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const getSystemSettingsForNormalUserSchema = {
  summary: 'Get system settings for normal user',
  description: 'Get system settings for normal user',
  tags: ['Admin', 'System Settings'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const updateSystemSettingsSchema = {
  summary: 'Update system settings',
  description: 'Update system settings',
  tags: ['Admin', 'System Settings'],
  security: [{ bearToken: [] }],
  body: {
    type: 'object',
    additionalProperties: {
      type: 'string',
    }
  },
  response: sharedSchema.response,
};

module.exports = {
  getSystemSettingsSchema,
  updateSystemSettingsSchema,
  getSystemSettingsForNormalUserSchema,
};
