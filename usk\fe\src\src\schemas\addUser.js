import MESSAGE from 'helpers/message';

import userSchema from './model/user';

const addUserSchema = {
  type: 'object',
  required: [
    'enterpriseName',
    'enterpriseCode',
    'enterpriseNameKana',
    'password',
    'passwordConfirm',
  ],
  properties: {
    enterpriseName: {
      ...userSchema.enterpriseName,
      // pattern: '^[ａ-ｚＡ-Ｚ０-９、。，．・「」『』〜｛｝［］：；！？］＊＆＠＃％＋＝＞＜ぁ-んァ-ヴ一-龯]+$',
    },
    enterpriseCode: {
      ...userSchema.enterpriseCode,
    },
    enterpriseNameKana: {
      ...userSchema.enterpriseNameKana,
    },
    mobileNumber: {
      type: 'string',
      oneOf: [
        {
          pattern: '^\\d{11}$',
        },
        {
          minLength: 0,
          maxLength: 0,
        },
      ],
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR,
      },
    },
    password: {
      ...userSchema.password,
    },
    passwordConfirm: {
      ...userSchema.passwordConfirm,
    },
  },
  additionalProperties: false,
};

export default addUserSchema;
