<template>
  <q-page class="h-full tw:flex tw:flex-col tw:items-center">
    <div v-if="editState" class="tw:flex tw:flex-col tw:items-center">
      <form class="tw:max-w-[600px] tw:min-w-[550px] tw:pt-6 tw:pb-3">
        <div class="tw:mb-4 tw:mr-4">
          <span class="tw:text-[1rem] tw:block tw:mb-1">
            入出荷時の重量アラート閾値<span class="tw:text-red">*</span></span
          >
          <q-input
            type="text"
            maxlength="4"
            inputmode="decimal"
            autocomplete="nope"
            :model-value="
              form[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
            "
            @update:model-value="handleInputWeightAlertThreshold"
            v-cleave="{
              numeral: true,
              numeralPositiveOnly: true,
            }"
            bg-color="white"
            outlined
            input-class="tw:text-right tw:text-[1rem]"
            :error="
              !!errors[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
            "
            :error-message="
              errors[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
            "
            no-error-icon
          >
            <template v-slot:before>
              <span class="tw:text-[1rem] tw:text-[#333333]">±</span>
            </template>
            <template v-slot:after>
              <span class="tw:text-[1rem] tw:text-[#333333]">%</span>
            </template>
          </q-input>
        </div>

        <div class="tw:mb-4">
          <span class="tw:text-[1rem] tw:block tw:mb-1">
            入出荷登録修正期限<span class="tw:text-red">*</span></span
          >
          <q-input
            type="text"
            maxlength="3"
            inputmode="numeric"
            autocomplete="nope"
            :model-value="
              form[ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]
            "
            @update:model-value="handleInputEditDeadlineForArrival"
            v-cleave="{
              numeral: true,
              numeralPositiveOnly: true,
            }"
            bg-color="white"
            outlined
            input-class="tw:text-right tw:text-[1rem]"
            :error="
              !!errors[ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]
            "
            :error-message="
              errors[ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]
            "
            no-error-icon
          >
            <template v-slot:after>
              <span class="tw:text-[1rem] tw:text-[#333333]">日間</span>
            </template>
          </q-input>
        </div>

        <div class="tw:mb-4">
          <span class="tw:text-[1rem] tw:block tw:mb-1">
            採捕予備期間<span class="tw:text-red">*</span></span
          >
          <q-input
            type="text"
            maxlength="3"
            inputmode="numeric"
            autocomplete="nope"
            :model-value="
              form[ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]
            "
            @update:model-value="handleInputCatchingReservePeriod"
            v-cleave="{
              numeral: true,
              numeralPositiveOnly: true,
            }"
            bg-color="white"
            outlined
            input-class="tw:text-right tw:text-[1rem]"
            :error="
              !!errors[ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]
            "
            :error-message="
              errors[ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]
            "
            no-error-icon
          >
            <template v-slot:after>
              <span class="tw:text-[1rem] tw:text-[#333333]">日間</span>
            </template>
          </q-input>
        </div>
      </form>
      <BaseButton
        padding="1rem"
        @click.prevent="handleClickSubmit"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:w-40 tw:text-[1rem]`"
        label="修正する"
      />
    </div>
    <div v-else class="tw:flex tw:flex-col tw:items-center">
      <div class="tw:max-w-[600px] tw:min-w-[550px] tw:pt-6 tw:pb-3">
        <p class="tw:text-[1rem] tw:mb-2">以下の内容で登録します。</p>
        <div class="tw:mb-4">
          <span class="tw:text-[1rem] tw:block tw:mb-1">
            入出荷時の重量アラート閾値</span
          >
          <div
            :class="`tw:flex tw:justify-end tw:items-center
              tw:text-[1rem] tw:text-right tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`"
          >
            <span class="tw:text-[1rem] tw:block">±</span
            >{{ form[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD] }}%
          </div>
        </div>

        <div class="tw:mb-4">
          <span class="tw:text-[1rem] tw:block tw:mb-1">
            入出荷登録修正期限</span
          >
          <div
            :class="`tw:text-[1rem] tw:text-right tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`"
          >
            {{
              form[ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]
            }}日間
          </div>
        </div>

        <div class="tw:mb-4">
          <span class="tw:text-[1rem] tw:block tw:mb-1"> 採捕予備期間</span>
          <div
            :class="`tw:text-[1rem] tw:text-right tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`"
          >
            {{
              form[ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]
            }}日間
          </div>
        </div>
      </div>
      <div class="tw:flex tw:justify-center tw:gap-2 tw:w-full">
        <BaseButton
          outline
          padding="1rem"
          :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
          label="いいえ"
          @click.prevent="handleClickCancel"
        />
        <BaseButton
          padding="1rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
          label="はい"
          @click.prevent="handleClickSave"
        />
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { ADMIN_SYSTEM_SETTING_KEYS_ENUM } from 'src/helpers/constants';
import systemSettingsService from 'src/shared/services/admin/systemSettings.admin.service';
import { useAppStore } from 'src/stores/app-store';
import { storeToRefs } from 'pinia';
import useValidate from 'composables/validate';
import BaseButton from 'components/base/BaseButton.vue';
import adminSystemSettingsSchema from 'src/schemas/admin/system-setting/systemSetting.schema';
import {
  isNumeric,
  doParseFloatNumber,
  FORMAT_NUMBER,
} from 'src/helpers/common';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';

const { colorSub } = storeToRefs(useAppStore());
const { validateData, errors } = useValidate();
const systemSettingsInfo = ref(null);
const editState = ref(true);
const form = ref({
  [ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]: '',
  [ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]: '',
  [ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]: '',
});

const handleInputWeightAlertThreshold = value => {
  if (!isNumeric(value)) {
    return;
  }
  const weightAlertThreshold = Math.abs(doParseFloatNumber(value || 0));
  form.value.tareWeight = undefined;
  form.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD] = value
    ? FORMAT_NUMBER(weightAlertThreshold)
    : value;
};

const handleInputEditDeadlineForArrival = value => {
  if (!isNumeric(value)) {
    return;
  }
  const editDeadlineForArrival = Math.abs(doParseFloatNumber(value || 0));
  form.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL] = value
    ? FORMAT_NUMBER(editDeadlineForArrival)
    : value;
};

const handleInputCatchingReservePeriod = value => {
  if (!isNumeric(value)) {
    return;
  }
  const catchingReservePeriod = Math.abs(doParseFloatNumber(value || 0));
  form.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD] = value
    ? FORMAT_NUMBER(catchingReservePeriod)
    : value;
};

const handleClickSubmit = async () => {
  const payload = {
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]:
    doParseFloatNumber(form.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]),
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]:
      doParseFloatNumber(form.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]),
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]:
      doParseFloatNumber(form.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]),
  };

  const validate = validateData(adminSystemSettingsSchema, payload);
  if (validate) {
    editState.value = !editState.value;
  }
};

const handleClickSave = async () => {
  const newSystemSettings = await systemSettingsService.updateSystemSettings({ ...form.value });
  if (newSystemSettings.code === 0) {
    systemSettingsInfo.value = newSystemSettings.payload;
    editState.value = !editState.value;
    toast.access(MESSAGE.MSG_CHANGED_SETTING_INFO);
  }
};

const handleClickCancel = () => {
  editState.value = !editState.value;
};

onMounted(async () => {
  const systemSettings = await systemSettingsService.getSystemSettings();

  systemSettingsInfo.value = systemSettings.payload;
  form.value = {
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]:
      systemSettings.payload[
        ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD
      ],
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]:
      systemSettings.payload[
        ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL
      ],
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]:
      systemSettings.payload[
        ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD
      ],
  };
});
</script>
<style scoped></style>
