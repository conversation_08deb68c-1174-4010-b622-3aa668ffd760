const { MESSAGE } = require('../utils/message');
const {
  sharedSchema,
  accountSchema,

} = require('../validations');

const checkUserIdSchema = {
  summary: 'Check User ID',
  description: 'Check User ID to see if it is associated with License ID',
  tags: ['Account'],
  security: [{ 'bearToken': [] }],
  params: {
    type: 'object',
    properties: {
      userId: {
        ...accountSchema.userid,
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const linkUserIdSchema = {
  summary: 'Link User ID',
  description: 'Link User ID to see if it is associated with License ID',
  tags: ['Account'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['userid', 'licenseCode'],
    properties: {
      licenseCode: {
        ...accountSchema.licenseCode
      },
      userid: {
        ...accountSchema.userid
      },
    },
    errorMessage: {
      required: {
        userid: 'UserID is required',
        licenseCode: 'licenseCode is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const addUserSchema = {
  summary: 'Create new account',
  description: 'Create new account',
  tags: ['Account'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['enterpriseName', 'enterpriseCode', 'licenseCode',
      'enterpriseNameKana', 'password', 'passwordConfirm'],
    properties: {
      enterpriseName: {
        ...accountSchema.enterpriseName
      },
      enterpriseCode: {
        ...accountSchema.enterpriseCode
      },
      enterpriseNameKana: {
        ...accountSchema.enterpriseNameKana
      },
      mobileNumber: {
        type: 'string',
        oneOf: [
          {
            pattern: '^\\d{11}$',
          },
          {
            minLength: 0,
            maxLength: 0,
          },
        ],
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR
        }
      },
      password: {
        ...accountSchema.password
      },
      licenseCode: {
        ...accountSchema.licenseCode
      },
    },
    errorMessage: {
      required: {
        enterpriseName: 'Enterprise Name is required',
        enterpriseCode: 'Enterprise Code is required',
        enterpriseNameKana: 'Enterprise Name Kana is required',
        password: 'password is required',
        mobileNumber: 'mobile number is required',
        licenseCode: 'licenseCode is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

module.exports = {
  checkUserIdSchema,
  linkUserIdSchema,
  addUserSchema,
}