const { MESSAGE } = require("../utils/message");
const { sharedSchema } = require("../validations");

const getExportShipmentListSchema = {
  summary: 'Get export shipment list',
  description: 'Get export shipment list',
  tags: ['FA'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    type: 'object',
    additionalProperties: false,
    properties: {
      sortBy: {
        type: 'string',
        errorMessage: {
          _: 'Invalid sortBy value.',
        },
      },
      page: {
        type: 'number',
        errorMessage: {
          _: 'Invalid page value. It should be a number',
        },
      },
      rowsPerPage: {
        type: 'number',
        errorMessage: {
          _: 'Invalid limit value. It should be a number',
        },
      },
      code: {
        type: 'string',
        maxLength: 16,
        pattern: '^[0-9]{1,16}$',
      },
      enterpriseName: {
        type: 'string',
        maxLength: 256,
      },
      startDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      endDate: {
        type: 'string',
        format: 'slash-date',
        formatMinimum: {
          $data: '1/startDate',
        },
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          formatMinimum: 'End date must be greater than or equal to start date',
        },
      },
    }
  }
};

module.exports = {
  getExportShipmentListSchema,
};
