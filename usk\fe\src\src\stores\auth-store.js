import { defineStore } from 'pinia';
import { useLocalStorage } from 'composables/localstorage';

export const useAuthStore = defineStore('authStore', {
  state: () => ({
    user: null,
    userLink: null,
    proxyUser: null,
  }),
  getters: {
    isLoggedIn: state => state.user?.id,
  },
  actions: {
    signIn(userData) {
      const { userToken } = useLocalStorage();
      const { token, ...rest } = userData;
      this.user = rest;
      userToken.value = token;
    },
    signInLink(userData) {
      this.userLink = userData;
    },
    signInProxyUser(userData) {
      this.proxyUser = userData;
    },
    signOut() {
      this.user = null;
      const { userToken } = useLocalStorage();
      userToken.value = null;
    },
    adminProxyUserSignIn(userData) {
      const { cacheAdminToken, userToken } = useLocalStorage();
      const { token, ...rest } = userData;
      this.user = rest;
      cacheAdminToken.value = userToken.value;
      userToken.value = token;
    },
    adminProxyUserSignOut() {
      this.user = null;
      const { cacheAdminToken, userToken } = useLocalStorage();
      userToken.value = cacheAdminToken.value;
      cacheAdminToken.value = null;
    },
  },
});
