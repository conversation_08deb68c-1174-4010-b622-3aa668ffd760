const { ssmClient } = require('./ssmclient');
const { GetParametersCommand, GetParameterCommand, GetParametersByPathCommand } = require('@aws-sdk/client-ssm');

const scanParametersStore = async (param = {}) => {
  const result = await ssmClient.send(new GetParametersCommand(param));
  const { Parameters: dataScans } = result;

  return dataScans;
}

const scanParameterStore = async (param = {}) => {
  const result = await ssmClient.send(new GetParameterCommand(param));
  const { Parameter: dataScan } = result;

  return dataScan;
}

const scanParameterStoreByPath = async (param = {}) => {
  const result = await ssmClient.send(new GetParametersByPathCommand(param));
  const { Parameters: dataScan, NextToken: nextToken } = result;
  if (nextToken) {
    param.NextToken = nextToken;
    const nextResult = await scanParameterStoreByPath(param);
    dataScan.push(...nextResult);
  }
  return dataScan;
}

module.exports = { scanParametersStore, scanParameterStore, scanParameterStoreByPath }