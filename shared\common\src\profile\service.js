const { ControlledException } = require("../base/errors");
const BaseService = require("../base/serviceFn");
const { MESSAGE } = require('../utils/message');

class ProfileService extends BaseService {
  async getProfile(params) {
    const { userId } = params;
    const connectRead = this.DB.READ;
    const users = await connectRead.users.findFirst({
      include: {
        enterprise: true,
        license: true,
      },
      where: {
        id: userId,
      },
    });
    delete users?.password;
    return this.SUCCESS({ data: users });
  }

  async updatePhone(params) {
    const { userId, phone } = params;
    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    if (phone) {
      const checkExistPhone = await connectRead.users.findFirst({
        where: {
          NOT: {
            id: userId
          },
          AND: {
            phone
          }
        }
      });
      if (checkExistPhone) {
        throw new ControlledException(MESSAGE.MSG_USED_FHONENUMBER_ERROR);
      }
    }

    await connectWrite.users.update({
      where: {
        id: userId
      },
      data: {
        phone,
        latest_updated_by_id: userId,
      }
    });
    return this.SUCCESS({ message: MESSAGE.MSG_CHANGED_USERSETTING_INFO });
  }
}

module.exports = ProfileService;