<template>
  <div>
    <!-- title -->
    <div class="tw:text-2xl tw:mt-5 tw:tl:text-4xl tw:font-bold tw:text-center">
      パスワード再登録
    </div>
    <!-- content -->
    <div class="tw:text-sm tw:my-5 tw:tl:text-[1.35rem] tw:tl:leading-[1.92rem]">
      <div>
        パスワード再登録用のURLを記載たてSMSを送信します。登録している携帯電話番号を入力してください。SMS受信後、記載のURLからパスワードの再登録をお願いいたします。
      </div>
      <div class="tw:mt-3">
        携帯電話番号が不明な方はシステム管理者にお問い合わせください。
      </div>
    </div>
    <div>
      <div class="tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]">
        携帯電話番号<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-3"
        outlined
        type="tel" inputmode="numeric"
        v-model="phone"
        autocomplete="nope"
        lazy-rules
        :error="!!errors.mobileNumber"
        :error-message="errors.mobileNumber" no-error-icon
        maxlength="11"
      />
      <q-btn
        class="tw:w-full tw:mt-5 tw:text-base tw:font-bold tw:text-white
        tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
        label="送信する"
        type="button"
        :class="`tw:bg-${colorSub}`"
        @click.prevent="sentSMS"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import inputMobile from 'schemas/inputMobile';
import authService from 'services/auth.service';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();
const { errors, validateData } = useValidate();

// ===== REF =====
const phone = ref('');

// ===== METHOD =====
const sentSMS = async () => {
  // Sent SMS
  const valid = validateData(inputMobile, { mobileNumber: phone.value });
  if (!valid) {
    return;
  }
  const result = await authService.sendSmsPassword({ mobileNumber: phone.value });
  if (result.code === 0) {
    // Go to the SMS sending completion screen
    await router.push({ name: 'completeSMS' });
  }
};
</script>
