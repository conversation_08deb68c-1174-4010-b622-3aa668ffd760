const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const duration = require('dayjs/plugin/duration');
const customParseFormat = require('dayjs/plugin/customParseFormat');

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);
dayjs.extend(customParseFormat);

dayjs.getDate = (value = dayjs(), timeZone = 'Asia/Tokyo') => (dayjs(value).tz(timeZone));
dayjs.getDateFromJST = (value = dayjs(), timeZone = 'Asia/Tokyo') => (dayjs.tz(value, timeZone));

module.exports = dayjs;
