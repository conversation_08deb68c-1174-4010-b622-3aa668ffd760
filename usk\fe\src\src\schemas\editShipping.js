import MESSAGE from 'helpers/message';

const editShipping = {
  additionalProperties: false,
  type: 'object',
  required: [ 'grossWeight', 'date', 'quantity', 'tareWeight', 'netWeight'],
  properties: {
    destinationId: {
      type: 'integer',
      minimum: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    grossWeight: {
      type: 'string',
      minLength: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeight: {
      type: 'string',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    quantity: {
      type: 'string',
    },
    netWeight: {
      type: 'string',
    },
    date: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      name: <PERSON><PERSON><PERSON><PERSON>.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default editShipping;
