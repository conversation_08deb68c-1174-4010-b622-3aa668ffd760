const {
  searchLocatedTransactionSchema,
  getPartnerHybridListSchema,
  registerOneLocatedTransactionHandMadeSchema,
  toggleShowSchema,
  getLocatedTransactionDetailSchema,
  updateLocatedTransactionSchema,
  getPartnerHybridDetailSchema,
  registerPartnerHybridSchema,
} = require('./schema');
const { auth, apiKeyVerify, authEnterprise } = require('../base/authorized');

// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
async function searchLocatedTransactionHandler(request, reply) {
  const { query, user } = request;
  query.userId = user.id;
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.searchAll(query);
  return reply.send(response);
}

async function getPartnerHybridListHandler(request, reply) {
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.getPartnerHybridListHandler(
    request.user,
    request.query
  );
  return reply.send(response);
}

async function getPartnerHybridDetailHandler(request, reply) {
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.getPartnerHybridDetail(
    request.user,
    request.params,
  );
  return reply.send(response);
}

async function registerPartnerHybridHandler(request, reply) {;
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.registerPartnerHybrid(request.user, request.body);
  return reply.send(response);
}

async function toggleShowHandler(request, reply) {
  const { body, params, user } = request;
  body.enterpriseId = user.enterprise_id;
  body.userId = user.user_id;
  body.id = params.id;
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.toggleShow(body);
  return reply.send(response);
}

async function registerLocatedTransactionHandMadeHandler(request, reply) {
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.registerHandMade(request.user, request.body);
  return reply.send(response);
}

// Get located transaction detail
async function getLocatedTransactionDetailHandler(request, reply) {
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.getLocatedTransactionDetail(
    request.user,
    request.params.id
  );
  return reply.send(response);
}

// Update located transaction
async function updateLocatedTransactionHandler(request, reply) {
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.updateLocatedTransaction(
    request.user,
    request.params.id,
    request.body
  );
  return reply.send(response);
}

// delete located transaction
async function deleteLocatedTransactionHandler(request, reply) {
  const locatedTransactionService = request.diScope.resolve(
    'locatedTransactionService'
  );
  const response = await locatedTransactionService.deleteLocatedTransaction(
    request.user,
    request.params.id
  );
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/search-all',
    {
      schema: searchLocatedTransactionSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    searchLocatedTransactionHandler
  );
  fastify.get(
    '/hybrid-list',
    {
      schema: getPartnerHybridListSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    getPartnerHybridListHandler
  );
  fastify.get(
    '/hybrid-detail/:id',
    {
      schema: getPartnerHybridDetailSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    getPartnerHybridDetailHandler
  );
  fastify.post(
    '/register-partner-hybrid',
    {
      schema: registerPartnerHybridSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    registerPartnerHybridHandler
  );
  fastify.post(
    '/register-handmade',
    {
      schema: registerOneLocatedTransactionHandMadeSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    registerLocatedTransactionHandMadeHandler
  );
  fastify.post(
    '/toggle-show/:id',
    {
      schema: toggleShowSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    toggleShowHandler
  );
  fastify.get(
    '/located-transaction-detail/:id',
    {
      schema: getLocatedTransactionDetailSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    getLocatedTransactionDetailHandler
  );
  fastify.put(
    '/located-transaction-update/:id',
    {
      schema: updateLocatedTransactionSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    updateLocatedTransactionHandler
  );
  fastify.delete(
    '/located-transaction-delete/:id',
    {
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    deleteLocatedTransactionHandler
  );
};
