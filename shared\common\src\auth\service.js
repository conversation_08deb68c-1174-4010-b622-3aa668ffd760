const BaseService = require("../base/serviceFn");
const {
  ControlledException,
  UnauthorizedAccessException,
} = require("../base/errors");
const {
  ROLES_ENUM,
  USER_STATUS_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  USER_STATUS_REGISTER_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
} = require("../helpers/enum");
const { MESSAGE } = require("../utils/message");
const { compare, hash } = require("../utils/crypto");
const ejs = require("ejs");
const { sendSMS } = require("../utils/sms");
const Env = require("../boot/env");
const { convertToString } = require("../utils/convertToString");
const ShortUniqueId = require("short-unique-id");
const { Prisma } = require("@prisma/client");
const dayjs = require("../boot/dayjs");

class AuthService extends BaseService {
  // ====== 1. Private method ==========

  _makeTokenJWT(user) {
    const token = this.FASTIFY.jwt.sign({ ...user, datetime: Date.now() });
    return token;
  }

  async _getTemplateSendOTP() {
    const paramGetSettings = {
      Bucket: Env.S3_BUCKET_NAME,
      Key: "templates/forgot-password_plaintext.ejs",
    };
    const templateSMS = await convertToString(paramGetSettings);
    return templateSMS;
  }

  async _sendSMS(mobileNumber, template, data) {
    const contentSMS = ejs.render(template, data);
    await sendSMS(mobileNumber, contentSMS);
  }

  async _getUserEnterprise(connect, idEnterprise) {
    const user = await connect.users.findFirst({
      where: {
        enterprise_id: idEnterprise,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
      },
    });
    return user;
  }

  // ====== 2. Public method ==========

  /** Service login
   * args:
   * body: login information
   *
   */
  async login(loginInfo) {
    const userID = loginInfo.userid;
    const connect = this.DB.READ;
    const write = this.DB.WRITE;

    const account = await connect.users.findFirst({
      where: {
        user_code: userID,
        delete_flag: false,
        role: { not: ROLES_ENUM.ADMIN },
      },
      select: {
        id: true,
        user_code: true,
        password: true,
        enterprise_id: true,
        phone: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        status: true,
        loginfail: true,
        locktime: true,
        account_registration_status: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });

    if (!account || account.status === USER_STATUS_ENUM.PENDING) {
      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    if (account.locktime && Date.now() < new Date(account.locktime).getTime()) {
      await write.users.update({
        where: { user_code: userID },
        data: {
          loginfail: 0,
        },
      });
      throw new ControlledException(MESSAGE.MSG_LOCKED_USERID_ERROR);
    }

    // Check password
    let comparePassword = false;

    if (
      account.account_registration_status === USER_STATUS_REGISTER_ENUM.OFFICIAL
    ) {
      comparePassword = await compare(loginInfo.password, account.password);
    } else {
      comparePassword = loginInfo.password === account.password;
    }
    if (!comparePassword) {
      const failedCount = (account.loginfail || 0) + 1;

      if (failedCount >= 5) {
        await write.users.update({
          where: { user_code: userID },
          data: {
            loginfail: 0,
            locktime: new Date(Date.now() + 60 * 1000),
          },
        });
        throw new ControlledException(MESSAGE.MSG_LOCKED_USERID_ERROR);
      }

      await write.users.update({
        where: { user_code: userID },
        data: {
          loginfail: failedCount,
        },
      });

      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    // Check role
    if (
      account.role === ROLES_ENUM.NORMAL_USER &&
      account.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      throw new ControlledException(
        MESSAGE.MSG_LOGIN_EEL_FARMING_BUSINESSESL);
    }

    if (!account.enterprise_id && account.role === ROLES_ENUM.NORMAL_USER) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }

    const userEnterprise = await this._getUserEnterprise(
      connect,
      account.enterprise_id,
    );
    if (
      account.status === USER_STATUS_ENUM.PENDING ||
      userEnterprise?.status === USER_STATUS_ENUM.PENDING
    ) {
      throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
    }

    await write.users.update({
      where: { user_code: userID },
      data: {
        loginfail: 0,
        locktime: null,
      },
    });

    delete account.password;

    const token = this._makeTokenJWT(account);

    return this.SUCCESS({
      ...account,
      token,
    });
  }

  /**
   * Service login admin site
   * @param {*} loginInfo login information
   * @returns
   */
  async adminLogin(loginInfo) {
    const userID = loginInfo.userid;

    const account = await this.DB.READ.users.findFirst({
      where: {
        AND: {
          user_code: userID,
          delete_flag: false,
        },
        OR: [{ role: ROLES_ENUM.ADMIN }, { role: ROLES_ENUM.SYSTEM_ADMIN }],
      },
      select: {
        id: true,
        user_code: true,
        password: true,
        status: true,
        loginfail: true,
        locktime: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        // other fields...
      },
    });

    if (!account) {
      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    if (account.locktime && Date.now() < new Date(account.locktime).getTime()) {
      await this.DB.WRITE.users.update({
        where: { user_code: userID },
        data: {
          loginfail: 0,
        },
      });
      throw new ControlledException(MESSAGE.MSG_LOCKED_USERID_ERROR);
    }

    if (
      account.status === USER_STATUS_ENUM.PENDING ||
      account.status === USER_STATUS_ENUM.NONACTIVE
    ) {
      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    const isPasswordCorrect = await compare(
      loginInfo.password,
      account.password,
    );

    if (!isPasswordCorrect) {
      const failedCount = (account.loginfail || 0) + 1;

      if (failedCount >= 5) {
        await this.DB.WRITE.users.update({
          where: { user_code: userID },
          data: {
            loginfail: 0,
            locktime: new Date(Date.now() + 60 * 1000),
          },
        });
        throw new ControlledException(MESSAGE.MSG_LOCKED_USERID_ERROR);
      }

      await this.DB.WRITE.users.update({
        where: { user_code: userID },
        data: {
          loginfail: failedCount,
        },
      });

      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    await this.DB.WRITE.users.update({
      where: { user_code: userID },
      data: {
        loginfail: 0,
        locktime: null,
      },
    });

    delete account.password;
    const token = this._makeTokenJWT(account);

    return this.SUCCESS({
      ...account,
      token,
    });
  }

  /**
   * Service admin login proxy user - only for admin site
   * @param {*} loginInfo login information
   * @returns
   */
  async adminLoginProxyUser(id) {
    const account = await this.DB.READ.users.findFirst({
      where: {
        id: Number(id),
        delete_flag: false,
        status: {
          in: [USER_STATUS_ENUM.ACTIVE, USER_STATUS_ENUM.NONACTIVE],
        },

        enterprise_type: {
          in: [
            ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
            ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
            ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
          ],
        },
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        status: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });

    if (!account) {
      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    if (
      account.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      throw new ControlledException(MESSAGE.MSG_INVALID_LOGIN_ERROR);
    }

    const token = this._makeTokenJWT(account);
    const response = {
      ...account,
      token: token,
    };

    return this.SUCCESS(response);
  }

  /** Service login link
   * args:
   * body: Link login information
   */
  async loginLink(loginInfo) {
    const qrCode = loginInfo.qrCode;
    const connect = this.DB.READ;
    // Check licenses existence
    const checkLicenses = await connect.licenses.findFirst({
      where: {
        license_code: qrCode,
        delete_flag: false,
      },
    });

    // Not found license
    if (!checkLicenses) {
      throw new ControlledException(MESSAGE.MSG_INVALID_QRCODE_ERROR, {}, 401);
    }

    // License id not yet linked
    if (!checkLicenses.user_id) {
      throw new ControlledException(
        MESSAGE.MSG_NA_USERID_ERROR,
        checkLicenses,
        402,
      );
    }

    // Check qrcode with corresponding account
    const account = await connect.users.findFirst({
      where: {
        license_id: checkLicenses.id,
        delete_flag: false,
        // status: USER_STATUS_ENUM.ACTIVE,
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        status: true,
        note_1: true,
        note_2: true,
        province_custom_data: true,
        province_id: true,
        license_number: true,
        account_registration_status: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        enterprise: {
          select: {
            type: true,
            delete_flag: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
        provinces: {
          select: {
            setting: true,
          },
        },
      },
    });

    // In case the user information does not match the QRcode information
    if (
      !account ||
      !account.enterprise_id ||
      account.enterprise?.delete_flag
    ) {
      // not found
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR, {}, 401);
    }

    if (
      account.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      throw new ControlledException(MESSAGE.MSG_INVALID_LOGIN_ERROR, {}, 401);
    }

    const userEnterprise = await this._getUserEnterprise(
      connect,
      account.enterprise_id,
    );

    if (
      account.status === USER_STATUS_ENUM.PENDING ||
      userEnterprise?.status === USER_STATUS_ENUM.PENDING
    ) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR, {}, 401);
    }

    if (
      account.role === ROLES_ENUM.NORMAL_USER &&
      account === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
    ) {
      return this.SUCCESS(account);
    }

    if (account.province_custom_data) {
      Object.keys(account.province_custom_data).forEach((key) => {
        if (typeof account.province_custom_data[key] === "string") {
          account.province_custom_data[key] = account.province_custom_data[
            key
          ].replaceAll(";", "\n");
        }
      });
    }
    return this.SUCCESS(account);
  }

  /** Service check login
   * args:
   * user: User information after login
   *
   */
  async checkLogin(user) {
    // Create token
    const token = this._makeTokenJWT(user);

    const response = {
      ...user,
      token: token,
    };
    return this.SUCCESS(response);
  }

  /** Service send sms password
   * args:
   *
   */
  async smsPassword(user) {
    const mobileNumber = user.mobileNumber;
    // Check phone exist
    const checkPhone = await this.DB.READ.users.findFirst({
      where: {
        phone: mobileNumber,
      },
    });
    if (!checkPhone) {
      throw new ControlledException(MESSAGE.MSG_PHONE_NUMBER_DOES_NOT_EXIST);
    }
    delete checkPhone.password;
    const template = await this._getTemplateSendOTP();
    const content = new ShortUniqueId({
      length: 32,
      dictionary: "alphanum",
    }).randomUUID();
    await this.DB.WRITE.verifications.create({
      data: {
        user_id: checkPhone.id,
        content,
        type: 1,
        created_by_id: checkPhone.id,
      },
    });
    await this._sendSMS(mobileNumber, template, {
      url: `${Env.URL_SITE}/reset-passwd/${content}`,
    });
    // todo send SMS
    return this.SUCCESS(checkPhone);
  }

  /** Service reset password
   * args:
   *
   */
  async resetPassword(body, token) {
    const newPassword = body.password;
    // Check id user
    const checkVerify = await this.DB.READ.verifications.findFirst({
      where: {
        content: token,
      },
    });
    // not found
    if (!checkVerify) {
      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }
    // generate password
    const password = await hash(newPassword);
    await this.DB.WRITE.$transaction(
      async (tx) => {
        await tx.users.update({
          where: {
            id: checkVerify.user_id,
          },
          data: {
            password,
          },
        });
        await tx.verifications.delete({
          where: {
            id: checkVerify.id,
          },
        });
      },
      { isolationLevel: Prisma.TransactionIsolationLevel.Serializable },
    );
    return this.SUCCESS({});
  }

  /** Service login instead user id
   * args:
   * body: login instead user id information
   *
   */
  async loginInsteadUserId(user, body) {
    const { userid } = body;
    const connect = this.DB.READ;
    // Get catching reserve period
    const catchingReservePeriod = await connect.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD,
      },
      select: {
        setting_value: true,
      },
    });
    // Get account by user id (from body)
    const account = await connect.users.findFirst({
      where: {
        user_code: userid,
        delete_flag: false,
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        license_number: true,
        status: true,
        account_registration_status: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });

    // Check account existence
    // If not found, throw error with key MSG_NA_USERID_ERROR
    if (!account) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }

    // Get user enterprise
    const userEnterprise = await this._getUserEnterprise(
      connect,
      account.enterprise_id,
    );
    // Check user status
    // If user status is pending or user enterprise status is pending, throw error with key MSG_NA_USERID_ERROR
    if (
      account.status === USER_STATUS_ENUM.PENDING ||
      userEnterprise?.status === USER_STATUS_ENUM.PENDING
    ) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }

    // Check user status is non-active
    // If user status is non-active, throw error with key MSG_LIMITS_PREMISSION_ERROR
    if (account.status === USER_STATUS_ENUM.NONACTIVE) {
      throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
    }

    // Check user has enterprise type is catch enterprise
    // If user has enterprise type is catch enterprise, check expiry date
    // If expiry date + reserve period is before current date, throw error with key MSG_LIMITS_PREMISSION_ERROR
    const expiryDate = account.license?.expiry_date;
    if (account.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
      const reservePeriod = catchingReservePeriod?.setting_value || 14;
      if (dayjs(expiryDate).add(reservePeriod, "day").isBefore(dayjs())) {
        throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
      }
    }

    // Check user and proxy user has enterprise type is catch enterprise
    // If user and proxy user doesn't have same apply code then throw error
    if (
      account.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      user.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      account.enterprise_id !== user.enterprise_id
    ) {
      throw new ControlledException(
        MESSAGE.MSG_DIFFERENT_NOTIFICATIONUMBER_ERROR,
      );
    }

    // Check enterprise type of proxy user
    if (
      account.enterprise_type !== ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
    ) {
      throw new ControlledException(
        MESSAGE.MSG_INVALID_QRCODE_ERROR,
      )
    }
    
    const token = this._makeTokenJWT({ id: account.id });

    const response = {
      ...account,
      sessionToken: token,
    };

    return this.SUCCESS(response);
  }

  /**
   * For login instead link (QR code)
   * @param {*} query { qrCode: string }
   * @param {*} user { user information }
   * @returns
   */
  async loginInsteadLink(user, query) {
    const { qrCode } = query;
    const connect = this.DB.READ;
    // Get catching reserve period
    const catchingReservePeriod = await connect.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD,
      },
      select: {
        setting_value: true,
      },
    });
    // Get account by license code (from query)
    const account = await connect.users.findFirst({
      where: {
        license: {
          license_code: qrCode,
          delete_flag: false,
        },
        delete_flag: false,
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        license_number: true,
        status: true,
        account_registration_status: true,
        license: {
          select: {
            id: true,
            license_code: true,
            expiry_date: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });

    // Check account existence
    // If not found, throw error with key MSG_NA_USERID_ERROR
    if (!account) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }

    // Get user enterprise
    const userEnterprise = await this._getUserEnterprise(
      connect,
      account.enterprise_id,
    );
    // Check user status
    // If user status is pending or user enterprise status is pending, throw error with key MSG_NA_USERID_ERROR
    if (
      account.status === USER_STATUS_ENUM.PENDING ||
      userEnterprise?.status === USER_STATUS_ENUM.PENDING
    ) {
      throw new ControlledException(MESSAGE.MSG_NA_USERID_ERROR);
    }

    // Check user status is non-active
    // If user status is non-active, throw error with key MSG_LIMITS_PREMISSION_ERROR
    if (account.status === USER_STATUS_ENUM.NONACTIVE) {
      throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
    }

    // Check user has enterprise type is catch enterprise
    // If user has enterprise type is catch enterprise, check expiry date
    // If expiry date + reserve period is before current date, throw error with key MSG_LIMITS_PREMISSION_ERROR
    const expiryDate = account.license?.expiry_date;
    if (account.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
      const reservePeriod = catchingReservePeriod?.setting_value || 14;
      if (dayjs(expiryDate).add(reservePeriod, "day").isBefore(dayjs())) {
        throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
      }
    }

    // Check user and proxy user has enterprise type is catch enterprise
    // If user and proxy user doesn't have same apply code then throw error
    if (
      account.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      user.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      account.enterprise_id !== user.enterprise_id
    ) {
      throw new ControlledException(
        MESSAGE.MSG_DIFFERENT_NOTIFICATIONUMBER_ERROR,
      );
    }

    // Check enterprise type of proxy user
    if (
      account.enterprise_type !== ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
    ) {
      throw new ControlledException(
        MESSAGE.MSG_INVALID_QRCODE_ERROR,
      )
    }

    const token = this._makeTokenJWT({ id: account.id });

    const response = {
      ...account,
      sessionToken: token,
    };

    return this.SUCCESS(response);
  }

  /**
   * Recovery password when user has pending status
   * @param {*} user user information
   * @param {*} body {password} : new password
   * @returns
   */
  async recoveryPassword(user, body) {
    const newPassword = body.password;

    const userInfo = await this.DB.READ.users.findUnique({
      where: {
        id: user.id,
        delete_flag: false,
      },
      select: {
        password: true,
      },
    });

    if (!userInfo) {
      throw new UnauthorizedAccessException();
    }

    // Check password
    // if new password is same as old password, throw error
    if (newPassword === userInfo.password) {
      throw new ControlledException(MESSAGE.MSG_REUSE_INITPASSWARD_ERROR);
    }

    // generate password
    const password = await hash(newPassword);
    await this.DB.WRITE.$transaction(async (tx) => {
      await tx.users.update({
        where: {
          id: user.id,
        },
        data: {
          password,
          status: USER_STATUS_ENUM.ACTIVE,
          account_registration_status: 1,
          latest_updated_by_id: user.id,
          latest_updated_on: dayjs.getDate(),
        },
      });
    });

    return this.SUCCESS({});
  }
}

module.exports = AuthService;
