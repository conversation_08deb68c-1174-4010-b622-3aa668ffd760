<template>
  <div class="tw:my-4 tw:tl:pb-0 tw:pb-[6.75rem]">
    <q-card class="tw:mt-4 tw:tl:mb-4 tw:mb-20 tw:bg-white tw:p-4 tw:min-h-[calc(100vh-15rem)] tw:tl:min-h-[calc(140vh-15rem)]">
      <div class="tw:text-l-design tw:font-[700]">従事者詳細</div>

      <div class="tw:border tw:border-[#E0E0E0] tw:rounded-none">
        <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#E0E0E0]">
          <!-- userID -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1 tw:font-[400]
              tw:text-m-design tw:items-center tw:flex">
              ユーザーID
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design">
              {{ data?.user_code }}
            </div>
          </div>
          <!-- name -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1
              tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              氏名
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1
            tw:font-[400] tw:text-m-design">
              {{ data?.name }}
            </div>
          </div>
          <!-- name_kana -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1
              tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              氏名（カナ）
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400]
            tw:text-m-design">
              {{ data?.name_kana }}
            </div>
          </div>
          <!-- license code -->
          <div v-if="CHECK_ROLE([ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],[STAFF_TYPE_ENUM.STAFF], data)" class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1
              tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              許可番号
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design">
              {{
                data?.license_number
              }}
            </div>
          </div>
          <!-- phone -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              連絡先等
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:break-words">
              {{ data?.phone }}
            </div>
          </div>

          <!-- password -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              パスワード
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design">
              ********
            </div>
          </div>

          <!-- note1 -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              備考１
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:break-all">
              {{ data?.note_1 }}
            </div>
          </div>

          <!-- note2 -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              備考２
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:break-all">
              {{ data?.note_2 }}
            </div>
          </div>

          <!-- status -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <span
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex">
              状態
            </span>
            <div class="tw:tl:w-[70%] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:break-all">
              {{ changeStatus(data?.status) }}
            </div>
          </div>
        </div>
      </div>
    </q-card>
    <q-footer elevated class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row">
      <BaseButton outline class="
      tw:rounded-[40px]" :class="`tw:bg-white
      tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[20rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`" label="従事者管理に戻る" @click.prevent="handleClickBack" />
    </q-footer>
  </div>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import staffService from 'services/staff.service';
import { ROLES_ENUM,  ENTERPRISE_TYPE_ENUM, STAFF_TYPE_ENUM, OPTIONS_STATUS_USER } from 'helpers/constants';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { CHECK_ROLE } from 'src/helpers/common';

const router = useRouter();
const data = ref({});
// method

const handleClickBack = async () => {
  await router.push({
    name: 'employeeList',
  });
};

const changeStatus = statusInfo => {
  const labelStatus = OPTIONS_STATUS_USER.find(item => item.value === statusInfo);
  return labelStatus?.label;
};

onMounted(async () => {
  const result = await staffService.getOneStaff(
    router.currentRoute.value.params.id
  );
  if (result.code === 401) {
    return;
  }
  if (result.code === 0) {
    data.value = result.payload.data;
  } else {
    router.back();
  }
});
</script>
