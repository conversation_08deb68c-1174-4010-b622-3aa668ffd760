import BaseService from 'services/base.service';

class AdminSystemSettings extends BaseService {
  async getSystemSettings(query) {
    try {
      return this.dao.getSystemSettings(query);
    } catch (error) {
      return null;
    }
  }

  async updateSystemSettings(data) {
    try {
      return this.dao.updateSystemSettings(data);
    } catch (error) {
      return null;
    }
  }

  async getSystemSettingsForNormalUser() {
    try {
      return this.dao.getSystemSettingsForNormalUser();
    } catch (error) {
      return null;
    }
  }
}

export default new AdminSystemSettings('admin-system-settings');
