<template>
  <div class="tw:mt-2 tw:tl:px-3 tw:h-full" id="header">
      <q-card class="tw:p-4 tw:mt-4 tw:mb-4 tw:bg-white">
        <span class="tw:text-l-design tw:font-bold">プライバシーポリシー</span>
    <iframe :src="srcPolicy"
      frameborder='0'
      class="tw:w-full tw:h-full"
      scrolling="no">
    </iframe>
      </q-card>
  </div>
  <!-- end content -->

</template>
<script setup>
// open page denso
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
// ===== REF =====
const srcPolicy = ref('');

onMounted(() => {
  srcPolicy.value = `https://view.officeapps.live.com/op/embed.aspx?src=${process.env.CLOUDFRONT}/document/fasite/policy.docx`;
});
</script>
