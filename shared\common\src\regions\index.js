// ======== IMPORT ========================
const { getRegionListSchema } = require('./schema');
const { apiKeyVerify, auth } = require('../base/authorized');

// ===== 1. Router Functions ==============
async function getRegionList(request, reply) {
  const regionService = request.diScope.resolve('regionService');
  const response = await regionService.getRegionWithProvinces();
  return reply.send(response);
}

// ===== 2. Router Registration ===========
module.exports = async (fastify) => {
  fastify.get(
    '/get',
    {
      schema: getRegionListSchema,
      onRequest: [apiKeyVerify, auth],
    },
    getRegionList
  );
};
