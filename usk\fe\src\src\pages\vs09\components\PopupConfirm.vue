<template>
  <q-dialog v-model="model" persistent>
    <q-card class="tw:min-w-[35rem] tw:lg:min-w-[70rem]">
      <div class="tw:font-bold tw:text-m-design tw:bg-[#004AB9] tw:text-white tw:p-5">
          {{ label }}
      </div>
      <p class="tw:text-m-design tw:p-5 tw:mb-0">
        {{ description }}
      </p>
      <slot />
      <div class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-end tw:gap-5 tw:px-5 tw:pb-5">
        <q-btn
          outline
          class="tw:bg-white tw:rounded-[40px] tw:text-[#004AB9]
            tw:text-xs-design tw:font-bold tw:h-[4.75rem]!"
          :label="labelCancel"
          @click="handleClickCancel"
        />
        <q-btn
          class="tw:bg-[#004AB9] tw:rounded-[40px] tw:text-white tw:text-xs-design
            tw:font-bold tw:tl:min-w-[17rem] tw:h-[4.75rem]!"
          :label="labelSubmit"
          @click="handleClickSubmit"
        />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
defineProps({
  label: {
    type: String,
  },
  description: {
    type: String,
  },
  labelSubmit: {
    type: String,
  },
  labelCancel: {
    type: String,
  },
});
const model = defineModel();
const emit = defineEmits(['onClickCancel', 'onClickSubmit ']);

const handleClickCancel = () => {
  emit('onClickCancel');
};

const handleClickSubmit = () => {
  emit('onClickSubmit');
};

</script>
