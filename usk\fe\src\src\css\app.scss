// app global css in SCSS form
.q-pagination__middle button {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.q-page-container {
  padding-bottom: 0 !important;
}

main {
  min-height: 1px !important;
}

.content-tab .q-tab__label {
  font-size: 1.1rem;
}

@media (min-width: 768px) {
  /* tl breakpoint */
  .content-tab .q-tab__label {
    font-size: 1.6rem;
  }
}

@media (min-width: 1024px) {
  /* dt breakpoint */
  .content-tab .q-tab__label {
    font-size: 1.5rem;
  }
}

.w-content {
  width: calc(100vw - 1.5rem);
}

.q-layout__shadow {
  display: none;
}

.q-btn:before {
  box-shadow: none;
}

.q-card {
  box-shadow: none;
}

.q-table__container {
  box-shadow: none;
}

.markdown-heading h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.markdown-heading h3 {
  font-size: 1.2rem;
  font-weight: 600;
}

.popup-content-class-custom .q-virtual-scroll__content .q-item {
  font-size: 18px;
}

@media (min-width: 768px) {
  .popup-content-class-custom .q-virtual-scroll__content .q-item {
    font-size: 2rem !important;
  }
}

#price-input-id {
  display: none !important;
}

.input-price .q-field__control::before {
  border-left-width: 0px !important;
  border-right-width: 0px !important;
  border-radius: 0px !important;
}

.input-price .q-field__control::after {
  border-left-width: 0px !important;
  border-right-width: 0px !important;
  border-radius: 0px !important;
}

.q-select__dialog .q-field__control {
  align-items: center;
}

.vs .q-field--auto-height .q-field__control,
.vs .q-field--auto-height .q-field__native,
.vs .q-field__native,
.vs .q-field__marginal {
  line-height: 1 !important;
  min-height: unset;
  height: 4.25rem !important;
}

.vs .q-field__control,
.vs .q-field--outlined .q-field__control:after {
  min-height: unset;
  height: 4.25rem !important;
}

.icon .q-field__control {
  padding-right: 0px !important;
}

.bg-blue-3 {
  background-color: #004ab9 !important;
}

.text-blue-3 {
  color: #004ab9 !important;
}

.q-table__card {
  color: #333333 !important;
}

body {
  color: #333333;
}

.height-header {
  height: 3.5rem;
  min-height: 3.5rem;
}

@media (min-width: 961px) {
  .height-header {
    height: inherit;
    min-height: unset;
  }
}

.input .vs .q-field__control,
.input .vs .q-field__native,
.input .vs,
.input .vs .q-field--disabled,
.input .vs .q-field--outlined .q-field__control:after,
.input .vs .q-field__marginal {
  line-height: 1 !important;
  min-height: 5rem !important;
  height: 5rem !important;
}

html:not(.admin-layout) .q-field__native {
  color: #333333 !important;
}

html:not(.admin-layout) .q-field__messages {
  font-size: 1.5rem;
}

