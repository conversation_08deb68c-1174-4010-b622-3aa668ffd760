<template>
  <div class="tw:w-[40%] tw:min-w-[550px] tw:m-auto">
    <!-- title -->
    <div class="tw:my-5 tw:font-bold tw:text-center tw:text-2xl tw:border-b tw:border-gray tw:pb-4">
      パスワード再登録
    </div>
    <!-- content -->
    <div>
      <div class="tw:leading-[2rem] tw:text-base">
        パスワード(英字数値8文字以下組み合わせ)<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-3"
        outlined
        v-model="newPassword"
        autocomplete="off"
        :type="showNewPassword ? 'text' : 'password'"
        lazy-rules
        maxlength="256"
        :error="!!errors.password" :error-message="errors.password" no-error-icon
      >
        <template v-slot:append>
          <q-icon
            :name="showNewPassword ? 'visibility' : 'visibility_off'"
            @click.prevent="showNewPassword = !showNewPassword"
          />
        </template>
      </q-input>
      <div class="tw:text-base tw:mt-5 tw:leading-[2rem]">
        パスワード(確認用)<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-3"
        outlined
        v-model="comparePassword"
        autocomplete="off"
        :type="showComparePassword ? 'text' : 'password'"
        lazy-rules
        maxlength="256"
        :error="!!errors.passwordConfirm" :error-message="errors.passwordConfirm" no-error-icon
      >
        <template v-slot:append>
          <q-icon
            :name="showComparePassword ? 'visibility' : 'visibility_off'"
            @click.prevent="showComparePassword = !showComparePassword"
          />
        </template>
      </q-input>
      <q-btn
        class="tw:w-full tw:mt-5 tw:text-base tw:font-bold tw:text-white"
        label="登録する"
        type="button"
        :class="`tw:bg-${colorSub}`"
        @click.prevent="changePassword"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import resetPassword from 'schemas/resetPassword';
import authService from 'services/auth.service';

const { colorSub } = storeToRefs(useAppStore());
const { errors, validateData } = useValidate();
const router = useRouter();

// ===== REF =====
const newPassword = ref('');
const comparePassword = ref('');
const showNewPassword = ref(false);
const showComparePassword = ref(false);

// ===== METHOD =====
const changePassword = async () => {
  // change password
  const valid = validateData(
    resetPassword,
    { password: newPassword.value, passwordConfirm: comparePassword.value }
  );
  if (!valid) {
    return;
  }
  const result = await authService.resetPassword(
    { password: newPassword.value },
    router.currentRoute.value.params?.code
  );
  if (result.code === 0) {
    // Go to the complete change pass
    await router.push({ name: 'adminCompleteChangePassword' });
  }
};
</script>
