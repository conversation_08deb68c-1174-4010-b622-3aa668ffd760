<template>
  <div class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
    <ExpansionSearchItem v-model:expanded="isSearchExpanded">
      <PartnerHybridSearch />
    </ExpansionSearchItem>
    <div
      v-if="!checkConditionIsEmpty(searchQueryConditions)"
      class="tw:mt-4 tw:text-xs-design tw:text-[#333333] tw:flex tw:items-center tw:gap-4 tw:flex-wrap"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]"
        >検索条件</span
      >
      <span
        v-if="Number.isInteger(searchQueryConditions.type)"
        class="tw:text-xs-design tw:p-2 tw:rounded tw:bg-white"
      >
        {{ mapEnterPriseType(+searchQueryConditions.type) }}
      </span>
      <span
        v-if="searchQueryConditions.name"
        class="tw:text-xs-design tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchQueryConditions.name }}
      </span>
      <span
        v-if="
          searchQueryConditions.userCode
        "
        class="tw:text-xs-design tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchQueryConditions.userCode }}
      </span>
      <span
        v-if="searchQueryConditions.licenseNumber"
        class="tw:text-xs-design tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchQueryConditions.licenseNumber }}
      </span>
    </div>

    <q-card
      class="tw:flex tw:p-4 tw:mt-4 tw:flex-col tw:h-full"
      v-if="partnerHybridList?.length > 0 && isStartingSearch"
    >
      <!-- filter end -->
      <!-- sort btn start -->
      <div
        class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:items-start tw:border-neutral-500 tw:pt-1"
      >
        <div class="tw:flex tw:tl:flex-row tw:flex-col tw:space-y-4">
          <div
            class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]"
          >
            <span class="tw:text-xs-design">表示件数</span>
            <PageSizeDropdownSP v-model="pagination.rowsPerPage" />
          </div>
          <SortByDropdownSP
            v-model="sortBySelectedSP"
            class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden"
          />
        </div>
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:pt-5 tw:tl:pt-0 tw:w-full tw:tl:w-auto"
        >
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block">
        <q-table
          class="tw:border tw:border-[#D2D2D2] tw:border-collapse"
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="pagination"
        >
          <template v-slot:header="props">
            <q-tr
              :props="props"
              class="tw:text-s-design tw:border tw:border-[#D2D2D2]"
              :class="`tw:bg-[#E2E3EA]`"
            >
              <q-th
                @click="handleClickSort(props.cols[0].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[0].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc'
                      ? 'north'
                      : 'south'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2] tw:dt:w-[24rem] tw:tl:w-[24rem]"
              >
                {{ props.cols[1].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc'
                      ? 'north'
                      : 'south'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[2].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:dt:w-[18rem] tw:tl:w-[18rem]"
              >
                {{ props.cols[2].label }}
                <q-icon
                  @click="handleClickSort(props.cols[2].name)"
                  v-if="getSortOrder(props.cols[2].name)"
                  :name="
                    getSortOrder(props.cols[2].name) === 'asc'
                      ? 'north'
                      : 'south'
                  "
                  size="24px"
                  class="tw:ml-1"
                />
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr class="tw:cursor-pointer tw:w-full" :props="props">
              <q-td
                key="name"
                :props="props"
                class="text-left tw:border-r tw:border-[#D2D2D2] tw:text-s-design"
              >
                <div
                  :class="`tw:truncate tw:tl:max-w-[500px] tw:dt:max-w-[580px] tw:underline tw:underline-offset-1 tw:text-[#004AB9] tw:font-bold`"
                  @click.prevent="handleClickPartnerHybridDetail(_, props.row)"
                >
                  {{ props.row.name }}
                </div>
              </q-td>
              <q-td
                key="type"
                :props="props"
                class="text-center tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:dt:w-[24rem] tw:tl:w-[24rem]">
                  {{
                    mapEnterpriseTypeStaffTypeToDisplay(
                      props.row.type,
                      props.row.staffType
                    )
                  }}
                </div>
              </q-td>
              <q-td
                key="userCode"
                :props="props"
                class="text-center border-r border-l border-b border-td-color tw:text-s-design"
              >
                <div class="tw:truncate tw:dt:w-[18rem] tw:tl:w-[18rem]">
                  {{ props.row.userCode }}
                </div>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </div>
      <!-- start table for smartphone -->
      <div class="tw:block tw:tl:hidden">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-5 tw:mt-4"
          card-class="tw:w-full"
          :rows="sortedRows"
          row-key="index"
          hide-pagination
          hide-header
          :columns="columns"
          v-model:pagination="pagination"
        >
          <template v-slot:item="props">
            <div
              class="tw:w-full"
              @click.prevent="handleClickPartnerHybridDetail(_, props.row)"
            >
              <q-card flat bordered>
                <q-card-section class="tw:text-left tw:cursor-pointer">
                  <div
                    class="tw:flex-1 tw:flex tw:items-center tw:justify-between"
                  >
                    <div>
                      <strong
                        class="tw:text-s-design tw:text-[#004AB9] tw:break-words tw:font-bold"
                      >
                        {{ props.row.name }}</strong
                      >
                      <div class="tw:text-[#333333] tw:text-s-design">
                        <span>
                          {{
                            mapEnterpriseTypeStaffTypeToDisplay(
                              props.row.type,
                              props.row.staffType
                            )
                          }}</span
                        >
                      </div>
                      <div class="tw:text-[#333333] tw:text-s-design">
                        <span>{{ props.row.userCode }}</span>
                      </div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </template>
        </q-table>
      </div>
      <div
        class="tw:tl:justify-end tw:flex tw:justify-center tw:pt-5 tw:w-full tw:tl:w-auto"
      >
        <PaginationNotifi />
      </div>
    </q-card>
    <q-card
      v-if="partnerHybridList?.length === 0 && isStartingSearch"
      class="tw:flex tw:p-4 tw:mt-4"
    >
      <div class="tw:w-full tw:text-start tw:font-normal tw:text-s-design">
        対象の取引先が見つかりませんでした。新しい取引先を登録できます。
      </div>
    </q-card>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3
      tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:items-center tw:flex tw:justify-center tw:tl:justify-start
      tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[21.2rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="取引先管理に戻る"
        @click.prevent="router.push({ name: 'partner' })"
      />
      <div class="tw:text-m-design tw:text-black tw:pl-2">
        取引先が見つからない場合
      </div>
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="新規登録"
        @click.prevent="router.push({ name: 'partnerInput' })"
      />
    </q-footer>
  </div>
</template>
<script setup>
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import { useLocalStorage } from 'composables/localstorage';
import useValidate from 'composables/validate';
import { isEqual, orderBy } from 'lodash';
import partnerService from 'services/locatedTransaction.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import SortByDropdownSP from 'src/components/SortByDropdownSP.vue';
import {
  ENTERPRISE_TYPE_ENUM,
  ENTERPRISE_TYPE_OPTIONS,
  ROLES_ENUM_OPTIONS_VALUES,
  STAFF_TYPE_ENUM,
  USER_ROLE_OPTIONS,
} from 'src/helpers/constants';
import searchPartnerHybridListConditionSchema from 'src/schemas/partner/searchPartnerHybridListCondition.schema';
import { computed, onMounted, provide, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import PartnerHybridSearch from './components/PartnerHybridSearch.vue';

// #region variable
const { validateData, errors } = useValidate();
const hasDiffPageIndex = ref(false);
const router = useRouter();
const sortBySelectedSP = ref('');
const isSearchExpanded = ref(true);
const isStartingSearch = ref(false);
const { listPageQueryParams } = useLocalStorage();
// because quasar does not support mutli sort, we use this variable to manage sort conditions
const multiSortConditions = ref([]);
// default pagination for table
const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: 1,
  rowsPerPage: listPageQueryParams.value?.partner?.rowsPerPage || 10,
});

// for manage state of search form
const searchForm = ref({
  type: '',
  name: '',
  userCode: '',
  licenseNumber: '',
});
// for manage state of search form data (validated)
const searchFormData = ref({
  type: '',
  name: '',
  userCode: '',
  licenseNumber: '',
});
// for table columns structure
const columns = [
  {
    name: 'name',
    label: '事業者名',
    align: 'left',
    field: 'name',
    sortable: true,
  },
  {
    name: 'type',
    label: '事業者/従事者区分',
    align: 'left',
    field: 'type',
    sortable: true,
  },
  {
    name: 'userCode',
    label: 'ユーザーID',
    align: 'left',
    field: 'userCode',
    sortable: true,
  },
];
const partnerHybridList = ref([]);
const pageIndex = ref(1);
const totalPage = ref(1);
// #region variable

// #region function
const handleClickPartnerHybridDetail = async (_, row) => {
  await router.push({
    name: 'partnerHybridDetail',
    params: {
      id: row.id,
    },
  });
};

const getPartnerHybridList = async () => {
  // do not pass sortBy and descending to server because sorting is done on client side
  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
    sortBy: undefined,
    descending: undefined,
  };
  const response = await partnerService.searchPartnerHybridList(query);
  if (response.code === 0) {
    isStartingSearch.value = true;
    partnerHybridList.value = response.payload.items.map(item => ({
      id: item.id,
      name: item.name,
      type: item.enterprise_type,
      userCode: item.user_code,
      staffType: item.staff_type,
    }));

    // calculate total page
    totalPage.value = Math.ceil(
      (partnerHybridList.value.length ?? 1) / pagination.value.rowsPerPage
    );

    // check if page index is different from the value server return
    // if different, replace query to the new page index (value server return)
    if (response.payload.page !== pageIndex.value || totalPage.value < pageIndex.value) {
      hasDiffPageIndex.value = true;
      router.replace({
        query: {
          ...pagination.value,
          sortBy: formatSortConditionsToQuery(),
          descending: undefined,
          page: 1,
          ...formatSearchFormToQuery(searchForm.value),
        },
      });
    }
  }
};

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }

  router.push({
    query: {
      ...pagination.value,
      sortBy: formatSortConditionsToQuery(),
      descending: undefined,
      ...formatSearchFormToQuery(searchFormData.value),
    },
  });
};
// #endregion function

// #region helper function
const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const formatSearchFormToQuery = formData => ({
  type: formData.type?.toString() || undefined,
  name: formData.name?.trim() || undefined,
  userCode: formData.userCode?.trim() || undefined,
  licenseNumber: formData.licenseNumber?.trim() || undefined,
});

const formatSortConditionsToQuery = () =>
  multiSortConditions.value
    .map(item => `${item.key}.${item.order}`)
    .join(',') || undefined;

const checkConditionIsEmpty = condition =>
  Object.values(condition).every(
    value => value === '' || value === undefined || value === null
  );

const mapEnterPriseType = type =>
  ENTERPRISE_TYPE_OPTIONS.find(item => item.value === type)?.label || '';

const mapEnterpriseTypeStaffTypeToDisplay = (enterprise_type, staff_type) => {
  let role = '';
  switch (enterprise_type) {
    case ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE:
      if (staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
        role = ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE;
      } else {
        role = ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF;
      }
      break;
    case ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE:
      if (staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
        role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE;
      } else {
        role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF;
      }
      break;
    default:
      role = ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE;
  }
  return USER_ROLE_OPTIONS.find(option => option.value === role)?.label || '';
};

const resetState = () => {
  isSearchExpanded.value = true;
  isStartingSearch.value = false;
  partnerHybridList.value = [];
  pagination.value = {
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: listPageQueryParams.value?.partner?.rowsPerPage || 10,
  };
  searchForm.value = {
    type: '',
    name: '',
    userCode: '',
    licenseNumber: '',
  };
  searchFormData.value = { ...searchForm.value };
};
// #endregion helper function

// #region computed
const sortedRows = computed(() => {
  const rows = [...partnerHybridList.value];
  const lstKey = [];
  const lstOrder = [];
  for (const { key, order } of multiSortConditions.value) {
    lstKey.push(key);
    lstOrder.push(order);
  }
  return orderBy(rows, lstKey, lstOrder);
});

const searchQueryConditions = computed(() => ({
  type: router.currentRoute.value.query.type?.length
    ? +router.currentRoute.value.query.type
    : '',
  name: router.currentRoute.value.query.name || '',
  userCode: router.currentRoute.value.query.userCode || '',
  licenseNumber: router.currentRoute.value.query.licenseNumber || '',
}));
// #endregion computed

// #region watch
watch(
  () => pagination.value.rowsPerPage,
  () => {
    if (pagination.value.rowsPerPage && isStartingSearch.value) {
      pageIndex.value = 1;
      router.push({
        query: {
          ...pagination.value,
          sortBy: formatSortConditionsToQuery(),
          descending: undefined,
          page: 1,
          ...formatSearchFormToQuery(searchFormData.value),
        },
      });
    }
    if (pagination.value.rowsPerPage) {
      totalPage.value = Math.ceil(
        sortedRows.value.length / pagination.value.rowsPerPage
      );
    }
  }
);
watch(
  () =>
    router.currentRoute.value.query.type +
    router.currentRoute.value.query.name +
    router.currentRoute.value.query.userCode +
    router.currentRoute.value.query.licenseNumber,
  async () => {
    if (checkConditionIsEmpty(searchQueryConditions.value)) {
      resetState();
      return;
    }

    if (router.currentRoute.value.query.rowsPerPage) {
      listPageQueryParams.value.partner = {
        rowsPerPage: +router.currentRoute.value.query.rowsPerPage,
      };
    }
    pageIndex.value = +router.currentRoute.value.query.page;

    pagination.value = {
      ...pagination.value,
      page: +router.currentRoute.value.query.page,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage,
    };
    searchForm.value = {
      type: router.currentRoute.value.query.type?.length
        ? +router.currentRoute.value.query.type
        : '',
      name: router.currentRoute.value.query.name,
      userCode: router.currentRoute.value.query.userCode,
      licenseNumber: router.currentRoute.value.query.licenseNumber,
    };
    searchFormData.value = {
      ...searchForm.value,
    };

    // Use this for sm specific case
    // if hasDiffPageIndex is false, get data
    // if hasDiffPageIndex is true, do nothing because the query has been replaced
    if (!hasDiffPageIndex.value) {
      await getPartnerHybridList();
    } else {
      hasDiffPageIndex.value = false;
    }
  }
);
watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.page,
  () => {
    if (checkConditionIsEmpty(searchQueryConditions.value)) {
      resetState();
      return;
    }
    pageIndex.value = +router.currentRoute.value.query.page || 1;
    pagination.value = {
      ...pagination.value,
      page: +router.currentRoute.value.query.page,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage,
    };
    if (router.currentRoute.value.query.sortBy) {
      multiSortConditions.value = router.currentRoute.value.query.sortBy
        .split(',')
        .map(item => {
          const [key, order] = item.split('.');
          return { key, order: order || 'asc' };
        });
    } else {
      multiSortConditions.value = [];
    }
  }
);
// #endregion watch

// #region provide
const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      type: '',
      name: '',
      userCode: '',
      licenseNumber: '',
    };
    errors.value = {};
  },
  async handleSearch() {
    const payload = {
      type: searchForm.value.type !== '' ? +searchForm.value.type : '',
      name: searchForm.value.name || '',
    };

    if (searchForm.value.type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
        payload.license_number = searchForm.value.licenseNumber || '';
    } else {
       payload.user_code = searchForm.value.userCode || '';
    }

    if (isEqual({ ...searchForm.value }, { ...searchFormData.value }) && !checkConditionIsEmpty(payload)) {
      await getPartnerHybridList();
      this.isClose = true;
    } else {
      const validate = validateData(
        searchPartnerHybridListConditionSchema,
        payload
      );
      if (validate) {
        pageIndex.value = 1;
        await router.push({
          query: {
            ...pagination.value,
            sortBy: formatSortConditionsToQuery(),
            descending: undefined,
            page: 1,
            ...formatSearchFormToQuery(searchForm.value),
          },
        });
        this.isClose = true;
      } else {
        this.isClose = false;
      }
    }
  },
  isClose: false,
  title: '登録済の取引先をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: 'ユーザーID順', value: 'userCode' },
    { label: '事業者名順', value: 'name' },
  ],
  handleClickSortByItem: async option => {
    multiSortConditions.value = [{ key: option.value, order: 'asc' }];
    await router.push({
      query: {
        ...pagination.value,
        sortBy: formatSortConditionsToQuery(),
        descending: undefined,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        sortBy: formatSortConditionsToQuery(),
        descending: undefined,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);

onMounted(async () => {
  if (!checkConditionIsEmpty(searchQueryConditions.value)) {
    isSearchExpanded.value = false;
    pageIndex.value = +router.currentRoute.value.query.page;
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      rowsPerPage: router.currentRoute.value.query.rowsPerPage || 10,
    };
    searchForm.value = { ...searchQueryConditions.value };
    searchFormData.value = { ...searchForm.value };
    if (
      // sortBy=name.asc,type.desc
      router.currentRoute.value.query.sortBy
    ) {
      multiSortConditions.value = router.currentRoute.value.query.sortBy
        .split(',')
        .map(item => {
          const [key, order] = item.split('.');
          return { key, order: order || 'asc' };
        });
      if (multiSortConditions.value.length === 1) {
        const sortByOption = sortByDropdownProvideData.sortByOptions.find(
          option => option.value === multiSortConditions.value[0].key
        );
        sortBySelectedSP.value = sortByOption;
      }
    }
    await getPartnerHybridList();
  }
});
</script>
