import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import papa from 'papaparse';

const s3Client = new S3Client({ region: process.env.REGION });

const csvHeader = [
  'urlcardid',
  'user_code',
  'init_password',
  'enterprise_type',
  'enterprise_code',
  'staff_type',
  'license_number',
  'name',
  'name_kana',
  'province_name',
  'phone',
  'expiry_date_start',
  'expiry_date',
  'status',
  'account_registration_status',
  'note_1',
  'note_2',
];

const streamToString = (stream) =>
  new Promise((resolve, reject) => {
    const chunks = [];
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks).toString()));
  });

const parseCSVToJSon = (data) =>
  new Promise((resolve) => {
    const headers = [...csvHeader];
    papa.parse(data, {
      header: true,
      skipEmptyLines: true,
      encoding: 'UTF-8-BOM',
      transformHeader: (h, index) => {
        if (!headers[index]) {
          headers.push(h);
        }
        return headers[index];
      },
      complete: async (results) => {
        resolve({ dataImport: results.data, header: headers });
      },
    });
  });

export const getDataFromS3 = async (bucketName, s3KeyFile) => {
  const inputGetObject = {
    Bucket: bucketName,
    Key: s3KeyFile
  }
  const getObjectCommand = new GetObjectCommand(inputGetObject);
  const { Body } = await s3Client.send(getObjectCommand);
  const csvData = await streamToString(Body);
  const { dataImport, header } = await parseCSVToJSon(csvData);
  return { rawDataImports: dataImport, header };
}
