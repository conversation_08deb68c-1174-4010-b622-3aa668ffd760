import ME<PERSON><PERSON><PERSON> from 'helpers/message';
import { USER_STATUS_ENUM, USER_STATUS_REGISTER_ENUM } from 'src/helpers/constants';

const searchAllUserList = {
  additionalProperties: false,
  type: 'object',
  properties: {
    page: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_INDEX_INVALID,
      },
    },
    limit: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_SIZE_INVALID,
      },
    },
    provinceId: {
      type: 'integer',
    },
    enterpriseName: {
      type: 'string',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    userName: {
      type: 'string',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    userId: {
      type: 'string',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    status: {
      type: 'integer',
      enum: Object.values(USER_STATUS_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    statusRegister: {
      type: 'integer',
      enum: Object.values(USER_STATUS_REGISTER_ENUM),
    },
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    userType: {
      type: 'string',
    },
    startUpdateDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endUpdateDate',
      },
      errorMessage: {
        format: MESSAGE.START_ARRIVAL_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endUpdateDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.END_ARRIVAL_DATE_ERROR,
      },
    },
    startExpiryDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endExpiryDate',
      },
      errorMessage: {
        format: MESSAGE.START_ARRIVAL_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endExpiryDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.END_ARRIVAL_DATE_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.NOTE1_ERROR,
      },
    },
    note2: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.NOTE2_ERROR,
      },
    },
  },
};

export default searchAllUserList;
