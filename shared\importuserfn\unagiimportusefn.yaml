AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Parameters:
  StageName:
    Type: String
    Default: staging
  SecurityGroupIds:
    Type: String
    Default: ''
  SubnetIds:
    Type: String
    Default: ''
  S3Bucket:
    Type: String
    Default: ''

Globals:
  Function:
    Runtime: nodejs20.x
    AutoPublishAlias: live
    DeploymentPreference:
      Type: AllAtOnce
    CodeUri: src/
    Timeout: 900
    MemorySize: 4096
    Environment:
      Variables:
        StageName:
          Ref: StageName
        SecurityGroupIds:
          Ref: SecurityGroupIds
        SubnetIds:
          Ref: SubnetIds
    VpcConfig:
        SecurityGroupIds: !Split [ '|', !Ref SecurityGroupIds ]
        SubnetIds: !Split [ '|', !Ref SubnetIds ]

Resources:
  awsimportuserfromcsvfn:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: awss3_importuserfromcsv_fn
      Handler: index.handler
      Policies:
        - AmazonDynamoDBFullAccess
        - AmazonRDSFullAccess
        - AmazonEC2FullAccess
        - AmazonS3FullAccess
        - AmazonSESFullAccess
        - AmazonSSMFullAccess
        - AWSLambdaBasicExecutionRole
        - AWSLambdaRole
        - AWSLambdaVPCAccessExecutionRole