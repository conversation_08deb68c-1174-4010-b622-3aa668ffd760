<!-- eslint-disable max-len -->
<template>
  <div class="tw:tl:px-3" id="header">
    <q-card class="tw:p-4 tw:mt-4 tw:mb-4 tw:bg-white">
      <span class="tw:text-l-design tw:font-bold">ライセンス</span>
      <div class="markdown-heading" v-html="mdLicense"></div>
    </q-card>
  </div>
  <q-footer
    elevated
    class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
    tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
      label="トップに戻る"
      @click.prevent="goToPage('home')"
    />
  </q-footer>
</template>
<script setup>
// #region import
import { onMounted, ref } from 'vue';
import markdownit from 'markdown-it';
import linkifyHtml from 'linkify-html';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { useRouter } from 'vue-router';
// #endregion import
const router = useRouter();
// #region variable
const md = markdownit();
const mdLicense = ref('');
// #endregion variable

// #region function
// initialization

const goToPage = name => {
  router.push({ name });
};
onMounted(async () => {
  const options = {
    target: '_blank',
    rel: 'noopener noreferrer',
    defaultProtocol: 'https',
    className: 'tw:underline-offset-1 tw:cursor-pointer tw:break-all',
  };
  const encodedStr = `## ウナギ産業価値連鎖トレーサビリティ支援システム試用版
### 2024年10月25日16時20分
-------------------

### @intlify/vite-plugin-vue-i18n
  * **URL:** https://github.com/intlify/bundle-tools/blob/main/packages/vite-plugin-vue-i18n/README.md
  * **Version:** 7.0.0
  * **License:** MIT
  * **Description:** Vite plugin for Vue I18n

### @quasar/app-vite
  * **URL:** https://quasar.dev
  * **Version:** 1.10.2
  * **License:** MIT
  * **Description:** Quasar Framework App CLI with Vite

### @quasar/extras
  * **URL:** https://quasar.dev
  * **Version:** 1.16.12
  * **License:** MIT
  * **Description:** Quasar Framework fonts, icons and animations

### @vueuse/core
  * **URL:** https://github.com/vueuse/vueuse#readme
  * **Version:** 10.11.0
  * **License:** MIT
  * **Description:** Collection of essential Vue Composition Utilities

### @vueuse/integrations
  * **URL:** https://github.com/vueuse/vueuse/tree/main/packages/integrations#readme
  * **Version:** 10.11.0
  * **License:** MIT
  * **Description:** Integration wrappers for utility libraries

### ajv
  * **URL:** https://ajv.js.org
  * **Version:** 8.17.1
  * **License:** MIT
  * **Description:** Another JSON Schema Validator

### ajv-errors
  * **URL:** https://github.com/epoberezkin/ajv-errors#readme
  * **Version:** 3.0.0
  * **License:** MIT
  * **Description:** Custom error messages in JSON Schemas for Ajv validator

### ajv-formats
  * **URL:** https://github.com/ajv-validator/ajv-formats#readme
  * **Version:** 3.0.1
  * **License:** MIT
  * **Description:** Format validation for Ajv v7+

### ajv-keywords
  * **URL:** https://github.com/epoberezkin/ajv-keywords#readme
  * **Version:** 5.1.0
  * **License:** MIT
  * **Description:** Additional JSON-Schema keywords for Ajv JSON validator

### autoprefixer
  * **Version:** 10.4.7
  * **License:** MIT
  * **Description:** Parse CSS and add vendor prefixes to CSS rules using values from the Can I Use website

### axios
  * **URL:** https://axios-http.com
  * **Version:** 1.7.7
  * **License:** MIT
  * **Description:** Promise based HTTP client for the browser and node.js

### cleave.js
  * **URL:** https://github.com/nosir/cleave.js
  * **Version:** 1.6.0
  * **License:** Apache-2.0
  * **Description:** JavaScript library for formatting input text content when you are typing

### dayjs
  * **URL:** https://day.js.org
  * **Version:** 1.11.12
  * **License:** MIT
  * **Description:** 2KB immutable date time library alternative to Moment.js with the same modern API

### dompurify
  * **URL:** https://github.com/cure53/DOMPurify
  * **Version:** 3.1.7
  * **License:** (MPL-2.0 OR Apache-2.0)
  * **Description:** DOMPurify is a DOM-only, super-fast, uber-tolerant XSS sanitizer for HTML, MathML and SVG.
  * It&#39;s written in JavaScript and works in all modern browsers (Safari, Opera (15+), Internet Explorer (10+),
  * Firefox and Chrome - as well as almost anything else using Blink or WebKit).
  * DOMPurify is written by security people who have vast background in web attacks and XSS. Fear not.

### encoding-japanese
  * **URL:** https://github.com/polygonplanet/encoding.js
  * **Version:** 2.2.0
  * **License:** MIT
  * **Description:** Convert and detect character encoding in JavaScript

### eslint
  * **URL:** https://eslint.org
  * **Version:** 8.57.0
  * **License:** MIT
  * **Description:** An AST-based pattern checker for JavaScript.

### eslint-config-airbnb-base
  * **URL:** https://github.com/airbnb/javascript
  * **Version:** 15.0.0
  * **License:** MIT
  * **Description:** Airbnb&#39;s base JS ESLint config, following our styleguide

### eslint-plugin-import
  * **URL:** https://github.com/import-js/eslint-plugin-import
  * **Version:** 2.26.0
  * **License:** MIT
  * **Description:** Import with sanity.

### eslint-plugin-vue
  * **URL:** https://eslint.vuejs.org
  * **Version:** 9.2.0
  * **License:** MIT
  * **Description:** Official ESLint plugin for Vue.js

### html2pdf.js
  * **URL:** https://ekoopmans.github.io/html2pdf.js/
  * **Version:** 0.10.2
  * **License:** MIT
  * **Description:** Client-side HTML-to-PDF rendering using pure JS

### jsqr
  * **Version:** 1.4.0
  * **License:** Apache-2.0
  * **Description:** A pure javascript QR code reading library that takes in raw images and will locate, extract and parse any QR code found within.

### linkify-html
  * **URL:** https://linkify.js.org
  * **Version:** 4.1.3
  * **License:** MIT
  * **Description:** HTML String interface for linkifyjs

### linkifyjs
  * **URL:** https://linkify.js.org
  * **Version:** 4.1.3
  * **License:** MIT
  * **Description:** Find URLs, email addresses, #hashtags and @mentions in plain-text strings, then convert them into HTML &lt;a&gt; links.

### lodash
  * **URL:** https://lodash.com/
  * **Version:** 4.17.21
  * **License:** MIT
  * **Description:** Lodash modular utilities.

### markdown-it
  * **Version:** 14.1.0
  * **License:** MIT
  * **Description:** Markdown-it - modern pluggable markdown parser.

### papaparse
  * **URL:** http://papaparse.com
  * **Version:** 5.4.1
  * **License:** MIT
  * **Description:** Fast and powerful CSV parser for the browser that supports web workers and streaming large files.
  * Converts CSV to JSON and JSON to CSV.

### pinia
  * **URL:** https://github.com/vuejs/pinia#readme
  * **Version:** 2.2.0
  * **License:** MIT
  * **Description:** Intuitive, type safe and flexible Store for Vue

### postcss
  * **URL:** https://postcss.org/
  * **Version:** 8.4.40
  * **License:** MIT
  * **Description:** Tool for transforming styles with JS plugins

### qrcode
  * **URL:** http://github.com/soldair/node-qrcode
  * **Version:** 1.5.3
  * **License:** MIT
  * **Description:** QRCode / 2d Barcode api with both server side and client side support using canvas

### quasar
  * **URL:** https://quasar.dev
  * **Version:** 2.16.6
  * **License:** MIT
  * **Description:** Build high-performance VueJS user interfaces (SPA, PWA, SSR, Mobile and Desktop) in record time

### tailwindcss
  * **URL:** https://tailwindcss.com
  * **Version:** 3.4.7
  * **License:** MIT
  * **Description:** A utility-first CSS framework for rapidly building custom user interfaces.

### typescript
  * **URL:** https://www.typescriptlang.org/
  * **Version:** 5.6.3
  * **License:** Apache-2.0
  * **Description:** TypeScript is a language for application scale JavaScript development

### vite-plugin-checker
  * **URL:** https://github.com/fi3ework/vite-plugin-checker
  * **Version:** 0.7.2
  * **License:** MIT
  * **Description:** Vite plugin that runs TypeScript type checker on a separate process.

### vue
  * **URL:** https://github.com/vuejs/core/tree/main/packages/vue#readme
  * **Version:** 3.4.34
  * **License:** MIT
  * **Description:** The progressive JavaScript framework for building modern web UI.

### vue-i18n
  * **URL:** https://github.com/intlify/vue-i18n-next/tree/master/packages/vue-i18n#readme
  * **Version:** 9.13.1
  * **License:** MIT
  * **Description:** Internationalization plugin for Vue.js

### vue-router
  * **URL:** https://github.com/vuejs/router#readme
  * **Version:** 4.4.0
  * **License:** MIT

### workbox-build
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.0.0
  * **License:** MIT
  * **Description:** A module that integrates into your build process, helping you generate a manifest of local files that workbox-sw should precache.

### workbox-cacheable-response
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.1.0
  * **License:** MIT
  * **Description:** This library takes a Response object and determines whether it&#39;s cacheable based on a specific configuration.

### workbox-core
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.1.0
  * **License:** MIT
  * **Description:** This module is used by a number of the other Workbox modules to share common code.

### workbox-expiration
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.1.0
  * **License:** MIT
  * **Description:** A service worker helper library that expires cached responses based on age or maximum number of entries.

### workbox-precaching
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.1.0
  * **License:** MIT
  * **Description:** This module efficiently precaches assets.

### workbox-routing
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.1.0
  * **License:** MIT
  * **Description:** A service worker helper library to route request URLs to handlers.

### workbox-strategies
  * **URL:** https://github.com/GoogleChrome/workbox
  * **Version:** 7.1.0
  * **License:** MIT
  * **Description:** A service worker helper library implementing common caching strategies.`;
  mdLicense.value = linkifyHtml(
    md.render(encodedStr),
    options
  );
});
// #endregion function
</script>
