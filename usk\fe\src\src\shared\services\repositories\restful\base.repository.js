import { SERVICE_ID, ROUTER_COMMON } from 'helpers/constants';

import RestClient from './restClient';

export default class BaseRepository {
  client;

  constructor(servicePath, version = 'v1') {
    if (ROUTER_COMMON.includes(servicePath)) {
      this.client = new RestClient(`${version}/common/${SERVICE_ID}/${servicePath}`);
    } else {
      this.client = new RestClient(`${version}/${SERVICE_ID}/${servicePath}`);
    }
  }
}
