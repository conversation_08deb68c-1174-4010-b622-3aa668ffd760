import BaseService from 'services/base.service';

class AdminUser extends BaseService {
  async getUserList(query) {
    try {
      const listUser = await this.dao.getUserList(query);
      const { code, payload } = listUser;
      if (code === 0) {
        return payload;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  async getUserDetail(userId) {
    try {
      const userDetail = await this.dao.getUserDetail(userId);
      const { code, payload } = userDetail;
      if (code === 0) {
        return payload;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  async registerUser(body) {
    try {
      return this.dao.registerUser(body);
    } catch (error) {
      return null;
    }
  }

  async editUser(id, body) {
    try {
      return this.dao.editUser(id, body);
    } catch (error) {
      return null;
    }
  }

  async presignImportUser(body) {
    try {
      return this.dao.presignImportUser(body);
    } catch (error) {
      return null;
    }
  }

  async getImportUser() {
    try {
      return this.dao.getImportUser();
    } catch (error) {
      return null;
    }
  }

  async downloadImportUser(body) {
    try {
      return this.dao.downloadImportUser(body);
    } catch (error) {
      return null;
    }
  }

  async bulkUpdateUserStatus(body) {
    try {
      return this.dao.bulkUpdateUserStatus(body);
    } catch (error) {
      return null;
    }
  }
}

export default new AdminUser('admin-user');
