import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';

const batchNonActiveUpdateSchema = {
  additionalProperties: false,
  type: 'object',
  required: ['roles', 'provinceId'],
  properties: {
    roles: {
      type: 'array',
      minItems: 1,
      uniqueItems: true,
      items: {
        type: 'integer',
        enum: Object.values(ENTERPRISE_TYPE_ENUM),
      },
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    provinceId: {
      type: 'array',
      minItems: 1,
      uniqueItems: true,
      items: {
        type: 'integer',
      },
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export default batchNonActiveUpdateSchema;
