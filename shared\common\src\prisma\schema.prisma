generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "postgresql"
  url          = env("W_DATABASE_URL")
  relationMode = "prisma"
}

model enterprises {
  id                           Int           @id @default(autoincrement())
  created_by_id                Int?          @default(0)
  latest_updated_by_id         Int?
  created_on                   DateTime?
  latest_updated_on            DateTime?
  delete_flag                  Boolean       @default(false)
  enterprise_code              String        @db.VarChar(256)
  enterprise_name              String        @db.VarChar(256)
  enterprise_name_kana         String?       @db.VarChar(256)
  enterprise_name_nospace      String        @db.VarChar(256)
  enterprise_name_kana_nospace String?       @db.VarChar(256)
  type                         Int
  users                        users[]
  the_origins_starting         the_origins[] @relation("StartingEnterprise")
  the_origins_destination      the_origins[] @relation("DestinationEnterprise")
  the_origins_history_starting  the_origins_history[] @relation("StartingEnterprise")
  the_origins_history_destination the_origins_history[] @relation("DestinationEnterprise")

  @@index([enterprise_code])
  @@index([enterprise_name_kana_nospace])
  @@index([enterprise_name_nospace])
}

model the_origins {
  id                        Int          @id @default(autoincrement())
  created_by_id             Int?         @default(0)
  latest_updated_by_id      Int?
  created_on                DateTime?
  latest_updated_on         DateTime?
  delete_flag               Boolean      @default(false)
  code                      String?
  shipping_date             DateTime?
  shipping_gross_weight     Decimal?     @db.Decimal(65, 2)
  shipping_tare_weight      Decimal?     @db.Decimal(65, 2)
  shipping_net_weight       Decimal?     @db.Decimal(65, 2)
  shipping_quantity         Int?
  arrival_gross_weight      Decimal?     @db.Decimal(65, 2)
  arrival_tare_weight       Decimal?     @db.Decimal(65, 2)
  arrival_net_weight        Decimal?     @db.Decimal(65, 2)
  arrival_quantity          Int?
  arrival_date              DateTime?
  type_diff                 Int?
  shipping_type             Int?
  reason_diff               String?      @db.VarChar(300)
  qr_code                   String?      @db.VarChar(256)
  destination_enterprise_id Int?
  starting_enterprise_id    Int?
  inventory_id              Int?
  ingredient                Json?
  setting                   Json?
  destination_user_id       Int?
  starting_user_id          Int?
  shipping_type_diff        Int?
  shipping_reason_diff      String?      @db.VarChar(300)
  operating_days            Int?         @default(0)
  destination_enterprise_name String      @db.VarChar(256)
  destination_user_name     String      @db.VarChar(256)
  destination_license_number String?      @db.VarChar(256)
  destination_user_note_1   String?      @db.VarChar(256)
  destination_user_note_2   String?      @db.VarChar(256)
  starting_enterprise_name String      @db.VarChar(256)
  starting_user_name       String      @db.VarChar(256)
  starting_license_number  String?      @db.VarChar(256)
  starting_user_note_1     String?      @db.VarChar(256)
  starting_user_note_2     String?      @db.VarChar(256)
  shipping_inventory_type   Int @default(0)
  shipping_id_list          Json         @default("[{}]")
  destination_enterprise    enterprises? @relation("DestinationEnterprise", fields: [destination_enterprise_id], references: [id])
  starting_enterprise       enterprises? @relation("StartingEnterprise", fields: [starting_enterprise_id], references: [id])
  inventory                 inventories? @relation(fields: [inventory_id], references: [id])
  user_create               users?       @relation("UserCreated", fields: [created_by_id], references: [id])
  user_update               users?       @relation("UserUpdated", fields: [latest_updated_by_id], references: [id])
  destination_user          users?       @relation("UserShipper", fields: [destination_user_id], references: [id])
  starting_user             users?       @relation("UserSupplier", fields: [starting_user_id], references: [id])
  the_origins_history_created the_origins_history[]

  @@index([arrival_date])
  @@index([code])
  @@index([destination_enterprise_id])
  @@index([destination_user_id])
  @@index([inventory_id])
  @@index([qr_code])
  @@index([shipping_date])
  @@index([shipping_type])
  @@index([starting_enterprise_id])
  @@index([starting_user_id])
}

model inventories {
  id                     Int                   @id @default(autoincrement())
  delete_flag            Boolean               @default(false)
  created_by_id          Int?                  @default(0)
  latest_updated_by_id   Int?
  created_on             DateTime
  latest_updated_on      DateTime?
  group_name             String?               @db.VarChar(256)
  net_weight_inventory   Decimal               @db.Decimal(65, 2)
  net_weight_total       Decimal               @db.Decimal(65, 2)
  latest_arrival_date    DateTime
  cancelable_from_date   DateTime?
  gross_weight_inventory Decimal               @db.Decimal(65, 2)
  tare_weight_inventory  Decimal               @db.Decimal(65, 2)
  is_history_cancel_locked Boolean             @default(true)
  user_id                Int
  inventories_history    inventories_history[]
  the_origins            the_origins[]
  the_origins_history    the_origins_history[]
  current_arrival_id_list Json? @default("[]")
  inventory_type          Int                  @default(0)
  inventory_start_date      DateTime

  @@index([group_name])
  @@index([latest_arrival_date])
  @@index([user_id])
  @@index([net_weight_inventory])
  @@index([delete_flag])
}

model inventories_history {
  id                         Int         @id @default(autoincrement())
  delete_flag                Boolean     @default(false)
  created_by_id              Int?        @default(0)
  latest_updated_by_id       Int?
  created_on                 DateTime
  latest_updated_on          DateTime?
  inventory_id               Int
  group_name                 String?     @db.VarChar(256)
  net_weight_inventory       Decimal     @db.Decimal(65, 2)
  net_weight_total           Decimal     @db.Decimal(65, 2)
  latest_arrival_date        DateTime
  gross_weight_inventory     Decimal     @db.Decimal(65, 2)
  new_gross_weight_inventory Decimal     @db.Decimal(65, 2)
  new_net_weight_inventory   Decimal     @db.Decimal(65, 2)
  new_tare_weight_inventory  Decimal     @db.Decimal(65, 2)
  tare_weight_inventory      Decimal     @db.Decimal(65, 2)
  user_id                    Int
  type_diff                  Int?
  reason_diff                String?     @db.VarChar(256)
  inventory                  inventories @relation(fields: [inventory_id], references: [id])
  current_arrival_id_list    Json        @default("[]")
  new_current_arrival_id_list Json       @default("[]")
  is_display_inventories_history Boolean @default(true)

  @@index([inventory_id])
  @@index([user_id])
}

model licenses {
  id                   Int       @id @default(autoincrement())
  created_by_id        Int?      @default(0)
  latest_updated_by_id Int?
  created_on           DateTime?
  latest_updated_on    DateTime?
  delete_flag          Boolean   @default(false)
  license_code         String    @unique @db.VarChar(256)
  user_id              Int?
  expiry_date_start    DateTime?
  expiry_date          DateTime?
  license_status        Int?
  users                users[]

  @@index([user_id])
}

model notifications {
  id                   Int       @id @default(autoincrement())
  created_by_id        Int?      @default(0)
  latest_updated_by_id Int?
  created_on           DateTime
  latest_updated_on    DateTime?
  delete_flag          Boolean   @default(false)
  title                String?   @db.VarChar(256)
  content              String?
  date_send            DateTime
  role_notify          String[]  @db.VarChar
  readers              readers[]
  creator              users?    @relation("CreatorNotification", fields: [created_by_id], references: [id])
}

model readers {
  id                   Int           @id @default(autoincrement())
  created_by_id        Int           @default(0)
  latest_updated_by_id Int?
  created_on           DateTime
  latest_updated_on    DateTime?
  delete_flag          Boolean       @default(false)
  notification_id      Int
  reader_id            Int
  notification         notifications @relation(fields: [notification_id], references: [id])
  reader               users         @relation(fields: [reader_id], references: [id])

  @@index([notification_id])
  @@index([reader_id])
}

model partners {
  id                   Int       @id @default(autoincrement())
  created_by_id        Int?      @default(0)
  latest_updated_by_id Int?
  created_on           DateTime?
  latest_updated_on    DateTime?
  delete_flag          Boolean   @default(false)
  user_id              Int
  partner_id           Int
  hidden_flag          Boolean   @default(false)
  partner_type         Int[]     @default([])
  user                 users     @relation("fk_pa_from", fields: [user_id], references: [id])
  partner              users     @relation("fk_pa_to", fields: [partner_id], references: [id])

  @@unique([user_id, partner_id])
  @@index([partner_id])
  @@index([user_id])
}

model settings {
  id                      Int       @id @default(autoincrement())
  created_by_id           Int?      @default(0)
  latest_updated_by_id    Int?
  created_on              DateTime?
  latest_updated_on       DateTime?
  delete_flag             Boolean   @default(false)
  user_id                 Int       @unique
  unit_per_gram           Decimal   @default(0.2) @db.Decimal(65, 2)
  destination_id          Int?
  display_shipment_weight Boolean   @default(true)
  display_actual_received Boolean   @default(false)
  qr_scan_init            Int?
  receipt_number          Int?      @default(2)
  report_type             Int?      @default(2)
  enable_session_timeout  Boolean   @default(true)
  session_expirytime      Int       @default(24)
  include_tax_type        Int       @default(1)
  inventory_control_type  Int       @default(2)
  price_per_kilogram      Decimal[] @default(dbgenerated("(ARRAY[]::numeric[])::numeric(65,2)[]")) @db.Decimal
  price_per_quantity      Decimal[] @default(dbgenerated("(ARRAY[]::numeric[])::numeric(65,2)[]")) @db.Decimal
  user                    users     @relation(fields: [user_id], references: [id])
}

model users {
  id                          Int             @id @default(autoincrement())
  created_by_id               Int?            @default(0)
  latest_updated_by_id        Int?
  created_on                  DateTime?
  latest_updated_on           DateTime?
  delete_flag                 Boolean         @default(false)
  user_code                   String?         @unique @db.VarChar(256)
  qr_code                     String?         @db.VarChar(256)
  enterprise_id               Int?
  name                        String          @db.VarChar(256)
  name_kana                   String?         @db.VarChar(256)
  phone                       String          @db.VarChar(256)
  password                    String
  role                        String          @db.VarChar(2)
  statistics_date_from        DateTime?
  license_id                  Int?
  note_1                      String?         @db.VarChar(256)
  note_2                      String?         @db.VarChar(256)
  license_number              String?         @db.VarChar(256)
  name_nospace                String          @db.VarChar(256)
  name_kana_nospace           String?         @db.VarChar(256)
  province_custom_data        Json?
  status                      Int?
  province_id                 Int?
  locktime                    DateTime?
  loginfail                   Int             @default(0)
  enterprise_type             Int?
  staff_type                  Int?
  account_registration_status Int             @default(0)
  enable_export_function      Boolean         @default(false)
  enterprise                  enterprises?    @relation(fields: [enterprise_id], references: [id])
  license                     licenses?       @relation(fields: [license_id], references: [id])
  setting                     settings?
  readers                     readers[]
  the_origins_created         the_origins[]   @relation("UserCreated")
  the_origins_updated         the_origins[]   @relation("UserUpdated")
  the_origins_shipper         the_origins[]   @relation("UserShipper")
  the_origins_supplier        the_origins[]   @relation("UserSupplier")
  the_origins_history_created         the_origins_history[]   @relation("UserCreated")
  the_origins_history_updated         the_origins_history[]   @relation("UserUpdated")
  the_origins_history_shipper         the_origins_history[]   @relation("UserShipper")
  the_origins_history_supplier        the_origins_history[]   @relation("UserSupplier")
  notifications               notifications[] @relation("CreatorNotification")
  partners                    partners[]      @relation("fk_pa_from")
  partnered                   partners[]      @relation("fk_pa_to")
  provinces                   provinces?      @relation(fields: [province_id], references: [id])

  @@index([enterprise_id])
  @@index([license_id])
  @@index([name_kana_nospace])
  @@index([name_nospace])
  @@index([phone])
  @@index([status])
}

model sys_settings {
  id                   Int       @id @default(autoincrement())
  created_by_id        Int?      @default(0)
  latest_updated_by_id Int?
  created_on           DateTime
  latest_updated_on    DateTime?
  delete_flag          Boolean   @default(false)
  setting_name         String    @unique @db.VarChar(256)
  setting_value        String?   @db.VarChar(256)
}

model provinces {
  id                   Int       @id @default(autoincrement())
  name                 String?   @db.VarChar(300)
  setting              Json
  created_by_id        Int?      @default(0)
  latest_updated_by_id Int?
  created_on           DateTime?
  latest_updated_on    DateTime?
  delete_flag          Boolean   @default(false)
  region_id            Int
  region               regions   @relation(fields: [region_id], references: [id])
  user                 users[]

  @@index([region_id])
}

model regions {
  id                   Int         @id @default(autoincrement())
  region_name          String      @unique
  created_by_id        Int         @default(0)
  latest_updated_by_id Int?
  created_on           DateTime?
  latest_updated_on    DateTime?
  delete_flag          Boolean     @default(false)
  province             provinces[]
}

model catch_records {
  id                            Int     @id @default(autoincrement())
  catch_date                    String
  destination_user_id           Int
  destination_enterprise_name   String  @db.VarChar(256)
  destination_user_name         String  @db.VarChar(256)
  destination_license_number    String  @db.VarChar(256)
  destination_user_note_1       String  @db.VarChar(256)
  destination_user_note_2       String  @db.VarChar(256)
  starting_user_id              Int
  starting_enterprise_name      String  @db.VarChar(256)
  starting_user_name            String  @db.VarChar(256)
  starting_license_number       String  @db.VarChar(256)
  starting_user_note_1          String  @db.VarChar(256)
  starting_user_note_2          String  @db.VarChar(256)
  catch_weight                  Decimal @db.Decimal(65, 2)
  operating_days                Int
}

model code_suffixes_alloc {
  id                        Int      @id @default(autoincrement())
  code_group_key            String   @db.VarChar(13)
  code_suffix               String   @db.VarChar(3)
  alloc_status              Int      @default(0)
  created_by_id             Int      @default(0)
  latest_updated_by_id      Int?
  created_on                DateTime
  latest_updated_on         DateTime?

  @@index([code_group_key])
  @@index([code_suffix])
}

model the_origins_history {
  id                             Int       @id @default(autoincrement())
  the_origins_history_type       Int?
  the_origins_id                 Int      @default(0)
  the_origins_history_created_by_id Int   @default(0)
  the_origins_history_created_on DateTime
  code                           String   @db.Text
  shipping_date                  DateTime?
  shipping_gross_weight          Decimal?  @db.Decimal(65, 2)
  shipping_tare_weight           Decimal?  @db.Decimal(65, 2)
  shipping_net_weight            Decimal?  @db.Decimal(65, 2)
  shipping_quantity              Int?
  arrival_gross_weight           Decimal?  @db.Decimal(65, 2)
  arrival_tare_weight            Decimal?  @db.Decimal(65, 2)
  arrival_net_weight             Decimal?  @db.Decimal(65, 2)
  arrival_quantity               Int?
  arrival_date                   DateTime?
  type_diff                      Int?
  shipping_type                  Int?
  reason_diff                    String?   @db.VarChar(300)
  created_by_id                  Int      @default(0)
  latest_updated_by_id           Int?
  created_on                     DateTime
  latest_updated_on              DateTime?
  delete_flag                    Boolean   @default(false)
  qr_code                        String?   @db.VarChar(256)
  destination_enterprise_id      Int?
  starting_enterprise_id         Int?
  inventory_id                   Int?
  ingredient                     Json?
  setting                        Json?
  destination_user_id            Int?
  starting_user_id               Int?
  shipping_type_diff             Int?
  shipping_reason_diff           String?   @db.VarChar(300)
  operating_days                 Int?      @default(0)
  destination_enterprise_name    String   @db.VarChar(256)
  destination_user_name          String   @db.VarChar(256)
  destination_license_number     String?   @db.VarChar(256)
  destination_user_note_1        String?   @db.VarChar(256)
  destination_user_note_2        String?   @db.VarChar(256)
  starting_enterprise_name       String   @db.VarChar(256)
  starting_user_name             String   @db.VarChar(256)
  starting_license_number        String?   @db.VarChar(256)
  starting_user_note_1           String?   @db.VarChar(256)
  starting_user_note_2           String?   @db.VarChar(256)
  shipping_inventory_type        Int       @default(0)
  shipping_id_list               Json      @default("[]")

  the_origins               the_origins    @relation(fields: [the_origins_id], references: [id])
  destination_enterprise    enterprises? @relation("DestinationEnterprise", fields: [destination_enterprise_id], references: [id])
  starting_enterprise       enterprises? @relation("StartingEnterprise", fields: [starting_enterprise_id], references: [id])
  inventory                 inventories? @relation(fields: [inventory_id], references: [id])
  user_create               users?       @relation("UserCreated", fields: [created_by_id], references: [id])
  user_update               users?       @relation("UserUpdated", fields: [latest_updated_by_id], references: [id])
  destination_user          users?       @relation("UserShipper", fields: [destination_user_id], references: [id])
  starting_user             users?       @relation("UserSupplier", fields: [starting_user_id], references: [id])

  @@index([the_origins_id])
  @@index([destination_enterprise_id])
  @@index([starting_enterprise_id])
  @@index([inventory_id])
  @@index([destination_user_id])
  @@index([starting_user_id])
  @@index([created_by_id])
  @@index([latest_updated_by_id])
  @@index([the_origins_history_created_by_id])
}