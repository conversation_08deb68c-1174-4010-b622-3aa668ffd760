import { Notify } from 'quasar';

class ToastUtil {
  // for warning notification
  warning(message) {
    Notify.create({
      type: 'warning',
      message,
    });
  }

  // for error notification
  error(message) {
    Notify.create({
      type: 'negative',
      message,
      color: 'red',
      classes: 'tw:text-[2rem] tw:tl:text-[1.53rem] tw:py-2',
    });
  }

  // for success notification
  access(message) {
    Notify.create({
      message,
      icon: 'error',
      iconColor: 'blue-4',
      textColor: 'blue-4',
      iconSize: '40px',
      classes: 'tw:text-[2rem] tw:tl:text-[1.53rem] tw:py-2',
      color: 'blue-5',
    });
  }

  // for delete/cancel action success notification
  cancel(message) {
    Notify.create({
      message,
      icon: 'error',
      iconColor: 'red-1',
      textColor: 'red-1',
      iconSize: '40px',
      classes: 'tw:text-[1rem] tl:tw:text-[1.53rem] tw:py-2',
      color: 'blue-3',
    });
  }

  exception(error) {
    if (error instanceof Error) {
      if (error.code === 'ECONNABORTED') {
        // timeout
        this.error(
          'ただいま、サーバーが混み合っております。しばらく経ってからもう一度お試しください'
        );
      } else if (error.code === 'API_ERROR') {
        // Api Error
        this.error(
          'サーバエラーが発生しました。システム担当者にお問合せください'
        );
      } else {
        // default
        this.error(
          'ネットワークが接続されていません。ネットワーク接続を確認後、もう一度を試しください'
        );
      }
    } else {
      // ServiceErrorResponse
      this.error(
        'ネットワークが接続されていません。ネットワーク接続を確認後、もう一度を試しください'
      );
    }
  }
}

export default new ToastUtil();
