<template>
  <PopupConfirmText />
  <div class="tw:mb-4 tw:mt-2">
    <q-card class="tw:mb-4 tw:bg-white tw:p-4 tw:min-h-[calc(100vh-15rem)] tw:pb-[10rem]">
      <div class="tw:text-l-design tw:font-[700] tw:mb-5">在庫管理詳細</div>
      <div class="tw:border tw:border-[#D2D2D2] tw:rounded-none">
        <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#D2D2D2]">
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex"
            >
              グループ名
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{
                inventoryDetail?.group_name ||
                inventoryDetail?.fallback_group_name
              }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1
              tw:font-[400] tw:text-m-design tw:items-center tw:flex"
            >
              漁獲/荷口番号：仕入先
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design
              tw:max-h-[16rem] tw:overflow-y-auto"
            >
              <div
                v-for="item in inventoryDetail?.the_origins || []"
                :key="item.id"
              >
                {{ maskCodeString(item.code) }}：{{
                  `${item?.starting_enterprise_name}`
                }}
              </div>
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex"
            >
              総入荷量
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ FORMAT_NUMBER(inventoryDetail?.net_weight_total) ? `${FORMAT_NUMBER(inventoryDetail?.net_weight_total)}g` : '' }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex"
            >
              総在庫量
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ FORMAT_NUMBER(inventoryDetail?.net_weight_inventory) ? `${FORMAT_NUMBER(inventoryDetail?.net_weight_inventory)}g` : '' }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:min-h-[4.25rem] tw:bg-[#E2E3EA] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design tw:items-center tw:flex"
            >
              最新の入荷日
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:pl-5 tw:pt-1 tw:font-[400] tw:text-m-design"
            >
              {{ FORMAT_DATE(inventoryDetail?.latest_arrival_date) }}
            </div>
          </div>
        </div>
      </div>
    </q-card>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
        label="在庫管理に戻る"
        @click.prevent="goToPage('inventoryList')"
      />
      <BaseButton
        v-if="user?.status === USER_STATUS_ENUM.ACTIVE"
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[16.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`" label="修正する" @click.prevent="handleClickEdit" />
    </q-footer>
  </div>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { onMounted, ref, provide } from 'vue';
import { useRouter } from 'vue-router';
import inventoryManagementService from 'services/inventoryManagement.service';
import { maskCodeString, FORMAT_DATE, FORMAT_NUMBER } from 'helpers/common';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import MESSAGE from 'helpers/message';
import toast from 'utilities/toast';
import BaseButton from 'components/base/vs/BaseButton.vue';
import { USER_STATUS_ENUM } from 'src/helpers/constants';
import { useAuthStore } from 'src/stores/auth-store';

// #region state
const router = useRouter();
const inventoryDetail = ref(null);
const isPopupConformTextPopup = ref(false);
// #endregion

const { user } = storeToRefs(useAuthStore());
//  #region actions
const handleClickEdit = async () => {
  await router.push({
    name: 'inventoryEdit',
    params: { id: inventoryDetail.value.id },
  });
};

const goToPage = name => {
  router.push({ name });
};
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  const inventoryId = router.currentRoute.value?.params?.id;
  if (!inventoryId) {
    router.push({ name: 'inventoryList' });
  }

  const inventoryDetailResponse =
    await inventoryManagementService.getInventoryDetail(inventoryId);
  if (inventoryDetailResponse.code === 401) {
    return;
  }
  if (inventoryDetailResponse.code === 0) {
    if (
      !inventoryDetailResponse.payload?.net_weight_inventory
    ) {
      router.back();
    }
    inventoryDetail.value = inventoryDetailResponse.payload;
  } else {
    router.push({ name: 'inventoryList' });
  }
});
// #endregion

// #region provide
const popupConfirmTextProvideData = {
  isPopup: isPopupConformTextPopup,
  titlePopup: '確認',
  caption: 'この在庫をリセットします。よろしいですか？',
  handleCloseModal: () => {
    isPopupConformTextPopup.value = false;
  },
  handleAcceptModal: async () => {
    isPopupConformTextPopup.value = false;
    const result = await inventoryManagementService.resetInventory(
      inventoryDetail.value?.id
    );
    if (result.code !== 401) {
      if (result.code !== 0) {
        toast.error(result.message);
        router.push({ name: 'inventoryList' });
      } else {
        toast.access(MESSAGE.MSG_RESET_STOCK_INFO);
        router.push({ name: 'inventoryList' });
      }
    }
  },
};
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
// #endregion
</script>
