import UserRepository from 'services/repositories/restful/user.repository';
import LocatedTransactionRepository from 'services/repositories/restful/locatedTransaction.repository';
import ArrivalRepository from 'services/repositories/restful/arrival.repository';
import ProfileRepository from 'services/repositories/restful/profile.repository';
import CommonRepository from 'services/repositories/restful/common.repository';
import SettingRepository from 'services/repositories/restful/setting.repository';
import AuthRepository from 'services/repositories/restful/auth.repository';
import ShippingRepository from 'services/repositories/restful/shipping.repository';
import OutboundShipmentRepository from 'services/repositories/restful/outboundShipment.repository';
import StaffRepository from 'services/repositories/restful/staff.repository';
import InventoryManagementRepository from 'services/repositories/restful/inventoryManagement.repository';
import NotificationRepository from 'services/repositories/restful/notification.repository';
import AdminSystemSettingsRepository from 'services/repositories/restful/admin/systemSettings.admin.repository';
import AdminUserRepository from 'services/repositories/restful/admin/user.admin.repository';
import FAExportShipmentRepository from 'services/repositories/restful/fa/export.fa.repository';

import regionsRepository from './restful/regions.repository';

class RepositoryFactory {
  getRepository(serviceName) {
    switch (serviceName) {
    case 'common':
      return CommonRepository;
    case 'user':
      return UserRepository;
    case 'outbound-shipment':
      return OutboundShipmentRepository;
    case 'located-transaction':
      return LocatedTransactionRepository;
    case 'arrival':
      return ArrivalRepository;
    case 'profile':
      return ProfileRepository;
    case 'setting':
      return SettingRepository;
    case 'auth':
      return AuthRepository;
    case 'shipping':
      return ShippingRepository;
    case 'staff':
      return StaffRepository;
    case 'inventory-management':
      return InventoryManagementRepository;
    case 'notification':
      return NotificationRepository;
    case 'admin-system-settings':
      return AdminSystemSettingsRepository;
    case 'fa-export-shipment':
      return FAExportShipmentRepository;
    case 'admin-user':
      return AdminUserRepository;
    case 'regions':
      return regionsRepository;
    default:
      throw Error('Invalid param');
    }
  }
}

export default new RepositoryFactory();
