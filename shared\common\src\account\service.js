const BaseService = require('../base/serviceFn');
const {
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  USER_STATUS_ENUM,
  PROVINCES_ENUM,
  STAFF_TYPE_ENUM } = require('../helpers/enum');
const { ControlledException } = require('../base/errors');
const { hash } = require('../utils/crypto');
const { MESSAGE } = require('../utils/message')
const dayjs = require('../boot/dayjs');

class AccountService extends BaseService {

  // ====== 1. Private method ==========

  // Remove space of userID and convert to half-size
  _convertToHalfWidthAndRemoveSpaces(userId) {
    // Remove all spaces in string
    const noSpaces = userId.toString().replace(/\s+/g, '');

    // Convert characters from full-width to half-width
    const convertToHalfWidth = (text) => {
      return text.replace(/[\uFF01-\uFF60\uFFE0-\uFFE6]/g, (match) => {
        return String.fromCharCode(match.charCodeAt(0) - 0xFEE0);
      }).replace(/[\u3000]/g, ' ');
    };

    return convertToHalfWidth(noSpaces);
  }

  // Make token
  _makeTokenJWT(user) {
    const token = this.FASTIFY.jwt.sign({ ...user, datetime: Date.now() });
    return token;
  }

  // Register user
  async _registerUser(tx, data) {
    const { enterprisesID, mobileNumber, password, enterpriseName,
      enterpriseNameKana, statisticsDateFrom, qrScanInit, licenseCode, licenseId, dateNow, typeEnterprises } = data;

    // generate password
    const hashPassword = await hash(password);
    // Add user
    const account = await tx.users.create({
      data: {
        enterprise_id: enterprisesID,
        phone: mobileNumber,
        password: hashPassword,
        delete_flag: false,
        role: ROLES_ENUM.NORMAL_USER,
        enterprise_type: typeEnterprises,
        staff_type: typeEnterprises === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE ? null : STAFF_TYPE_ENUM.ENTERPRISE,
        name: enterpriseName,
        name_kana: enterpriseNameKana,
        statistics_date_from: statisticsDateFrom,
        name_nospace: enterpriseName.replace(/\s+/g, ''),
        name_kana_nospace: enterpriseNameKana?.replace(/\s+/g, '') || null,
        license_id: licenseId,
        status: USER_STATUS_ENUM.ACTIVE,
        province_id: PROVINCES_ENUM.NONE,
        latest_updated_on: dateNow,
      },
    });

    // Update license
    await tx.licenses.update({
      where: {
        license_code: licenseCode
      },
      data: {
        user_id: account.id,
        latest_updated_on: dateNow,
      },
    })

    //Add settings
    await tx.settings.create({
      data: {
        user_id: account.id,
        price_per_kilogram: [200],
        delete_flag: false,
        qr_scan_init: qrScanInit,
        price_per_quantity: [400],
        latest_updated_on: dateNow,
      }
    });
    delete account.password;
    delete account.created_by_id;
    delete account.created_on;
    delete account.delete_flag;
    delete account.latest_updated_by_id;
    delete account.latest_updated_on;
    delete account.license_number;
    delete account.note_1;
    delete account.note_2;
    delete account.phone;
    delete account.qr_code;
    delete account.statistics_date_from;
    return account;
  }
  // ====== 2. Public method ==========

  // Create new account
  async create(accountInfo) {
    const { enterpriseName, enterpriseCode, licenseCode,
      enterpriseNameKana, mobileNumber, password } = accountInfo;

    const connectWrite = this.DB.WRITE;
    const connectRead = this.DB.READ;

    const checkLicenses = await this.DB.READ.licenses.findFirst({
      where: {
        license_code: licenseCode,
        delete_flag: false
      },
    });

    // Not found license
    if (!checkLicenses) {
      throw new ControlledException(MESSAGE.MSG_INVALID_QRCODE_ERROR, {}, 401);
    }

    // Connect the role to the enterprise type
    let typeEnterprises = '';
    const dateTimeNow = dayjs().toDate();
    let statisticsDateFrom = null;
    let qrScanInit = null;

    if (enterpriseCode.startsWith("0")) {
      typeEnterprises = ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE;
    } else if (enterpriseCode.startsWith("5")) {
      typeEnterprises = ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE;
      statisticsDateFrom = dateTimeNow;
      qrScanInit = SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA;

    } else {
      typeEnterprises = ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE;
      qrScanInit = SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA;
    }

    // Check exits Phone
    const checkExistPhone = await connectRead.users.findFirst({
      where: {
        phone: mobileNumber,
      }
    });

    if (mobileNumber && checkExistPhone) {
      throw new ControlledException(MESSAGE.MSG_USED_FHONENUMBER_ERROR);
    }

    const checkExistEnterPrise = await connectRead.enterprises.findFirst({
      where: {
        enterprise_code: enterpriseCode.toString(),
      }
    });
    let enterprisesID = checkExistEnterPrise?.id;
    if (checkExistEnterPrise) {
      // Check enterprise id in users
      const checkExistEnterPriseId = await connectRead.users.findFirst({
        where: {
          enterprise_id: checkExistEnterPrise.id,
        }
      });
      // Error EnterPrise Id already exists in user
      if (checkExistEnterPriseId) {
        throw new ControlledException(MESSAGE.MSG_USED_NOTIFICATIONUMBER_ERROR);
      }
    }

    let response = {};
    let account;
    await connectWrite.$transaction(async (tx) => {
      // Check and add enterprise
      if (!checkExistEnterPrise) {
        const addEnterprises = await tx.enterprises.create({
          data: {
            enterprise_name: enterpriseName,
            enterprise_code: enterpriseCode.toString(),
            enterprise_name_kana: enterpriseNameKana || null,
            enterprise_name_nospace: enterpriseName.replace(/\s+/g, ''),
            enterprise_name_kana_nospace: enterpriseNameKana?.replace(/\s+/g, '') || null,
            type: typeEnterprises,
            latest_updated_on: dateTimeNow,
          }
        });
        enterprisesID = addEnterprises.id;
      } else {
        // Update enterPrise
        await tx.enterprises.update({
          where: {
            id: enterprisesID
          },
          data: {
            enterprise_name: enterpriseName,
            enterprise_code: enterpriseCode.toString(),
            enterprise_name_kana: enterpriseNameKana || null,
            enterprise_name_nospace: enterpriseName.replace(/\s+/g, ''),
            enterprise_name_kana_nospace: enterpriseNameKana?.replace(/\s+/g, '') || null,
            type: typeEnterprises,
            latest_updated_on: dateTimeNow,
          },
        })
      }

      account = await this._registerUser(tx, {
        enterprisesID, mobileNumber, password, enterpriseName,
        enterpriseNameKana, statisticsDateFrom, qrScanInit, licenseCode, licenseId: checkLicenses.id, dateNow: dateTimeNow, typeEnterprises
      });
    });
    const newAccount = await this.DB.READ.users.findFirst({
      where: {
        id: account.id,
      },
      select: {
        id: true,
        user_code: true,
        password: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          }
        }
      },
    });
    const token = this._makeTokenJWT(newAccount);
    response = {
      ...newAccount,
      token: token
    }
    return this.SUCCESS(response);
  }

  // Check if userId is associated with license ID
  async checkUserId(userid) {
    // Remove space of userID and convert to half-size
    const convertToHalfWidth = this._convertToHalfWidthAndRemoveSpaces(userid)
    const userIdCovert = convertToHalfWidth;

    // Check userId
    const checkUserID = await this.DB.READ.users.findFirst({
      where: {
        user_code: userIdCovert,
        delete_flag: false
      },
      select: {
        id: true,
        status: true,
        enterprise: {
          select: {
            enterprise_code: true,
            enterprise_name: true,
            enterprise_name_kana: true,
            type: true,
          }
        }
      },
    });

    // Error message: userId not found or account is "suspended"
    if (!checkUserID || checkUserID.status !== USER_STATUS_ENUM.ACTIVE) {
      throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
    }
    return this.SUCCESS(checkUserID);
  }

  // Link to License ID
  async linkUserId(userid, licenseCode) {
    // Remove space of userID and convert to half-size
    const convertToHalfWidth = this._convertToHalfWidthAndRemoveSpaces(userid)
    const userIdCovert = convertToHalfWidth;

    // Check userId
    const checkUserID = await this.DB.READ.users.findFirst({
      where: {
        user_code: userIdCovert,
        delete_flag: false,
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
      }
    });

    // Error message: userId not found or account is "suspended"
    if (!checkUserID) {
      throw new ControlledException(MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR);
    }

    // Check licenses existence
    const checkLicenses = await this.DB.READ.licenses.findFirst({
      where: {
        license_code: licenseCode,
        user_id: null,
        delete_flag: false
      },
    });
    // Not found license
    if (!checkLicenses) {
      throw new ControlledException(MESSAGE.MSG_INVALID_QRCODE_ERROR);
    }

    // Check fishing time
    const expiryDate = checkLicenses.expiry_date;
    const extendDay = this.DB.READ.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD,
        delete_flag: false
      },
      select: {
        setting_value: true
      }
    })
    if (this.checkLicenseExpried(expiryDate, extendDay ? +extendDay.setting_value : undefined)) {
      throw new ControlledException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR, checkUserID);
    }

    await this.DB.WRITE.$transaction(async (tx) => {
      await tx.users.update({
        where: {
          id: checkUserID.id
        },
        data: {
          license_id: checkLicenses.id,
        },
      });
      // Update license
      await tx.licenses.update({
        where: {
          id: checkLicenses.id,
        },
        data: {
          user_id: checkUserID.id,
        },
      })
    });
    const account = await this.DB.READ.users.findFirst({
      where: {
        id: checkUserID.id,
        delete_flag: false,
      },
      select: {
        id: true,
        user_code: true,
        password: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          }
        }
      }
    });
    // const token = this._makeTokenJWT(account);
    // const response = {
    //   ...account,
    //   token: token
    // }
    return this.SUCCESS(account);
  }
}
module.exports = AccountService;