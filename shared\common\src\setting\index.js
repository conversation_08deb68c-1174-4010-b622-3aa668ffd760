// ======== IMPORT ========================
const { getSettingSchema, updateSettingSchema } = require('./schemas');
const { auth, apiKeyVerify } = require('../base/authorized');

// ===== 1. Injection ==============
// ===== 2. Router Functions ==============
async function getSettingHandler(request, reply) {
  const settingService = request.diScope.resolve('settingService');
  const response = await settingService.getSetting(request.user);
  return reply.send(response);
}

async function updateSettingHandler(request, reply) {
  const settingService = request.diScope.resolve('settingService');
  const response = await settingService.updateSetting(request.user, request.body);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/get-setting',
    {
      schema: getSettingSchema,
      onRequest: [apiKeyVerify, auth],
    },
    getSettingHandler
  );
  fastify.post(
    '/update-setting',
    {
      schema: updateSettingSchema,
      onRequest: [apiKeyVerify, auth],
    },
    updateSettingHandler
  );
};
