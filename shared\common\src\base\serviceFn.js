const { success, error } = require('../helpers/response');
const dayjs = require('../boot/dayjs');
const { EXTENT_EXPRIED_DATE } = require('../helpers/enum');

class BaseService {
  constructor({ FASTIFY, DB, currentUser }) {
    this.FASTIFY = FASTIFY;
    this.DB = DB;
    this.currentUser = currentUser;
  };

  SUCCESS(payload = null, message = '') {
    return success(payload, message);
  }

  ERROR(message, code = 999, payload = null) {
    return error(message, payload, code);
  }

  // Check license expried
  checkLicenseExpried(dateExpried, extendDay = EXTENT_EXPRIED_DATE) {
    const compareExpiryDate = dayjs.getDateFromJST(dateExpried)
      .add(extendDay, 'day')
      .format("YYYYMMDD HH:mm:ss") < dayjs.getDateFromJST().format("YYYYMMDD HH:mm:ss");
    return compareExpiryDate;
  }
}

module.exports = BaseService;