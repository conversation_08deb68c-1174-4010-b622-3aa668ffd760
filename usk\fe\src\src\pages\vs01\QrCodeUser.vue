<template>
  <div class="tw:p-6">
    <!-- title -->
    <div
      class="tw:text-2xl tw:tl:text-[2.3rem] tw:pb-2 tw:border-b tw:border-gray tw:font-bold"
    >
      許可証QRコード連携
    </div>
    <!-- content -->
    <div>
      <div class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]">
        以下にユーザーIDを入力してください。
      </div>
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        ユーザーID<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model.trim="userID"
        autocomplete="nope"
        lazy-rules
        maxlength="12"
        :error="!!errors.userid"
        :error-message="errors.userid"
      />
      <q-btn
        class="tw:w-full tw:mt-5 tw:text-base tw:tl:text-[1.53rem]
        tw:font-bold tw:text-white"
        label="許可証とユーザーIDを連携する"
        type="button"
        :class="`tw:bg-${colorSub}`"
        @click.prevent="confirmUser"
      />
      <div
      class="tw:text-blue-1 tw:cursor-pointer tw:mt-3"
      @click.prevent="goRegisterUser"
      >
          このシステムを利用したことがない場合はこちら
      </div>
    </div>
  </div>
  <PopupConfirmItems/>
</template>

<script setup>

import { ref, provide, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import PopupConfirmItems from 'components/PopupConfirmItems.vue';
import qrCodeUserSchema from 'schemas/qrCodeUser';
import UserService from 'services/user.service';
import { ENTERPRISE_TYPE_ENUM } from 'helpers/constants';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();
const { errors, validateData } = useValidate();

// ===== REF =====
const userID = ref('');
const formDataConfirm = ref([]);
const qrCode = ref(router.currentRoute.value.params.qrCode);
// ===== METHOD =====

// Change the name of the authorization
const changeRoleName = value => {
  let listRole = '';
  if (value === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    listRole = '採捕事業者';
  }
  if (value === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
    listRole = '取扱事業者';
  }
  if (value === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    listRole = '養鰻事業者';
  }
  return listRole;
};

const confirmFunc = async () => {
  // Verify userid and license id association
  const result = await UserService.linkUserId(formDataConfirm.value);
  if (result) {
    await router.push({
      name: 'loginUserId',
    });
  }
};

const popupConfirmItems = ref({
  isPopup: false,
  titlePopup: '以下の内容で登録します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 122,
  minWidthTlDefault: 172,
  minWidthDtDefault: 264,
});

const confirmUser = async () => {
  // validate
  const formData = {
    userid: userID.value,
    licenseCode: qrCode.value,
  };
  const valid = validateData(qrCodeUserSchema, formData);
  if (!valid) {
    return;
  }

  // Check userid
  const userInfo = await UserService.checkUserId(userID.value);
  popupConfirmItems.value.isPopup = true;
  popupConfirmItems.value.titlePopup = '以下のユーザーと連携します';
  popupConfirmItems.value.listItems = ([
    { key: 'ユーザーID', value: userID.value },
    { key: '届出番号', value: userInfo.enterprise?.enterprise_code },
    { key: '事業者名', value: userInfo.enterprise?.enterprise_name },
    { key: '事業者名（カナ）', value: userInfo.enterprise?.enterprise_name_kana },
    { key: '事業者/従事者区分', value: changeRoleName(userInfo.enterprise?.type) },
  ]);
  formDataConfirm.value = formData;
};

const goRegisterUser = async () => {
  await router.push({
    name: 'registerUser',
    params: {
      licenseCode: qrCode.value,
    },
  });
};
provide('popupConfirmItems', popupConfirmItems);

onMounted(() => {
  if (!router.options.history.state?.back.startsWith('/login')) {
    router.push({
      name: 'loginQrScan',
    });
  }
});

</script>
