<template>
  <div class="tw:flex tw:justify-center">
    <div class="tw:w-[40%] tw:min-w-[550px]">
      <!-- qr_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          ログイン用QRコード
        </span>
        <div class="tw:font-[400] tw:text-base tw:bg-qcard
        tw:p-4">
        <img
          alt=""
          id="shipment-qr-code"
          :src="qrCode?.value"
          spinner-color="white"
          class="tw:w-full tw:max-w-[240px] tw:mx-auto"
        />
        </div>
      </div>
      <!-- province -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          都道府県
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showProvinceName(detailUser.province_id) }}
        </div>
      </div>
      <!-- staff type -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          事業者/従事者区分
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showRole(detailUser) }}
        </div>
      </div>
      <!-- enterprise_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          届出番号
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ detailUser.enterprise?.enterprise_code }}
        </div>
      </div>
      <!-- user_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          ユーザーID
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ detailUser.user_code }}
        </div>
      </div>
      <!-- enterprise_name -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          事業者名
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          {{ detailUser.name }}
        </div>
      </div>
      <!-- enterprise_name_kana -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          事業者名（カナ）
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          {{ detailUser.name_kana }}
        </div>
      </div>
      <!-- license_number -->
      <div class="tw:flex tw:flex-col tw:my-4"
        v-if="detailUser.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
        detailUser.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE"
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          許可番号
        </span>
        <div
        class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          {{ detailUser.license_number }}
        </div>
      </div>
      <!-- phone -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          連絡先等
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ detailUser.phone }}
        </div>
      </div>
      <!-- expiry_date -->
      <div class="tw:flex tw:flex-col tw:my-4"
        v-if="detailUser.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
        (detailUser.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
        && detailUser?.license?.expiry_date_start && detailUser?.license?.expiry_date)"
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          採捕許可有効期間
        </span>
        <div
        class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
        {{ FORMAT_DATE(detailUser?.license?.expiry_date_start) }}
          <span v-if="detailUser?.license?.expiry_date_start
          && detailUser?.license?.expiry_date">~</span>
        {{ FORMAT_DATE(detailUser?.license?.expiry_date) }}
        </div>
      </div>
      <!-- status user -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          状態
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showStatus(detailUser.status) }}
        </div>
      </div>
      <!-- status register -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          アカウント登録状況
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showStatusRegister(detailUser.account_registration_status) }}
        </div>
      </div>
      <!-- temporary password -->
      <div v-if="detailUser.account_registration_status === USER_STATUS_REGISTER_ENUM.TEMPORARY" class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          初期パスワード
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14 tw:break-words">
          {{ detailUser.password }}
        </div>
      </div>
      <!-- official password -->
      <div v-if="detailUser.account_registration_status === USER_STATUS_REGISTER_ENUM.OFFICIAL" class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          パスワード
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          **********
        </div>
      </div>
      <!-- note 1 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          備考1
        </span>
        <div
        class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          <span>
            {{ detailUser.note_1 }}
          </span>
        </div>
      </div>
      <!-- note 2 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          備考2
        </span>
        <div
        class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          <span>
            {{ detailUser.note_2 }}
          </span>
        </div>
      </div>
      <!-- enable export function -->
      <div v-if="detailUser.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
      && detailUser.staff_type === STAFF_TYPE_ENUM.ENTERPRISE" class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          輸出機能
        </span>
        <div
        class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          <span>
            {{ showEnableExport(detailUser.enable_export_function) }}
          </span>
        </div>
      </div>
      <!-- customization_item -->
      <div v-if="detailUser.province_custom_data &&
      detailUser.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE">
        <div class="tw:flex tw:flex-col tw:my-4"
        v-for="item in Object.keys(detailUser.province_custom_data)" :key="item">
          <span class="tw:font-[400] tw:text-base tw:pb-1">
            {{ item }}
          </span>
          <div
          class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
            <span class="tw:whitespace-pre-line">
              {{ detailUser.province_custom_data[item]?.replaceAll(';','\n') }}
            </span>
          </div>
        </div>
      </div>
      <!-- #endregion end customization_item -->
      <!-- button -->
      <div class="tw:flex tw:gap-3 tw:justify-center">
        <BaseButton
          outline
          padding="0.75rem"
          :class="`tw:bg-white tw:text-${colorMain} tw:w-40 tw:text-[1rem]`"
          label="戻る"
          @click.prevent="goBack"
        />
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-40 tw:text-[1rem]`"
          label="修正する"
          @click.prevent="editUser"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
// #region import
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQRCode } from '@vueuse/integrations/useQRCode';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import {
  showStatus, showRole, FORMAT_DATE,
  showStatusRegister,
  showEnableExport,
} from 'src/helpers/common';
import { ENTERPRISE_TYPE_ENUM, ROLES_ENUM, STAFF_TYPE_ENUM, USER_STATUS_ENUM, USER_STATUS_REGISTER_ENUM } from 'src/helpers/constants';
import authService from 'src/shared/services/auth.service';
import BaseButton from 'src/components/base/BaseButton.vue';
import regionsService from 'src/shared/services/regions.service';
// #endregion import

const router = useRouter();
const { colorMain, colorSub, role } = storeToRefs(useAppStore());

const userId = ref('');
const detailUser = ref({});
const qrCode = ref();
const listProvince = ref();

const goBack = () => {
  router.push({
    name: 'adminUserManager',
    query: JSON.parse(localStorage.getItem('adminUserListQuery')),
  });
};

const editUser = () => {
  router.push({
    name: 'adminUserEdit',
    params: {
      id: userId.value,
    },
  });
};

const showProvinceName = provinceId => {
  if (!listProvince.value || !Array.isArray(listProvince.value)) {return '';}
  const findProvince = listProvince.value.find(p => p.value === +provinceId);
  return findProvince ? findProvince.label : '';
};

// onMounted
onMounted(async () => {
  userId.value = router.currentRoute.value.params?.id;
  const listRegions = await regionsService.getRegionWithProvinces();
  listProvince.value = listRegions.payload.items.flatMap(item =>
  item.province.map(province => ({
    label: province.name,
    value: province.id,
  })));

  const result = await userAdminService.getUserDetail(userId.value);
  if (result) {
    detailUser.value = result;
    qrCode.value = useQRCode(`${process.env.SITE_URL}/login/${detailUser.value?.license?.license_code}`);
  }

});

</script>
