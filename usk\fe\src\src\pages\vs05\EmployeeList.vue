<template>
  <q-page class="h-full tw:space-y-4 tw:pb-[7rem]">
    <!-- filter start -->

    <ExpansionSearchItem v-model:expanded="isSearchExpanded">
      <SearchForm />
    </ExpansionSearchItem>

    <div
      v-if="
        searchConditions.name ||
        searchConditions.licenseNumber ||
        searchConditions.note1 ||
        searchConditions.note2
      "
      class="tw:mb-2 tw:text-xs-design tw:flex-wrap tw:flex tw:items-center tw:space-x-2"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9] tw:mt-2"
        >検索条件</span
      >
      <span
        v-if="searchConditions.name"
        class="tw:text-xs-design tw:ml-[1rem] tw:max-w-[70vw] tw:truncate tw:p-2 tw:rounded tw:bg-white tw:mt-2"
      >
        {{ searchConditions.name }}
      </span>
      <span
        v-if="searchConditions.licenseNumber"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:max-w-[70vw] tw:truncate tw:mt-2"
      >
        {{ searchConditions.licenseNumber }}
      </span>
      <span
        v-if="searchConditions.note1"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:max-w-[70vw] tw:truncate tw:mt-2"
      >
        {{ searchConditions.note1 }}
      </span>
      <span
        v-if="searchConditions.note2"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:max-w-[70vw] tw:truncate tw:mt-2"
      >
        {{ searchConditions.note2 }}
      </span>
    </div>
    <q-card class="tw:p-4 tw:mt-8 tw:mb-[5rem] tw:bg-white">
      <!-- filter end -->
      <!-- sort btn start -->
      <div
        class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:border-neutral-500 tw:pt-1"
      >
        <div
          class="tw:flex tw:tl:flex-row tw:flex-col tw:items-start tw:space-y-4"
        >
          <div
            class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]"
          >
            <span class="tw:text-xs-design">表示件数</span>
            <PageSizeDropdownSP v-model="pageSize" />
          </div>
          <SortByDropdownSP v-model="sortModel"
            class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden tw:mb-[1rem] tw:tl:mb-0"
          />
        </div>
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0"
        >
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block q-py-sm">
        <q-table
          class="tw:border tw:border-[#D2D2D2] tw:border-collapse"
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="paginationComputed"
        >
          <template v-slot:header="props">
            <q-tr
              :props="props"
              class="tw:text-s-design tw:border tw:border-[#D2D2D2]"
              :class="`tw:bg-[#E2E3EA]`"
            >
              <q-th
                @click="handleClickSort(props.cols[0].name)"
                class="tw:font-bold tw:text-s-design tw:text-left tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[0].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:font-bold tw:text-s-design tw:text-start"
              >
                {{ props.cols[1].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr class="tw:cursor-pointer tw:w-full" :props="props">
              <q-td
                key="name"
                :props="props"
                @click.prevent="employeeDetail(_, props.row)"
                class="text-left tw:border-r tw:font-bold tw:border-[#D2D2D2] tw:text-s-design"
              >
                <div
                  :class="`tw:truncate tw:max-w-[40vw] tw:underline tw:underline-offset-1 tw:text-[#004AB9]`"
                >
                  {{ props.row?.name }}
                </div>
              </q-td>
              <q-td
                key="license_number"
                :props="props"
                class="text-center border-r border-l border-b border-td-color tw:text-s-design"
              >
                <div class="tw:truncate tw:max-w-[40vw]">
                  {{ FORMAT_LICENSE_NUMBER(props.row?.license_number) }}
                </div>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div
              class="tw:w-full tw:text-center tw:text-s-design"
            >
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <!-- start table for smartphone -->
      <div class="tw:block tw:tl:hidden q-py-sm">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-1"
          card-class="tw:w-full"
          :rows="employeeList"
          v-model:pagination="paginationComputed"
          row-key="id"
          hide-pagination
          hide-header
        >
          <template v-slot:item="props">
            <div
              class="tw:w-full"
              @click.prevent="employeeDetail(_, props.row)"
            >
              <q-card flat bordered>
                <q-card-section class="tw:text-left tw:cursor-pointer">
                  <div
                    class="tw:flex-1 tw:flex tw:items-center tw:justify-between"
                  >
                    <div class="tw:max-w-[80vw] tw:break-words">
                      <strong class="tw:text-s-design tw:text-[#004AB9]">
                        {{ props.row.name }}</strong
                      >
                      <br />
                      <div
                        class="tw:text-[#333333] tw:text-s-design tw:max-w-[80vw] tw:break-words"
                      >
                        <span>
                          {{
                            FORMAT_LICENSE_NUMBER(props.row?.license_number)
                          }}</span
                        >
                        <br />
                      </div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </template>
          <template v-slot:no-data="">
            <div :class="`tw:w-full tw:text-center tw:text-s-design`">
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <div
        class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0 tw:w-full"
      >
        <PaginationNotifi />
      </div>
    </q-card>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
      tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        dense
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
        label="トップに戻る"
        @click.prevent="goToPage('home')"
      />
    </q-footer>
  </q-page>
</template>

<script setup>
// import
import { computed, onMounted, ref, watch, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import staffService from 'services/staff.service';
import useValidate from 'composables/validate';
import searchAllStaff from 'schemas/searchAllStaff';
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import { isEqual, orderBy } from 'lodash';
import { useLocalStorage } from 'composables/localstorage';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import SortByDropdownSP from 'src/components/SortByDropdownSP.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { FORMAT_LICENSE_NUMBER } from 'src/helpers/common';

import SearchForm from './components/SearchForm.vue';

// const begin
const { listPageQueryParams } = useLocalStorage();
const router = useRouter();
const pagination = ref({
  sortBy: 'name',
  descending: false,
  page: listPageQueryParams.value?.employeeList?.page || 1,
  limit: listPageQueryParams.value?.employeeList?.rowsPerPage || 10,
});
const sortModel = ref({});
const { validateData, errors } = useValidate();
const totalPage = ref(1);
const pageSize = ref(router.currentRoute.value.query.limit || 10);
const pageIndex = ref(pagination.value.page || 1);
const sortBySelectedLabel = ref('従事者名順');
const employeeList = ref([]);
const isSearchExpanded = ref(false);
const route = useRoute();
const multiSortConditions = ref([]);
const isFirstLoad = ref(true);
const searchConditions = computed(() => {
  const query = route.query;
  return {
    name: query.name || '',
    licenseNumber: query.licenseNumber || '',
    note1: query.note1 || '',
    note2: query.note2 || '',
  };
});
const columns = [
  {
    name: 'name',
    label: '従事者名',
    align: 'left',
    field: 'name',
    sortable: true,
    classes: 'tw:w-[50%] tw:max-w-[50%]',
  },
  {
    name: 'license_number',
    label: '許可番号',
    align: 'left',
    field: 'license_number',
    sortable: true,
    classes: 'tw:w-[50%] tw:max-w-[50%]',
  },
];

const searchForm = ref({
  name: '',
  licenseNumber: '',
  note1: '',
  note2: '',
});
const searchFormData = ref({
  name: '',
  licenseNumber: '',
  note1: '',
  note2: '',
});

// #region computed
const paginationComputed = computed(() => ({
  page: pagination.value.page,
  rowsPerPage: pagination.value.limit,
}));

const sortedRows = computed(() => {
  const rows = [...employeeList.value].map(row => ({
    ...row,
    license_number: FORMAT_LICENSE_NUMBER(row.license_number),
  }));
  const lstKey = [];
  const lstOrder = [];
  for (const { key, order } of multiSortConditions.value) {
    lstKey.push(key);
    lstOrder.push(order);
  }
  return orderBy(rows, lstKey, lstOrder);
});

// #endregion

const employeeDetail = async (_, row) => {
  await router.push({
    name: 'employeeDetail',
    params: {
      id: row.id,
    },
  });
};

const goToPage = name => {
  router.push({ name });
};

const formatSearchFormToQuery = form => ({
  name: form.name || undefined,
  licenseNumber: form.licenseNumber || undefined,
  note1: form.note1 || undefined,
  note2: form.note2 || undefined,
});

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }
};

const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const getData = async () => {
  const searchCondition = {
    ...pagination.value,
    name: searchFormData.value.name || undefined,
    licenseNumber: searchFormData.value.licenseNumber || undefined,
    note1: searchFormData.value.note1 || undefined,
    note2: searchFormData.value.note2 || undefined,
    isMobile: 0,
  };

  const result = await staffService.getStaff(searchCondition);

  employeeList.value = result.payload.items;
  if (window.innerWidth < 960) {
    let key;
    const order = 'asc';
    switch (router.currentRoute.value.query.sortBy) {
      case 'name':
        key = 'name';
        break;
      case 'license_number':
        key = 'license_number';
        break;
      default:
        break;
    }

    employeeList.value = orderBy(employeeList.value, [key], [order]);

  }
  totalPage.value = Math.ceil(
    (result.payload.total_item ?? 1) / pagination.value.limit
  );

  if (+result.payload.page !== +pageIndex.value || totalPage.value < pageIndex.value) {
    router.replace({
      query: {
        ...pagination.value,
        page: result.payload.page,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  }
};

watch(pageSize, async () => {
  pagination.value.rowsPerPage = +pageSize.value;
  totalPage.value = Math.ceil(employeeList.value.length / +pageSize.value);
  listPageQueryParams.value.employeeList.rowsPerPage = +pageSize.value;
  pagination.value.limit = pageSize.value;
  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
  };
  if (!isFirstLoad.value) {
    query.page = 1;
  }
  router.push({
    query: {
      ...query,
    },
  });
});

watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.descending +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.limit +
    router.currentRoute.value.query.licenseNumber +
    router.currentRoute.value.query.name +
    router.currentRoute.value.query.note1 +
    router.currentRoute.value.query.note2 +
    pagination.value.sortBy,
  async () => {
    if (!listPageQueryParams.value.employeeList) {
      listPageQueryParams.value.employeeList = {};
    }

    if (router.currentRoute.value.query.limit) {
      listPageQueryParams.value.employeeList.limit =
        +router.currentRoute.value.query.limit;
    }

    if (router.currentRoute.value.query.page) {
      listPageQueryParams.value.employeeList.page =
        +router.currentRoute.value.query.page;
    }

    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;

    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        )?.label
      : '従事者名順';
    pagination.value = {
      ...pagination.value,
      page: +router.currentRoute.value.query.page,
      limit: +router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      name: router.currentRoute.value.query.name || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
    };
    searchFormData.value = {
      name: router.currentRoute.value.query.name || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
    };
    listPageQueryParams.value.employeeList = {
      ...listPageQueryParams.value.employeeList,
      ...searchFormData.value,
      sortBy: router.currentRoute.value.query.sortBy,
    };
    if (window.innerWidth < 960) {
      let key;
      const order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'name':
          key = 'name';
          break;
        case 'license_number':
          key = 'license_number';
          break;
        default:
          break;
      }

      employeeList.value = orderBy(employeeList.value, [key], [order]);
    }

    if (isFirstLoad.value) {
      await getData();
      isFirstLoad.value = false;
    }
  }
);

const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      name: '',
      licenseNumber: '',
      note1: '',
      note2: '',
    };
    errors.value = {};
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getData();
      this.isClose = true;
      return;
    }

    const validate = validateData(searchAllStaff, {
      ...searchForm.value,
    });

    if (!validate) {
      this.isClose = false;
      return;
    }
    pageIndex.value = 1;
    isFirstLoad.value = true;

    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchForm.value),
        page: 1,
      },
    });
    this.isClose = true;
  },
  isClose: true,
  title: '従事者をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: '従事者名順', value: 'name' },
    { label: '許可番号順', value: 'license_number' },
  ],
  sortBySelectedLabel,
  handleClickSortByItem: async option => {
    sortBySelectedLabel.value = option.label;
    pagination.value.sortBy = option.value;
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);

onMounted(async () => {
  if (!listPageQueryParams.value.employeeList) {
    listPageQueryParams.value.employeeList = {};
  }
  if (!router.currentRoute.value?.query?.page) {
    sortModel.value = {
      value: listPageQueryParams.value.employeeList.sortBy,
      label:
        sortByDropdownProvideData.sortByOptions.find(
          item => item.value === listPageQueryParams.value.employeeList.sortBy
        )?.label || '',
    };
    isFirstLoad.value = true;
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(
          Object.keys(listPageQueryParams.value.employeeList).length
            ? listPageQueryParams.value.employeeList
            : searchFormData.value
        ),
        sortBy: listPageQueryParams.value.employeeList.sortBy,
      },
    });
  } else {
    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;

    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        )?.label
      : '';
    sortModel.value = {
      value: router.currentRoute.value.query.sortBy,
      label: sortBySelectedLabel.value,
    };
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      name: router.currentRoute.value.query.name || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
    };
    searchFormData.value = {
      name: router.currentRoute.value.query.name || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
    };
    await getData();
    isFirstLoad.value = false;
  }
});
</script>
