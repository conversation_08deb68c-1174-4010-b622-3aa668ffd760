<template>
  <div class="tw:mx-10 tw:mt-4">
    <div class="tw:pb-4 tw:border-gray tw:border-b-2">
      <div class="tw:my-2">ユーザー設定CSVを指定してください。</div>
      <div class="tw:flex tw:gap-2">
        <q-input
          v-model="fileName"
          outlined
          disable
          class="tw:w-[40rem]"
        />
        <input
          class="tw:hidden"
          accept=".csv"
          type="file"
          ref="fileInput"
          @change="onUploadFile"
        />
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
          label="ファイルを指定する"
          @click.prevent="triggerFileInput"
        />
      </div>
      <div class="tw:my-2">
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-60 tw:text-[1rem]`"
          label="指定したファイルを取り込む"
          :disable="!fileName"
          @click.prevent="importCsvHandler"
        />
      </div>
    </div>
    <div v-if="isLoading || isLoadingManual">
      <div>[ファイル名]のフォーマットが正しいことを確認しています。</div>
      <div>ファイルフォーマットの確認が終わるまで、お待ちください。</div>
      <div>ファイルフォーマットに問題ないことを確認したのちに、ファイルの取込処理まで実施します。</div>
      <div>この処理は数分かかる場合があります。</div>
    </div>
    <div v-if="isError">
      <q-table
        row-key="index"
        class="my-sticky-virtscroll-table tw:py-3"
        :rows="errors['']"
        hide-pagination
        virtual-scroll
        style="max-height: 600px; height: auto"
        :rows-per-page-options="[20000]"
      >
        <template v-slot:header>
          <q-tr class="tw:text-[white]" :class="`tw:bg-${colorMain}`">
            <q-th
              class="tw:font-normal tw:text-left tw:w-24 tw:text-base"
            >
              行数
            </q-th>
            <q-th
              class="tw:font-normal tw:text-left tw:w-48 tw:text-base"
            >
              エラー対象列
            </q-th>
            <q-th
              class="tw:font-normal tw:text-left tw:text-base"
            >
              エラー内容
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td
              key="line"
              class="border-r border-l border-b border-td-color tw:w-24"
            >
              {{ props.row.line }}
            </q-td>
            <q-td
              key="key"
              class="border-r border-l border-b border-td-color tw:w-48"
            >
              {{ props.row.key }}
            </q-td>
            <q-td
              key="message"
              class="border-r border-b border-td-color"
            >
              {{ props.row.message }}
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </div>
    <div>
      <div>{{FORMAT_NUMBER(dataDone.total)}}件のユーザー設定を、新規登録および更新しました。</div>
      <div>取り込みの結果は以下のとおりです。</div>
      <q-table
        row-key="index"
        :rows="[{}]"
        hide-pagination
        class="tw:py-3 tw:w-80"
      >
        <template v-slot:header>
          <q-tr class="tw:text-[white]" :class="`tw:bg-${colorMain}`">
            <q-th
              class="tw:font-normal tw:text-left tw:w-32 tw:text-base"
            >
              登録内容
            </q-th>
            <q-th
              class="tw:font-normal tw:text-left tw:w-32 tw:text-base"
            >
              件数
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body>
          <q-tr>
            <q-td
              key="key"
              class="border-r border-l border-b border-td-color tw:w-32"
            >
              新規登録
            </q-td>
            <q-td
              key="value"
              class="border-r border-l border-b border-td-color tw:w-32"
            >
              {{ FORMAT_NUMBER(dataDone.inserted_count) }}件
            </q-td>
          </q-tr>
          <q-tr>
            <q-td
              key="key"
              class="border-r border-l border-b border-td-color tw:w-32"
            >
              更新
            </q-td>
            <q-td
              key="value"
              class="border-r border-l border-b border-td-color tw:w-32"
            >
              {{ FORMAT_NUMBER(dataDone.updated_count) }}件
            </q-td>
          </q-tr>
        </template>
      </q-table>
      <div>
        <div>カードID、ユーザーIDおよび初期パスワードを更新したユーザー設定CSVがダウンロード可能になりました。</div>
        <div>「ユーザー設定CSVを出力」ボタンを押下し、更新後のファイルをダウンロードしてください。</div>
      </div>
      <!-- download -->
      <q-table
        row-key="index"
        :rows="dataExportList"
        :columns="columnExportList"
        hide-pagination
        class="tw:py-3"
        :rows-per-page-options="[10]"
      >
        <template v-slot:header>
          <q-tr class="tw:text-[white]" :class="`tw:bg-${colorMain}`">
            <q-th
              class="tw:font-normal tw:text-center tw:text-base"
            >
              指定ファイル名
            </q-th>
            <q-th
              class="tw:font-normal tw:text-center tw:w-48 tw:text-base"
            >
              取込開始日時
            </q-th>
            <q-th
              class="tw:font-normal tw:text-center tw:w-48 tw:text-base"
            >
              取込完了日時
            </q-th>
            <q-th
              class="tw:font-normal tw:text-center tw:w-48 tw:text-base"
            >
              新規登録
            </q-th>
            <q-th
              class="tw:font-normal tw:text-center tw:w-48 tw:text-base"
            >
              更新
            </q-th>
            <q-th
              class="tw:font-normal tw:text-center tw:w-[450px] tw:text-base"
            >
              取込ステータス
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td
              key="fileKey"
              :props="props"
              class="border-r border-l border-b border-td-color tw:text-left">
              <div class="tw:max-w-[400px] tw:break-all tw:whitespace-normal">
              {{ props.row.fileName ? props.row.fileName.split('/')[1]
                : props.row.fileKey.split('/')[1] }}</div>
            </q-td>
            <q-td
              key="dateInput"
              :props="props"
              class="border-r border-l border-b border-td-color tw:text-center tw:w-48"
            >
              {{ FORMAT_DATE_TIME_UTC(props.row.createDate) }}
            </q-td>
            <q-td
              key="dateEnd"
              :props="props"
              class="border-r border-l border-b border-td-color tw:text-center tw:w-48"
            >
              {{ ['SUCCESS', 'ERROR'].includes(props.row.status) ?
                FORMAT_DATE_TIME_UTC(props.row.datetimeprocess) : '' }}
            </q-td>
            <q-td
              key="inserted_count"
              :props="props"
              class="border-r border-l border-b border-td-color tw:text-center tw:w-48"
            >
              {{ FORMAT_NUMBER(props.row.inserted_count || 0) }}件
            </q-td>
            <q-td
              key="updated_count"
              :props="props"
              class="border-r border-l border-b border-td-color tw:text-center tw:w-48"
            >
              {{ FORMAT_NUMBER(props.row.updated_count || 0) }}件
            </q-td>
            <q-td
              key="button"
              :props="props"
              class="border-r border-l border-b border-td-color tw:text-center tw:max-w-[400px]
              tw:break-all"
            >
              <div v-if="props.row.status === 'SUCCESS'">
                <div v-if="props.row.changed_count > 0" class="tw-text-left tw-mb-2">
                  届出番号または氏名のいずれかに変更があったユーザーが
                  <span class="tw-font-bold">{{ FORMAT_NUMBER(props.row.changed_count) }}</span>人います。
                </div>
                <BaseButton
                  padding="0.75rem"
                  :class="`tw:bg-${colorSub} tw:text-[white] tw:w-48 tw:text-[1rem]`"
                  label="ユーザー設定CSVを出力"
                  @click.prevent="downloadCsv(props.row.fileKey)"
                />
              </div>
              <div v-else class="tw-text-left tw:max-w-[400px] tw:break-all tw:whitespace-pre-wrap
">
                {{ props.row.contents }}
              </div>
            </q-td>
          </q-tr>
        </template>
        <template v-slot:no-data="">
          <div class="tw:w-full tw:text-center tw:font-normal tw:text-base">データが見つかりません。</div>
        </template>
      </q-table>
      <div class="tw:mt-4">
        <div class="tw:flex tw:justify-center tw:gap-3">
          <BaseButton
            outline
            padding="0.75rem"
            :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
            label="一覧へ戻る"
            @click.prevent="goBack"
          />
          <BaseButton
            padding="0.75rem"
            :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
            label="更新"
            @click.prevent="reloadListDownload"
          />
      </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import papa from 'papaparse';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'src/stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'src/composables/validate';
import importUserSchema from 'src/schemas/admin/user/importUser';
import MESSAGE from 'src/helpers/message';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import {
  exportCSV, FORMAT_DATE_TIME_UTC, FORMAT_DATE_TIME_CSV, FORMAT_NUMBER, uploadFile2S3,
} from 'src/helpers/common';
import BaseButton from 'src/components/base/BaseButton.vue';

const fileInput = ref();
const router = useRouter();
const fileName = ref('');
const data = ref([]);
const isError = ref(false);
const dataDone = ref({
  // dataDownload: [],
  total: 0,
  updated_count: 0,
  inserted_count: 0,
  changed_count: 0,
});
const dataExportList = ref([]);
const columnExportList = ref([
  { name: 'fileKey', field: 'fileKey' },
  { name: 'dateInput', field: 'dateInput' },
  { name: 'dateEnd', field: 'dateEnd' },
  { name: 'inserted_count', field: 'inserted_count' },
  { name: 'updated_count', field: 'updated_count' },
  { name: 'changed_count', field: 'changed_count' },
  { name: 'button', field: 'button' },
]);
const {
  colorSub, isLoading, isLoadingManual, colorMain,
} = storeToRefs(useAppStore());
// const { setLoading, setLoadingManual } = useAppStore();
const { errors, validateData } = useValidate();
const headersDefaultJP = ref([
  'URL+カードID',
  'トレサビシステムユーザーID',
  '初期パスワード',
  '事業者種別',
  '届出番号',
  '事業者/従事者区分',
  '許可番号',
  '氏名',
  '氏名（カナ）',
  '都道府県',
  '連絡先等',
  '許可の有効期間（開始）',
  '許可の有効期間（終了）',
  '状態',
  'アカウント登録状況',
  '備考1',
  '備考2',
]);
const headerDefault = ref([
  'urlcardid',
  'user_code',
  'init_password',
  'enterprise_type',
  'enterprise_code',
  'staff_type',
  'license_number',
  'name',
  'name_kana',
  'province_name',
  'phone',
  'expiry_date_start',
  'expiry_date',
  'status',
  'account_registration_status',
  'note_1',
  'note_2',
]);

const headersDefaultJPExport = ref([
  'URL+カードID',
  'トレサビシステムユーザーID',
  '初期パスワード',
  '事業者種別',
  '届出番号',
  '事業者/従事者区分',
  '許可番号',
  '氏名',
  '氏名（カナ）',
  '都道府県',
  '連絡先等',
  '許可の有効期間（開始）',
  '許可の有効期間（終了）',
  '状態',
  'アカウント登録状況',
  '備考1',
  '備考2',
]);
const headerDefaultExport = ref([
  'urlcardid',
  'user_code',
  'init_password',
  'enterprise_type',
  'enterprise_code',
  'staff_type',
  'license_number',
  'name',
  'name_kana',
  'province_name',
  'phone',
  'expiry_date_start',
  'expiry_date',
  'status',
  'account_registration_status',
  'note_1',
  'note_2',
]);

const isHeaderError = ref(false);
const triggerFileInput = () => {
  fileInput.value.click();
};
const fileStore = ref();

const onUploadFile = async event => {
  [fileStore.value] = event.target.files;
  fileName.value = fileStore.value.name;
  const filePath = event.target.value;
  const fileExtension = filePath.substr(
    filePath.lastIndexOf('.') + 1,
    filePath.length
  );
  if (fileExtension !== 'csv') {
    return false;
  }
  isHeaderError.value = false;
  papa.parse(fileStore.value, {
    header: true,
    encoding: 'UTF-8-BOM',
    skipEmptyLines: true,
    transformHeader: (header, index) => {
      if (header !== headersDefaultJP.value[index] && !headerDefault.value.includes(header)
      && (index <= headersDefaultJP.value.length - 1)) {
        isHeaderError.value = true;
        return null;
      }
      return headerDefault.value[index] || header;
    },
    complete: async results => {
      fileInput.value.value = null;
      data.value = results.data;
    },
  });
  return true;
};

const reloadListDownload = async () => {
  const resultGet = await userAdminService.getImportUser();
  if (resultGet.code === 0) {
    const listSuccess = resultGet.payload.data.filter(item => item.status === 'SUCCESS');
    dataExportList.value = resultGet.payload.data
      .sort((a, b) => (a?.createDate < b?.createDate ? 1 : -1));
    dataDone.value.total = 0;
    dataDone.value.inserted_count = 0;
    dataDone.value.updated_count = 0;
    dataDone.value.changed_count = 0;
    listSuccess.forEach(item => {
      dataDone.value.total += (item.inserted_count + item.updated_count);
      dataDone.value.inserted_count += item.inserted_count;
      dataDone.value.updated_count += item.updated_count;
      dataDone.value.changed_count += item.changed_count;
    });
  }
};

const importCsvHandler = async () => {
  // check header key
  isError.value = false;
  if (isHeaderError.value) {
    isError.value = true;
    errors.value[''] = [{
      line: '',
      value: '',
      message: MESSAGE.MSG_NA_CSVITEM_ERROR,
    }];
    isHeaderError.value = false;
    return;
  }
  // check body
  const valid = validateData(importUserSchema, data.value);
  if (!valid) {
    isError.value = true;
    return;
  }
  const result = await userAdminService.presignImportUser({ nameFile: fileName.value });
  if (result.code === 0) {
    uploadFile2S3(result.payload.data, fileStore.value);
    await reloadListDownload();
  }
};

const goBack = () => {
  router.back();
};

const downloadCsv = async fileKey => {
  const result = await userAdminService.downloadImportUser({ fileKey });
  if (result.code === 0) {
    const headerExport = [...headerDefaultExport.value];
    const headerExportJP = [...headersDefaultJPExport.value];

    const dataExport = result.payload.data.map(item => {
      if (item.province_custom_data) {
        if (item.header) {
          item.header.forEach(key => {
            if (!headerExport.includes(key)) {
              headerExport.push(key);
              headerExportJP.push(key);
            }
            item[key] = Object.keys(item.province_custom_data).includes(key)
              ? item.province_custom_data[key] : item[key];
          });
        }
      }

      return {
        ...item,
        enterprise_code: item.enterprise_code ? `'${item.enterprise_code}` : '',
        urlcardid: (item?.license_code ? `${process.env.SITE_URL}/login/${item?.license_code}` : ''),
      };
    });

    exportCSV(
      dataExport,
      headerExport,
      headerExportJP,
      `ユーザー設定_${FORMAT_DATE_TIME_CSV('YYYYMMDDHHmm')}`
    );
  }
};

onMounted(async () => {
  await reloadListDownload();
});

</script>
<style lang="sass">
.my-sticky-virtscroll-table
  /* height or max-height is important */

  .q-table__top,
  .q-table__bottom,

  thead tr th
    position: sticky
    z-index: 1
  /* this will be the loading indicator */
  thead tr:last-child th
    /* height of all previous header rows */
    top: 48px
  thead tr:first-child th
    top: 0

  /* prevent scrolling behind sticky top row on focus */
  tbody
    /* height of all previous header rows */
    scroll-margin-top: 48px
</style>
