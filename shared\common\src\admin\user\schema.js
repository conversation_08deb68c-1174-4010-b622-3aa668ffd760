
const { sharedSchema } = require('../../validations');
const { MESSAGE } = require('../../utils/message');
const { USER_STATUS_ENUM, ENTERPRISE_TYPE_ENUM, USER_STATUS_REGISTER_ENUM, ROLES_ENUM_OPTIONS_VALUES } = require('../../helpers/enum');

const getUserListAdminSchema = {
  summary: 'Get user',
  description: 'Get user',
  tags: ['Admin'],
  security: [{ bearToken: [] }],
  query: {
    type: 'object',
    properties: {
      page: {
        type: 'integer',
        errorMessage: {
          _: MESSAGE.MSG_PAGE_INDEX_INVALID,
        },
      },
      limit: {
        type: 'integer',
        errorMessage: {
          _: MESSAGE.MSG_PAGE_SIZE_INVALID,
        },
      },
      provinceId: {
        type: 'integer',
      },
      enterpriseName: {
        type: 'string',
        maxLength: 256,
      },
      userName: {
        type: 'string',
        maxLength: 256,
      },
      userId: {
        type: 'string',
        maxLength: 50,
      },
      userNameOrCode: {
        type: 'string',
        maxLength: 50,
      },
      status: {
        type: 'integer',
        enum: Object.values(USER_STATUS_ENUM),
      },
      statusRegister: {
        type: 'integer',
        enum: Object.values(USER_STATUS_REGISTER_ENUM),
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      userType: {
        type: 'string',
        enum: Object.values(ROLES_ENUM_OPTIONS_VALUES),
      },
      startUpdateDate: {
        type: 'string',
        format: 'slash-date',
        formatMaximum: {
          $data: '1/endUpdateDate',
        },
        errorMessage: {
          format: MESSAGE.DATE_ERROR,
          formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
        },
      },
      endUpdateDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.DATE_ERROR,
        },
      },
      startExpiryDate: {
        type: 'string',
        format: 'slash-date',
        formatMaximum: {
          $data: '1/endExpiryDate',
        },
        errorMessage: {
          format: MESSAGE.DATE_ERROR,
          formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
        },
      },
      endExpiryDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.DATE_ERROR,
        },
      },
      note1: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.NOTE1_ERROR,
        },
      },
      note2: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.NOTE2_ERROR,
        },
      },
      showPending: {
        type: 'boolean',
      },
    },
  },
  response: sharedSchema.response,
};

const registerNewUserSchema = {
  summary: 'Register new user',
  description: 'Register new user',
  tags: ['Admin'],
  security: [{ 'bearToken': [], 'apiKey': [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    properties: {
      provinceId: {
        type: 'integer'
      },
      enterpriseType: {
        type: 'integer',
        enum: Object.values(ENTERPRISE_TYPE_ENUM),
      },
      enterpriseCode: {
        type: 'string',
        pattern: '^[0-9]{7}$',
        minLength: 1,
        errorMessage: {
          minLength: MESSAGE.MSG_NA_NOTIFICATIONUMBER_ERR,
          pattern: MESSAGE.MSG_NA_NOTIFICATIONUMBER_ERR,
        }
      },
      typeStaffOrEnterprise: {
        type: 'integer',
        enum: [0, 1], // 0: enterprise, 1: staff
      },
      enterpriseName: {
        type: 'string',
        minLength: 1,
        maxLength: 50,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      enterpriseNameKana: {
        type: 'string',
        oneOf: [
          {
            pattern: '^[ァ-ヴー　]+$',
            maxLength: 50,
          },
          {
            minLength: 0,
            maxLength: 0,
          }
        ],
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_KANA_ERROR
        },
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      phone:{
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR
        }
      },
      startExpiryDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.DATE_ERROR,
        },
      },
      endExpiryDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.DATE_ERROR,
        },
      },
      status: {
        type: 'integer',
        enum: Object.values(USER_STATUS_ENUM),
      },
      password: {
        type: 'string',
        minLength: 1,
        maxLength: 256,
        pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
        errorMessage: {
          minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
        },
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      },
    }
  }
};

const editUserSchema = {
  summary: 'Edit user',
  description: 'Edit user',
  tags: ['Admin'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    properties: {
      provinceId: {
        type: 'integer'
      },
      name: {
        type: 'string',
        minLength: 1,
        maxLength: 50,
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      nameKana: {
        type: 'string',
        oneOf: [
          {
            pattern: '^[ァ-ヴー　]+$',
            maxLength: 50,
          },
          {
            minLength: 0,
            maxLength: 0,
          }
        ],
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_KANA_ERROR
        },
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.LICENSE_NUMBER_ERROR,
        },
      },
      phone: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
        }
      },
      startExpiryDate: {
        anyOf: [
          {
            type: 'string',
            format: 'slash-date',
            formatMaximum: {
              $data: '1/endExpiryDate',
            },
          },
          {
            type: 'integer',
            nullable: true,
          },
        ],
        errorMessage: {
          anyOf: MESSAGE.DATE_ERROR,
          format: MESSAGE.DATE_ERROR,
          formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
        },
      },
      endExpiryDate: {
        anyOf: [
          {
            type: 'string',
            format: 'slash-date',
          },
          {
            type: 'integer',
            nullable: true,
          },
        ],
        errorMessage: {
          anyOf: MESSAGE.DATE_ERROR,
          format: MESSAGE.DATE_ERROR,
        },
      },

      status: {
        type: 'integer',
        enum: Object.values(USER_STATUS_ENUM),
      },
      account_registration_status: {
        type: 'integer',
        enum: Object.values(USER_STATUS_REGISTER_ENUM),
      },
      password: {
        type: 'string',
        oneOf: [
          {
            minLength: 0,
            maxLength: 0,
          },
          {
            minLength: 1,
            maxLength: 256,
            pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
            errorMessage: {
              minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
              _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
            },
          }
        ]
      },
      note1: {
        type: 'string'
      },
      note2: {
        type: 'string'
      },
      enable_export_function: {
        type: 'boolean',
      },
      province_custom_data: {
        type: 'object',
      },
    }
  }
}

const presignImportUserSchema = {
  summary: 'Import user',
  description: 'Import user',
  tags: ['Admin'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    properties: {
      nameFile: {
        type: 'string'
      },
    }
  }
}

const getImportUserSchema = {
  summary: 'Get Import user',
  description: 'Get Import user',
  tags: ['Admin'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
}

const downloadImportUserSchema = {
  summary: 'Download Import user',
  description: 'Download Import user',
  tags: ['Admin'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    additionalProperties: false,
    type: 'object',
    required: ['fileKey'],
    properties: {
      fileKey: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
}

const bulkUpdateUserStatusSchema = {
  body: {
    type: 'object',
    required: ['roles', 'provinceIds'],
    properties: {
      roles: {
        type: 'array',
        items: { type: 'integer' },
        minItems: 1,
      },
      provinceIds: {
        type: 'array',
        items: { type: 'integer' },
        minItems: 1,
      },
    },
  },
};

module.exports = {
  getUserListAdminSchema,
  registerNewUserSchema,
  editUserSchema,
  presignImportUserSchema,
  getImportUserSchema,
  downloadImportUserSchema,
  bulkUpdateUserStatusSchema
};
