version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 22.x
    commands:
      # - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &
      # - timeout 15 sh -c "until docker info; do echo .; sleep 1; done"
  pre_build:
    commands:
      - pwd
      - ls
      - cd usk/fe/src
      # - cd usk/fe/src
      - ls
      - echo $S3_BUCKET
      - echo $VERSION
      # - echo Logging in to Amazon ECR...
      # - aws --version
      # - AWS_DEFAULT_REGION=ap-northeast-1
      # - echo $AWS_DEFAULT_REGION
      # - aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 097316358141.dkr.ecr.ap-northeast-1.amazonaws.com
      # - REPOSITORY_URI=097316358141.dkr.ecr.ap-northeast-1.amazonaws.com/fargate-monorepository
      - aws s3 rm s3://$S3_BUCKET/$VERSION --recursive
  build:
    commands:
      - echo Build started on `date`
      - echo $NODE_ENV
      - rm -rf node_modules package-lock.json
      - npm i
      - npx quasar build -P -m pwa
      # - docker images
      # - docker build -t usk-fe-image .
      # - echo $REPOSITORY_URI
      # - echo $REPOSITORY_URI:usk-fe-image
      # - docker tag usk-fe-image:latest $REPOSITORY_URI:usk-fe-image
  post_build:
    commands:
      - echo Build completed on `date`
      - aws s3 sync ./dist s3://$S3_BUCKET/$VERSION
      # - echo Pushing the Docker image...
      # - docker push $REPOSITORY_URI:usk-fe-image
      # - echo Writing image definition file...
      - echo Done
