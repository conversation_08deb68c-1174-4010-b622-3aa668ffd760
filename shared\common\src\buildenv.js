// ### Create the .env file run local.
const fs = require("fs");
const os = require("os");
const path = require("path");
const { scanParameterStoreByPath } = require('./helpers/scanssmclient');

const envFilePath = path.resolve(__dirname, ".env");

// Parse env from ParameterStore
const parseEnv = (data) => {
  const envData = [];
  while (data.length > 0) {
    const element = data.shift();
    const environmentName = element.Name.replace(`/SHARED/${process.env.NODE_ENV}/ENV`, '')
      .split('/').filter(val => val !== '').shift();
    envData.push(`${environmentName}="${element.Value}"`);
  }
  envData.push(`StackName="${process.env.NODE_ENV}"`);
  return envData;
}

/**
 * Updates value for existing key or creates a new key=value line
 *
 * This function is a modified version of https://stackoverflow.com/a/65001580/3153583
 *
 * @param {string} key Key to update/insert
 * @param {string} value Value to update/insert
 */
const setEnvValue = (envVars) => {
  // write everything back to the file system
  fs.writeFileSync(envFilePath, envVars.join(os.EOL));
};

const getEnvFromParameterStore = async () => {
  const params = {
    Path: `/SHARED/${process.env.NODE_ENV}/ENV`,
    // Recursive: true,
  };
  const paramStoreData = await scanParameterStoreByPath(params);
  const envData = parseEnv(paramStoreData);
  setEnvValue(envData);
}

getEnvFromParameterStore().then(() => console.log('Make Environment Variables Finished!'));