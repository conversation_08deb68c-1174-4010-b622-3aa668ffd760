<template>
  <div class="tw:p-3 tw:tl:p-6 tw:text-[#333333]">
    <!-- title -->
    <div class="tw:text-start tw:text-l-design tw:py-5 tw:font-bold">
      パスワード再登録
    </div>
    <p class="tw:text-m-design tw:font-normal tw:mb-1">初期パスワードからパスワードを変更してください。</p>
    <p class="tw:text-m-design tw:font-normal">（パスワードは8文字以上で、英大文字・英小文字・数字をすべて含む必要があります。）</p>
    <!-- content -->
    <div class="tw:mt-5">
      <div class="tw:text-m-design tw:flex tw:flex-wrap ">
        <span class="tw:text-m-design tw:relative">パスワード
          <q-badge class=" tw:text-xxs-design tw:font-normal tw:px-[0.625rem]
          badgeColor tw:ml-2 tw:absolute tw:dt:top-[0.3rem] tw:top-[0.7rem]
          tw:min-h-[1.6875rem] tw:max-h-[1.6875rem] tw:h-[1.6875rem]">
          必須
        </q-badge>
        </span>

      </div>
      <q-input
        class="tw:mt-3 tw:text-[#7E8093] tw:text-m-design tw:font-normal"
        input-class="tw:py-[0.625rem] tw:px-[1.25rem]"
        outlined
        v-model="form.password"
        autocomplete="nope"
        :type="showPassword ? 'text' : 'password'"
        lazy-rules
        maxlength="256"
        placeholder="パスワードを入力してください。"
        :error="!!errors.password"
        :error-message="errors.password"
        no-error-icon
      >
        <template v-slot:append>
          <q-icon class="tw:text-[#003687]" size="2.25rem" :name="showPassword ? 'visibility' : 'visibility_off'"
            @click.prevent="showPassword = !showPassword" />
        </template>
      </q-input>
      <div class="tw:text-m-design tw:flex tw:flex-wrap tw:font-normal tw:mt-12 tw:tl:mt-4 ">
        <span class="tw:text-m-design tw:relative">パスワード確認用
          <q-badge class=" tw:text-xxs-design tw:font-normal tw:px-[0.625rem]
          badgeColor tw:ml-2 tw:absolute tw:dt:top-[0.3rem] tw:top-[0.7rem]
          tw:min-h-[1.6875rem] tw:max-h-[1.6875rem] tw:h-[1.6875rem]">
          必須
        </q-badge>
        </span>
      </div>
      <q-input
        class="tw:mt-3 tw:text-[#7E8093] tw:text-m-design tw:font-normal"
        input-class="tw:py-[0.625rem] tw:px-[1.25rem]"
        outlined
        v-model="form.passwordConfirm"
        autocomplete="nope"
        :type="showPasswordConfirm ? 'text' : 'password'"
        lazy-rules
        maxlength="256"
        placeholder="パスワードを入力してください。"
        :error="!!errors.passwordConfirm"
        :error-message="errors.passwordConfirm"
        no-error-icon
      >
        <template v-slot:append>
          <q-icon class="tw:text-[#004AB9]" size="2.25rem" :name="showPasswordConfirm ? 'visibility' : 'visibility_off'"
            @click.prevent="showPasswordConfirm = !showPasswordConfirm" />
        </template>
      </q-input>
      <div class="tw:flex tw:justify-center tw:mt-8">
        <BaseButton outline  class="tw:rounded-[40px]"
          :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold tw:tl:w-[16.5rem] tw:tl:h-[5.5rem] tw:w-full`"
          label="登録する" @click.prevent="recoveryPassword" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import resetPassword from 'schemas/resetPassword';
import authService from 'services/auth.service';
import BaseButton from 'src/components/base/vs/BaseButton.vue';

const { errors, validateData } = useValidate();
const router = useRouter();

// ===== REF =====
const form = ref({
  password: '',
  passwordConfirm: '',
});
const showPassword = ref(false);
const showPasswordConfirm = ref(false);

// ===== METHOD =====
const recoveryPassword = async () => {
  // change password
  const valid = validateData(resetPassword, form.value);
  if (!valid) {
    return;
  }

  await authService.recoveryPassword(
    { password: form.value.password },
    router.currentRoute.value.params?.code
  );

  router.push({ name: 'loginUserId' });
};
</script>

<style>
.badgeColor {
  background-color: #E80F00 !important;
  color: #ffffff !important;
}
</style>
