<template>
  <q-layout view="lHh Lpr lFf" class="tw:flex">
    <q-header elevated :class="`tw:bg-${colorMain}`">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click.prevent="toggleLeftDrawer"
        >
        </q-btn>

        <q-toolbar-title>
          <div class="tw:flex tw:justify-between">
            <div
              :class="`tw:flex-1 tw:text-[1.5rem] tw:flex tw:font-bold tw:py-3
                tw:flex-wrap tw:tl:justify-center`"
              @click="gotoPage('adminUserManager')"
            >
              <span class="tw:cursor-pointer">
                {{ envPrefix }}シラスウナギトレーサビリティ支援システム（管理者用）
              </span>
            </div>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered behavior="mobile">
      <q-list>
        <q-item-label header>
          <img
            :src="CloseSvg"
            alt=""
            class="tw:w-[2.5rem] tw:h-[2.5rem] tw:hover:cursor-pointer"
            @click.prevent="toggleLeftDrawer"
          />
        </q-item-label>

        <div class="tw:px-4 tw:font-normal tw:pb-2 tw:border-b tw:border-gray">
          <div
            class="tw:h-8 tw:pb-4 tw:leading-8 tw:text-sm"
          >
            ユーザーID：{{ user?.user_code }}
          </div>
          <div class="tw:flex tw:py-2">
            <div
              :class="`tw:w-1/2 tw:text-blue tw:leading-8
                tw:h-8 tw:hover:cursor-pointer tw:text-sm`"
              @click.prevent="logout"
            >
              ログアウト
            </div>
          </div>
        </div>
        <EssentialAdminLink />
      </q-list>
    </q-drawer>

    <div
      class="tw:w-screen tw:flex-1 tw:pt-[3.5rem] tw:tl:pt-[5.3rem] tw:dt:pt-[3rem]"
    >
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        class="tw:w-full tw:h-full"
        ref="scrollAreaRef"
      >
        <q-page-container
          class="tw:px-3 tw:tl:px-8 tw:tl:pt-[1rem]! tw:pt-[6rem] tw:bg-[#FFFFFF] tw:min-h-[calc(100vh-6rem)] "
        >
          <div class="w-content tw:tl:w-[calc(100vw-4rem)] tw:mb-3 tw:dt:mt-[1rem]">
            <div
              class="tw:pt-2 tw:inline-flex tw:items-center tw:hover:cursor-pointer tw:hover:opacity-70"
              @click.prevent="backRoute"
              v-if="
                router.currentRoute.value?.meta?.routeBack &&
                !router.currentRoute.value?.meta?.isBreadcrumb
              "
            >
              <q-icon
                name="arrow_back"
                class="tw:tl:text-[2rem] tw:text-lg tw:text-gray-1"
              />
              <span
                class="tw:ml-2 tw:tl:text-[2rem] tw:font-black tw:text-lg tw:text-gray-1"
              >
                戻る</span
              >
            </div>
            <div
              class="tw:flex tw:justify-between"
              v-if="
                router.currentRoute.value?.meta?.title &&
                !router.currentRoute.value?.meta?.isBreadcrumb
              "
            >
              <div
                class="tw:tl:leading-[4rem] tw:tl:text-[4rem] tw:font-bold tw:text-2xl tw:border-b tw:border-gray tw:pb-6 tw:pt-4 tw:flex-1"
              >
                {{ router.currentRoute.value.meta.title }}
              </div>
            </div>
            <div
              class="tw:flex tw:justify-between"
              v-if="router.currentRoute.value?.meta?.isBreadcrumb"
            >
              <!-- Breadcrumbs -->
              <q-breadcrumbs
                class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:flex-1"
                active-color="tw:text-blue-3"
              >
                <template v-slot:separator>
                  <q-icon
                    size="1.5em"
                    name="chevron_right"
                    class="tw:text-[#7E8093]"
                  />
                </template>
                <q-breadcrumbs-el
                  class="tw:cursor-pointer"
                  label="トップ"
                  @click.prevent="gotoPage('home')"
                />
                <q-breadcrumbs-el
                  :label="`${router.currentRoute.value.meta.title}`"
                  @click.prevent="gotoPage(`${router.currentRoute.value.meta.prevRoute}`)"
                />
                <div v-if="router.currentRoute.value.meta.tag" class="tw:ml-2 tw:text-[#333333] tw:flex tw:items-center tw:justify-center ">
                  <span class="tw:text-[#7E8093] tw:pb-1 tw:pl-4 tw:pr-5">|</span>
                  {{
                  router.currentRoute.value.meta.tag
                    ? router.currentRoute.value.meta.tag
                    : ""
                }}
                </div>
              </q-breadcrumbs>
            </div>
            <router-view />
          </div>
        </q-page-container>
      </q-scroll-area>
    </div>
  </q-layout>
</template>

<script setup>
import {
  computed, provide, ref, onMounted,
} from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import { useAuthStore } from 'stores/auth-store';
import authService from 'services/auth.service';
import { ROLES_ENUM } from 'helpers/constants';
import CloseSvg from 'assets/CloseSvg.svg';
import EssentialAdminLink from 'src/components/EssentialAdminLink.vue';

const { setLoading, setLoadingManual, setSettingUser } = useAppStore();
const { colorMain } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const envPrefix = ref('');
const linksList = computed(() => [
  {
    title: 'ユーザー管理',
    link: 'adminUserManager',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: '各種設定値管理',
    // vs14 done
    link: 'adminSystemSettings',
    role: [ROLES_ENUM.SYSTEM_ADMIN],
    borderTop: true,
  },
  {
    title: 'お知らせ管理',
    link: 'adminNotificationManager',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'マニュアル',
    external: true,
    link: `${window.location.origin}/document/adminsite/manual.pdf`,
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'プライバシーポリシー',
    link: 'privacyPolicyAdmin',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'ライセンス',
    link: 'licenseAdmin',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: '利用規約',
    link: 'termsOfUserAdmin',
    borderTop: true,
    borderBot: true,
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
  },
]);

const leftDrawerOpen = ref(false);

provide('items', linksList);

const toggleLeftDrawer = async () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const gotoPage = async name => {
  await router.push({ name });
};

const backRoute = async () => {
  router.back();
};

const logout = async () => {
  setLoadingManual(true);
  setLoading(true);
  await authService.logout();
  setSettingUser(null);
  router.push({
    name: 'adminLogin',
  });
  setLoading(false);
  setLoadingManual(false);
};

onMounted(() => {
  const html = document.querySelector('html');
  html.classList.add('admin-layout');
  envPrefix.value = `${process.env.PREFIX}`;

});
</script>
<style>
@media (min-width: 1440px) and (max-width: 1919px) {
  aside.q-drawer {
    width: 20rem;
  }
}
</style>
