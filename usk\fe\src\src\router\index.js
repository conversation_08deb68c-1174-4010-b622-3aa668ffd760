import { route } from 'quasar/wrappers';
import {
  createRouter,
  createM<PERSON>oryHistory,
  createWebHistory,
  createWebHashHistory,
} from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAuthStore } from 'stores/auth-store';
import authService from 'services/auth.service';
import { CHECK_ROLE } from 'helpers/common';
import { useAppStore } from 'stores/app-store';
import { SHOW_DEFAULT_SCAN_QR_ENUM } from 'helpers/constants';
import { ROLES_ENUM, ROUTER_NESTED_LIST, USER_STATUS_ENUM, USER_STATUS_REGISTER_ENUM } from 'src/helpers/constants';
import settingService from 'services/setting.service';
import notificationService from 'services/notification.service';
import { useLocalStorage } from 'src/composables/localstorage';

import routes from './routes';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route((/* { store, ssrContext } */) => {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
      ? createWebHistory
      : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  Router.beforeEach(async (to, from) => {
    const { setPreviousRoute, setSettingUser } = useAppStore();
    const { user } = storeToRefs(useAuthStore());
    const { role, settingUser } = storeToRefs(useAppStore());
    // Save previous route to store
    setPreviousRoute(from);

    if (!to.meta) {
      return false;
    }

    // if next route not need auth
    if (!to.meta.auth) {
      await authService.checkLogin();
      if (user.value) {
        const rootRoute =
          role.value === ROLES_ENUM.ADMIN ||
            role.value === ROLES_ENUM.SYSTEM_ADMIN
            ? 'adminUserManager'
            : 'home';
        return {
          name: rootRoute,
        };
      }
      return true;
    }

    // if next route need auth

    // if store not have user and route need auth
    if (!user.value) {
      // check login (use token to get user info)
      await authService.checkLogin();

      // authentication user failed
      if (!user.value) {
        // clear setting user
        const checkLoginInit =
          settingUser.value?.qr_scan_init ===
          SHOW_DEFAULT_SCAN_QR_ENUM.USE_SCAN;
        setSettingUser(null);
        if (
          to.meta.role?.includes(ROLES_ENUM.ADMIN) ||
          to.meta.role?.includes(ROLES_ENUM.SYSTEM_ADMIN)
        ) {
          return {
            name: 'adminLogin',
          };
        }
        return {
          name: checkLoginInit ? 'loginQrScan' : 'loginUserId',
        };
      }

      // Check user status if user has status pending redirect to recovery password
      if (user.value.account_registration_status === USER_STATUS_REGISTER_ENUM.TEMPORARY
        && user.value.role === ROLES_ENUM.NORMAL_USER
      ) {
        if (to.name !== 'recoveryPassword') {
          return {
            name: 'recoveryPassword',
          };
        }
        return true;
      }

      // get setting user with not admin role
      if (
        user.value.role !== ROLES_ENUM.ADMIN &&
        user.value.role !== ROLES_ENUM.SYSTEM_ADMIN &&
        user.value.role !== ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN &&
        user.value.role !== ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN
      ) {
        const result = await settingService.getSetting();
        setSettingUser(result.payload);
      }
    }

    // having user in store

    // Check user status if user is logged in and has status pending redirect to recovery password
    if (user.value.account_registration_status === USER_STATUS_REGISTER_ENUM.TEMPORARY
      && user.value.role === ROLES_ENUM.NORMAL_USER) {
      if (to.name !== 'recoveryPassword') {
        return {
          name: 'recoveryPassword',
        };
      }
      return true;
    }

    // get setting user with not admin role when user is logged in and not have setting user
    if (
      to.name === 'home' &&
      !settingUser.value &&
      user.value.role !== ROLES_ENUM.ADMIN &&
      user.value.role !== ROLES_ENUM.SYSTEM_ADMIN &&
      user.value.role !== ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN &&
      user.value.role !== ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN
    ) {
      const result = await settingService.getSetting();
      setSettingUser(result.payload);
    }

    // Calc root route
    let rootRoute = '';
    switch (role.value) {
      case ROLES_ENUM.ADMIN:
      case ROLES_ENUM.SYSTEM_ADMIN:
        rootRoute = 'adminUserManager';
        break;
      case ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN:
        rootRoute = 'fisheriesDepartmentAdmin';
        break;
      case ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN:
        rootRoute = 'localGovernmentAdmin';
        break;
      default:
        rootRoute = 'home';
        break;
    }

    //  redirect to root route if user is logged in and prev route is not valid
    if (to.meta.prevRoute && !to.meta.prevRoute.includes(from.name)) {
      return {
        name:
          role.value === ROLES_ENUM.ADMIN ||
            role.value === ROLES_ENUM.SYSTEM_ADMIN
            ? 'adminLogin'
            : 'loginUserId',
      };
    }

    // redirect to root route if user is logged in and user role is not valid
    if (
      to.meta.role?.length > 0 &&
      !CHECK_ROLE(to.meta.role, to.meta.type, to.meta.staff, user.value)
    ) {
      return {
        name: rootRoute,
      };
    }

    // reset listPageQueryParams to default when route is not nested
    const lstRoute = Object.keys(ROUTER_NESTED_LIST);
    if (lstRoute.includes(to.name) && !ROUTER_NESTED_LIST[to.name].routers.includes(from.name)) {
      const { listPageQueryParams } = useLocalStorage();
      listPageQueryParams.value[ROUTER_NESTED_LIST[to.name].nameOfLocal] = {
        page: 1,
        limit: 10,
        rowsPerPage: 10,
      };
    }
    return true;
  });

  Router.afterEach(async (to, from) => {
    const { setHasUnreadNotificationFlag } = useAppStore();
    const { user } = storeToRefs(useAuthStore());
    if (
      user.value &&
      (user.value.status === USER_STATUS_ENUM.ACTIVE ||
        user.value.status === USER_STATUS_ENUM.NONACTIVE) &&
      user.value.role !== ROLES_ENUM.ADMIN &&
      user.value.role !== ROLES_ENUM.SYSTEM_ADMIN &&
      to.name !== from.name
    ) {
      // Check has unread notification
      const checkHasUnreadNotification =
        await notificationService.checkHasUnreadNotification();
      setHasUnreadNotificationFlag(
        checkHasUnreadNotification?.payload?.has_unread
      );
    }

  });

  Router.onError(error => {
    const pattern = /Failed to fetch dynamically imported module/g;
    const patternSafari = /Importing a module script failed/g;
    const patternNotifi = /Failed to execute 'subscribe' on 'PushManager'/g;
    const isChunkLoadFailed =
      error.message.match(pattern) ||
      error.message.match(patternSafari) ||
      error.message.match(patternNotifi);
    if (isChunkLoadFailed) {
      window.location.reload();
    } else {
      throw error;
    }
  });

  return Router;
});
