<template>
  <q-layout view="lHh Lpr lFf" class="tw:flex">
    <q-header elevated :class="`tw:bg-${colorMain}`">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click.prevent="toggleLeftDrawer"
        >
        </q-btn>

        <q-toolbar-title>
          <div class="tw:flex tw:justify-between">
            <div
              :class="`tw:text-[1.5rem] tw:flex tw:w-full tw:font-bold tw:py-3 tw:justify-center`"
              @click="handleHeaderClick"
            >
            <span class="tw:cursor-pointer">
              {{ envPrefix }}シラスウナギトレーサビリティ支援システム（管理者用）
              </span>
            </div>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered behavior="mobile">
      <q-list>
        <q-item-label header>
          <img
            :src="CloseSvg"
            alt=""
            class="tw:w-[2.5rem] tw:h-[2.5rem] tw:hover:cursor-pointer"
            @click.prevent="toggleLeftDrawer"
          />
        </q-item-label>

        <div class="tw:px-4 tw:font-normal tw:pb-2 tw:border-b tw:border-gray">
          <div
            class="tw:h-8 tw:pb-4 tw:leading-8 tw:text-sm"
          >
            ユーザーID：{{ user?.user_code }}
          </div>
          <div class="tw:flex tw:py-2">
            <div
              :class="`tw:w-1/2 tw:text-blue tw:leading-8
                tw:h-8 tw:hover:cursor-pointer tw:text-sm`"
              @click.prevent="logout"
            >
              ログアウト
            </div>
          </div>
        </div>
        <EssentialAdminLink />
      </q-list>
    </q-drawer>

    <div class="tw:w-screen tw:flex-1 tw:pt-[4.5rem]">
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        :vertical-bar-style="verticalBarStyle"
        :horizontal-bar-style="horizontalBarStyle"
        class="tw:w-full tw:h-full"
        ref="scrollAreaRef"
      >
        <q-page-container class="tw:px-8 tw:pt-0">
          <div class="tw:w-[calc(100vw-4rem)] tw:mb-3">
            <div
              class="tw:pt-2 tw:inline-flex tw:items-center
              tw:hover:cursor-pointer tw:hover:opacity-70"
              @click.prevent="backRoute"
              v-if="router.currentRoute.value?.meta?.routeBack"
            >
              <q-icon
                name="arrow_back"
                class="tw:text-[2rem] tw:text-gray-1 tw:leading-[1.5rem]"
              />
              <span
                class="tw:ml-2 tw:leading-6 tw:text-[2rem] tw:font-black tw:text-gray-1"
              >
                戻る</span
              >
            </div>
            <div
              :class="`tw:border-b-2 tw:border-gray tw:text-[1.5rem] tw:font-bold
                tw:text-center tw:py-3 tw:mx-10`"
              v-if="router.currentRoute.value?.meta?.title"
            >
              <span class="tw:block tw:max-w-[840px] tw:mx-auto">
                {{ router.currentRoute.value.meta.title }}
              </span>
            </div>
            <router-view />
          </div>
        </q-page-container>
      </q-scroll-area>
    </div>
  </q-layout>
</template>

<script setup>
import {
  computed, provide, ref, watch,
  onMounted,
} from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from 'stores/auth-store';
import authService from 'services/auth.service';
import { ROLES_ENUM } from 'helpers/constants';
import CloseSvg from 'assets/CloseSvg.svg';
import EssentialAdminLink from 'src/components/EssentialAdminLink.vue';

const { setLoading, setLoadingManual, setSettingUser } = useAppStore();
const { colorMain } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const route = useRoute();
const scrollAreaRef = ref(null);
const envPrefix = ref('');
const linksList = computed(() => [
  {
    title: 'ユーザー管理',
    link: 'adminUserManager',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: '各種設定値管理',
    // vs14 done
    link: 'adminSystemSettings',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'お知らせ管理',
    link: 'adminNotificationManager',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'マニュアル',
    external: true,
    link: `${window.location.origin}/document/adminsite/manual.pdf`,
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'プライバシーポリシー',
    link: 'privacyPolicyAdmin',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: 'ライセンス',
    link: 'licenseAdmin',
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
    borderTop: true,
  },
  {
    title: '利用規約',
    link: 'termsOfUserAdmin',
    borderTop: true,
    borderBot: true,
    role: [ROLES_ENUM.SYSTEM_ADMIN, ROLES_ENUM.ADMIN],
  },
]);

const leftDrawerOpen = ref(false);

provide('items', linksList);

const toggleLeftDrawer = async () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const gotoPage = async name => {
  await router.push({ name });
};

const handleHeaderClick = () => {
  if (route.name === 'adminUserManager') {
    window.location.href = router.resolve({ name: 'adminUserManager' }).href;
  } else {
    gotoPage('adminUserManager');
  }

};

const backRoute = async () => {
  router.back();
};

const logout = async () => {
  setLoadingManual(true);
  setLoading(true);
  await authService.logout();
  setSettingUser(null);
  router.push({
    name: 'adminLogin',
  });
  setLoading(false);
  setLoadingManual(false);
};

// style scroll
const thumbStyle = {
  backgroundColor: '#737373',
  width: '8px',
  height: '8px',
  opacity: 0.75,
};
const barStyle = {
  backgroundColor: '#737373',
  opacity: 0.3,
};

const verticalBarStyle = {
  width: '8px',
};

const horizontalBarStyle = {
  height: '8px',
};

const animateScroll = () => {
  scrollAreaRef.value.setScrollPosition('vertical', 0);
};
watch(
  () => route.name,
  () => {
    animateScroll();
  }
);

onMounted(() => {
  const html = document.querySelector('html');
  html.classList.add('admin-layout');
  envPrefix.value = `${process.env.PREFIX}`;
});
</script>
<style>
aside.q-drawer {
  width: 20rem !important;
}
</style>
