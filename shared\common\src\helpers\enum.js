const PAGINATION = {
  PAGE_SIZE: 10,
  FIRST_PAGE: 1,
  SORT_BY: 'date',
  DEFAULT_DESCENDING: true,
};

const OPTION_TYPE = {
  USER_SUPPLIER: 'user-supplier',
  USER_SHIPPER: 'user-shipper',
  GROUP: 'group',
};

const ROLE_KBN = {
  'TenantOwner': '01',
  'NormalTenantAccount': '02',
  'SystemOwner': '99'
};

const STATUS_KBN = {
  'Active': '01',
  'Inactive': '02',
  'Blocked': '03'
};

const TENANT = {
  'SystemOwnerCode': 'OWNER'
};


const MAIL_TEMPLATE = {
  'ResetPassword': {
    file: '/emails/reset-password.handlebars',
    subject: 'Reset Password'
  }
};

const ROLES_ENUM = {
  SYSTEM_ADMIN: '99',
  ADMIN: '90',
  LOCAL_GOVERNMENT_ADMIN: '91',
  FISHERIES_DEPARTMENT_ADMIN: '92',
  NORMAL_USER: '00',
};


const ENTERPRISE_TYPE_ENUM = {
  CATCH_ENTERPRISE: 0,
  DISTRIBUTE_ENTERPRISE: 5,
  EEL_FARMING_ENTERPRISE: 9,
  FARM: 98,
  FOREIGN: 99,
};

const STAFF_TYPE_ENUM = {
  ENTERPRISE: 0,
  STAFF: 1,
};

const PARTNER_TYPE_ENUM = {
  SUPPLIER: 1,
  SHIPPER: 2,
};

const EXTENT_EXPRIED_DATE = 14;

// TODO: Remove this after refactoring
const UNIT_TYPE_SETTING_ENUM = {
  WEIGHT_ONLY: 1,
  QUANTITY_ONLY: 2,
};

const VOLUME_TYPE_ENUM = {
  WEIGHT: 1,
  QUANTITY: 2,
};

const SHOW_SHIPPING_DESTINATION_ENUM = {
  SHOW: true,
  HIDDEN: false,
};

const SHOW_DEFAULT_SCAN_QR_ENUM = {
  USE_CAMERA: 1,
  USE_SCAN: 2,
  USE_USER_ID: 3,
};

const TYPE_DIFFERENCE_WEIGHT_ENUM = {
  DEATH: 1,
  WEIGHT_ERROR: 2,
  OTHER: 3,
};

const RECEIPT_NUMBER_VALUE_ENUM = {
  ONE: 1,
  TWO: 2,
  THREE: 3,
  FOUR: 4,
  FIVE: 5,
};

const REPORT_TYPE_ENUM = {
  PDF_FILE: 1,
  PRINTED_RECEIPT: 2,
};

const EXPORT_ENUM = {
  SHOW: true,
  HIDDEN: false,
};

const NOTIFICATION_STATUS_ENUM = {
  ALL: 'all',
  READ: 'read',
  UNREAD: 'unread',
}

const INVENTORY_CONTROL_TYPE_ENUM = {
  // 事業者が集約して管理
  GROUP: 1,
  // 各従事者が個別管理
  USER: 2,
}

const INCLUDE_TAX_TYPE_ENUM = {
  // 内税
  INCLUSIVE: 1,
  // 外税
  EXCLUSIVE: 2,
}

const ADMIN_SYSTEM_SETTING_KEYS_ENUM = {
  WEIGHT_ALERT_THRESHOLD: 'weight_alert_threshold',
  EDIT_DEADLINE_FOR_ARRIVAL: 'edit_deadline_for_arrival_shipping',
  CATCHING_RESERVE_PERIOD: 'catching_reserve_period',
};

const USER_STATUS_ENUM = {
  ACTIVE: 0,
  NONACTIVE: 1,
  PENDING: 2,
};

const USER_STATUS_REGISTER_ENUM = {
  TEMPORARY: 0,
  OFFICIAL: 1,
};

const PROVINCES_ENUM = {
  NONE: 0,
  AICHI: 1,
  KAGOSHIMA: 2,
  MIYAZAKI: 3,
  KOCHI: 4,
  MIE: 5,
}

const DYNAMODB_TABLE_NAME = {
  IMPORT_USERS_DATA: 'usk-import_users_data',
  IMPORT_USERS_PROCESS: 'usk-import_users_process',
}

const ROLES_ENUM_OPTIONS_VALUES = {
  CATCH_STAFF: '01',
  CATCH_ENTERPRISE: '02',
  DISTRIBUTE_ENTERPRISE: '03',
  EEL_FARMING_ENTERPRISE: '04',
  DISTRIBUTE_STAFF: '05',
};

const TOTAL_LIMIT_EXPORT = 30000;

const ALLOC_STATUS = {
  DRAFT: 1,
  COMPLETED: 2,
}

module.exports = {
  PAGINATION,
  ROLE_KBN,
  STATUS_KBN,
  TENANT,
  MAIL_TEMPLATE,
  OPTION_TYPE,
  ROLES_ENUM,
  ENTERPRISE_TYPE_ENUM,
  PARTNER_TYPE_ENUM,
  EXTENT_EXPRIED_DATE,
  UNIT_TYPE_SETTING_ENUM,
  SHOW_SHIPPING_DESTINATION_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
  EXPORT_ENUM,
  NOTIFICATION_STATUS_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  USER_STATUS_ENUM,
  PROVINCES_ENUM,
  DYNAMODB_TABLE_NAME,
  RECEIPT_NUMBER_VALUE_ENUM,
  REPORT_TYPE_ENUM,
  USER_STATUS_REGISTER_ENUM,
  STAFF_TYPE_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
  TOTAL_LIMIT_EXPORT,
  INVENTORY_CONTROL_TYPE_ENUM,
  INCLUDE_TAX_TYPE_ENUM,
  ALLOC_STATUS,
  VOLUME_TYPE_ENUM,
};
