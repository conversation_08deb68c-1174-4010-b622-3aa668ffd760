import BaseService from 'services/base.service';

class ArrivalService extends BaseService {
  async getDetailQrArrival(query) {
    try {
      const detailQrArrival = await this.dao.getDetailQrArrival(query);
      return detailQrArrival;
    } catch (error) {
      return null;
    }
  }

  async registerArrivalManual(data) {
    try {
      return this.dao.registerArrivalManual(data);
    } catch (error) {
      return null;
    }
  }

  async registerArrivalQr(data) {
    try {
      return this.dao.registerArrivalQr(data);
    } catch (error) {
      return null;
    }
  }

  async getArrivalList(query) {
    try {
      return this.dao.getArrivalList(query);
    } catch (error) {
      return null;
    }
  }

  async getArrivalDetail(arrivalId) {
    try {
      return this.dao.getArrivalDetail(arrivalId);
    } catch (error) {
      return null;
    }
  }

  async editArrival(id, data) {
    try {
      return this.dao.editArrival(id, data);
    } catch (error) {
      return null;
    }
  }

  async cancelArrival(id) {
    try {
      return this.dao.cancelArrival(id);
    } catch (error) {
      return null;
    }
  }
}

export default new ArrivalService('arrival');
