const sharedSchema = {
  request: {
    page: {
      type: 'integer',
      minimum: 1
    },
  },
  response: {
    200: {
      type: 'object',
      description: 'Success',
      required: ['code', 'message', 'payload', 'datetime'],
      properties: {
        code: { type: 'integer' },
        message: { type: 'string' },
        payload: {
          oneOf: [
            {
              type: 'object',
              additionalProperties: true
            },
            {
              type: 'array'
            },
            { type: 'null' },
          ]
        },
        datetime: { type: 'integer' }
      },
      additionalProperties: false
    },
    // '401': {
    //   type: 'object',
    //   description: 'Unauthorized'
    // },
    // '404': {
    //   type: 'object',
    //   description: 'Not found'
    // },
    // default: {
    //   type: 'object',
    //   description: 'Unexpected error ( Firewall, server down, infrastructure )'
    // }
  },
  definitions: {
    nonEmptyString: {
      minLength: 1,
    },
  },
};

module.exports = {
  sharedSchema,
};