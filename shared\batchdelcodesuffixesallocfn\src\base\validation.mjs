import { Ajv } from 'ajv';
import * as AjvErrors from 'ajv-errors';
import * as AjvKeywords from 'ajv-keywords';
import * as AjvFormats from 'ajv-formats';
import dayjs from 'dayjs';

const ajv = new Ajv.Ajv({
  allErrors: true,
  removeAdditional: true,
  useDefaults: true,
  coerceTypes: true,
  $data: true,
});

AjvErrors.default(ajv);
AjvKeywords.default(ajv);
AjvFormats.default(ajv);

ajv.addFormat('slash-date', {
  type: 'string',
  validate: (data) => dayjs(data, 'YYYY-MM-DD', true).isValid() || !data,
  compare: (data1, data2) => {
    return new Date(data1).getTime() - new Date(data2).getTime();
  },
});

ajv.addKeyword({
  keyword: 'checkDataCust',
  validate: function validate(schema, item) {

    const validData = [];
    if ((!item.enterprise_type === '0'
      && !item.enterprise_type === '5'
      && item.staff_type === '1')
    ) {
      validData.push({
        field: 'staff_type',
        fieldName: '事業者/従事者区分'
      })
    }
    if (item.enterprise_type === '0'
      && !item.license_number) {
        validData.push(
        {
          field: 'license_number',
          fieldName: '許可番号',
        }
      )
    }
    if (item.enterprise_type === '0'
      && !item.expiry_date_start) {
        validData.push(
        {
          field: 'expiry_date_start',
          fieldName: '許可の有効期間（開始）',
        }
      )
    }
    if (item.enterprise_type === '0'
      && !item.expiry_date) {
        validData.push(
        {
          field: 'expiry_date',
          fieldName: '許可の有効期間（終了）',
        }
      )
    }

    if (validData.length) {
      validate.errors = [
        {
          keyword: validData,
          message: validData.map((vd) =>
            vd.fieldName).join(','),
          params: {
            keyword: validData.map((vd) => vd.field)
          }
        }
      ];
      return false;
    }
    return true;
  },
  errors: true,
});

export default ajv;

export const registerMultiCust = {
  type: 'object',
  properties: {
    urlcardid: {
      type: 'string',
    },
    user_code: {
      anyOf: [
        {
          type: 'string',
          maxLength: 12,
          minLength: 1,
          pattern: '^\\d{7}-\\d{4}$',
        },
        {
          type: 'string',
        }
      ],
    },
    init_password: {
      type: 'string',
    },
    enterprise_code: {
      type: 'string',
      minLength: 1,
      maxLength: 7,
      pattern: '^\\d{7}$',
    },
    staff_type: {
      type: 'string',
      enum: ['0', '1']
    },
    license_number: {
      anyOf: [
        {
          type: 'string',
          maxLength: 256,
          minLength: 1,
        },
        {
          type: 'string',
        }
      ],
    },
    name: {
      type: 'string',
      minLength: 1,
      maxLength: 50,
    },
    name_kana: {
      anyOf: [
        {
          type: 'string',
          minLength: 1,
          maxLength: 50,
          pattern: '^[ァ-ヴー　]+$',
        },
        {
          type: 'string',
          enum: ['']
        }
      ]
    },

    phone: {
      type: 'string',
      maxLength: 256,
    },
    expiry_date_start: {
      type: 'string',
      anyOf: [
        {
          format: 'slash-date',
        },
        {
          enum: [''],
        }
      ]
    },
    expiry_date: {
      type: 'string',
      anyOf: [
        {
          format: 'slash-date',
        },
        {
          enum: [''],
        }
      ]
    },
    note_1: {
      type: 'string',
      maxLength: 256,
    },
    note_2: {
      type: 'string',
      maxLength: 256,
    },
    enterprise_type: {
      type: 'string',
      enum: ['0', '5', '9'],
    },

    account_registration_status: {
      type: 'string',
      enum: ['0', '1'],
    },
    province_id: {
      type: 'string',
    },
    status: {
      type: 'string',
      enum: ['0', '1', '2'],
    },

  },
  checkDataCust: true,
  errorMessage: {
    properties: {
      urlcardid: 'URL+カードID',
      user_code: 'トレサビシステムユーザーID',
      init_password: '初期パスワード',
      enterprise_type: '事業者種別',
      enterprise_code: '届出番号',
      staff_type: '事業者/従事者区分',
      license_number: '許可番号',
      name: '氏名',
      name_kana: '氏名（カナ）',
      province_name: '都道府県',
      phone: '連絡先等',
      expiry_date_start: '許可の有効期間（開始）',
      expiry_date: '許可の有効期間（終了）',
      status: '状態',
      account_registration_status: 'アカウント登録状況',
      note_1: '備考1',
      note_2: '備考2',
    }
  }
}