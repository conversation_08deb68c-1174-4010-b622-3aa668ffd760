<template>
  <q-page class="h-full tw:space-y-4 tw:mb-[18rem] tw:xl:mb-[7rem]">
    <div>
      <!-- filter start -->
      <ExpansionSearchItem v-model:expanded="isSearchExpanded">
        <SearchForm />
      </ExpansionSearchItem>
      <div
      v-if="
        searchConditions.enterpriseName ||
        searchConditions.name ||
        searchConditions.code ||
        searchConditions.licenseNumber ||
        (searchConditions.startDate && searchConditions.endDate)
      "
      class="tw:mt-4 tw:mb-4 tw:text-xs-design tw:flex tw:items-center tw:space-x-2"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]">検索条件</span>
      <span
        v-if="searchConditions.enterpriseName"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.enterpriseName }}
      </span>
      <span
        v-if="searchConditions.name"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.name }}
      </span>
      <span
        v-if="searchConditions.startDate && searchConditions.endDate"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.startDate }} ~ {{ searchConditions.endDate }}
      </span>
      <span
        v-if="searchConditions.code"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.code }}
      </span>
      <span
        v-if="searchConditions.licenseNumber"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.licenseNumber }}
      </span>
    </div>

      <!-- filter end -->
      <q-card class="tw:mt-4 tw:mb-4 tw:bg-white tw:p-4">
        <!-- sort btn start -->
        <div
          class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:border-neutral-500 tw:pt-1"
        >
          <div
            class="tw:flex tw:items-start tw:tl:flex-row tw:flex-col tw:space-y-4"
          >
            <div
              class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]"
            >
              <span class="tw:text-xs-design">表示件数</span>
              <PageSizeDropdownSP v-model="pageSize" />
            </div>
            <SortByDropdownSP v-model="sortModel"
              class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden"
            />
          </div>
          <div
            class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0"
          >
            <PaginationNotifi />
          </div>
        </div>
        <!-- sort btn end -->
        <!-- start table for tablet -->
        <div class="tw:hidden tw:tl:block q-py-sm">
          <q-table
            :rows="sortedRows"
            :columns="columns"
            row-key="index"
            hide-pagination
            bordered
            v-model:pagination="paginationComputed"
            :rows-per-page-options="[50]"
          >
            <template v-slot:header="props">
              <q-tr
                :props="props"
                class="tw:text-s-design"
                :class="`tw:bg-[#E2E3EA]`"
              >
                <q-th
                  @click="handleClickSort(props.cols[0].name)"
                  class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2]"
                >
                  {{ props.cols[0].label }}
                  <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
                </q-th>
                <q-th
                  @click="handleClickSort(props.cols[1].name)"
                  class="tw:text-s-design tw:font-bold tw:text-center tw:dt:w-[15rem] tw:tl:w-[15rem] tw:border-r tw:border-[#D2D2D2]"
                >
                  {{ props.cols[1].label }}
                  <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
                </q-th>
                <q-th
                  @click="handleClickSort(props.cols[2].name)"
                  class="tw:text-s-design tw:font-bold tw:text-left tw:dt:w-[16rem] tw:tl:w-[16rem] tw:border-r tw:border-[#D2D2D2]"
                >
                  {{ props.cols[2].label }}
                  <q-icon
                  v-if="getSortOrder(props.cols[2].name)"
                  :name="
                    getSortOrder(props.cols[2].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
                </q-th>
                <q-th
                  @click="handleClickSort(props.cols[3].name)"
                  class="tw:text-s-design tw:font-bold tw:text-left tw:dt:w-[16rem] tw:tl:w-[16rem]"
                >
                  {{ props.cols[3].label }}
                  <q-icon
                  v-if="getSortOrder(props.cols[3].name)"
                  :name="
                    getSortOrder(props.cols[3].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
                </q-th>
              </q-tr>
            </template>
            <template v-slot:body="props">
              <q-tr
                class="tw:cursor-pointer tw:w-full"
                :props="props"
                @click.prevent="handleSelectInventory(_, props.row)"
              >
                <q-td
                  key="title"
                  :props="props"
                  class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:font-bold tw:border-r tw:border-[#D2D2D2]"
                >
                  <div
                    :class="`tw:truncate tw:tl:max-w-[300px] tw:lg:max-w-[350px]
                    tw:xl:max-w-[540px] tw:dt:max-w-[490px] tw:underline tw:underline-offset-1 tw:text-[#004AB9]`"
                  >
                    {{ props.row?.title }}
                  </div>
                </q-td>
                <q-td
                  key="createdOn"
                  :props="props"
                  class="text-center tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
                >
                  <div class="tw:truncate tw:dt:w-[15rem] tw:tl:w-[15rem]">
                    {{ props.row.createdOn }}
                  </div>
                </q-td>
                <q-td
                  key="netWeightInventory"
                  :props="props"
                  class="text-right tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
                >
                  <div class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]">
                    {{ FORMAT_NUMBER(props.row.netWeightInventory) }}g
                  </div>
                </q-td>
                <q-td
                  key="newNetWeightInventory"
                  :props="props"
                  class="text-right tw:text-s-design"
                >
                  <div class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]">
                    {{ FORMAT_NUMBER(props.row.newNetWeightInventory) }}g
                  </div>
                </q-td>
              </q-tr>
            </template>
            <template v-slot:no-data="">
              <div
                class="tw:w-full tw:text-center tw:text-s-design"
              >
                データが見つかりません。
              </div>
            </template>
          </q-table>
        </div>
        <!-- start table for smartphone -->
        <div class="tw:block tw:tl:hidden q-py-sm">
          <q-table
            grid
            card-container-class="tw:flex-col tw:gap-1"
            card-class="tw:w-full"
            :rows="inventoryListData"
            v-model:pagination="paginationComputed"
            row-key="id"
            hide-pagination
            hide-header
          >
            <template v-slot:item="props">
              <div
                class="tw:w-full"
                @click.prevent="handleSelectInventory(_, props.row)"
              >
                <q-card flat bordered>
                  <q-card-section class="tw:text-left tw:cursor-pointer">
                    <div
                      class="tw:flex-1 tw:flex tw:items-center tw:justify-between"
                    >
                      <div>
                        <strong class="tw:text-s-design tw:text-[#004AB9]">
                          {{ props.row.title }}</strong
                        >
                        <br />
                        <div class="tw:text-[#333333] tw:text-s-design">
                          <span>修正日: {{ `${props.row.createdOn}` }}</span>
                          <br />
                          <span
                            >修正前の在庫量:
                            {{ `${props.row.netWeightInventory}g` }}</span
                          >
                          <br />
                          <span
                            >修正後の在庫量:
                            {{ `${props.row.newNetWeightInventory}g` }}</span
                          >
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </template>
            <template v-slot:no-data="">
              <div
                :class="`tw:w-full tw:text-center tw:text-s-design`"
              >
                データが見つかりません。
              </div>
            </template>
          </q-table>
        </div>
        <!-- table end -->
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0"
        >
          <PaginationNotifi />
        </div>
      </q-card>
    </div>
    <div class="tw:relative tw:h-0 tw:overflow-hidden">
      <InventoryEditedListPdf />
    </div>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
      tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
        label="在庫管理に戻る"
        @click.prevent="goToPage('inventoryList')"
      />
      <div
        class="tw:gap-4 tw:flex tw:flex-col tw:justify-center tw:items-center tw:tl:flex-row tw:w-full tw:tl:w-[60rem]"
      >
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[31.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
          label="実績をPDFでダウンロード"
          @click.prevent="exportPdfHandler"
          :loading="isDownloading && downloadType === 'pdf'"
        />
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[32.6rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
          label="実績をCSVでダウンロード"
          @click.prevent="exportCsvHandler"
          :loading="isDownloading && downloadType === 'csv'"
        />
      </div>
    </q-footer>
  </q-page>
</template>

<script setup>
// #region import
import useValidate from 'composables/validate';
import {
  FORMAT_DATE,
  FORMAT_NUMBER,
  exportCSV,
  FORMAT_DATE_TIME_CSV,
} from 'helpers/common';
import { SORT_BY_INVENTORY_ENUM } from 'helpers/constants';
import searchInventoryCondition from 'schemas/inventory-management/searchInventoryCondition.schema';
import inventoryManagementService from 'services/inventoryManagement.service';
import { computed, nextTick, onMounted, provide, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import { useAppStore } from 'stores/app-store';
import { isEqual, orderBy } from 'lodash';
import { useLocalStorage } from 'composables/localstorage';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import SortByDropdownSP from 'src/components/SortByDropdownSP.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';
import InventoryEditedListPdf from 'src/components/pdf/InventoryEditedListPdf.vue';
import html2pdf from 'html2pdf.js';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'src/helpers/constants';

import SearchForm from './components/SearchForm.vue';

// #endregion

// #region state
const { setLoadingManual, setLoading } = useAppStore();

const router = useRouter();
const { validateData: validateSearchForm, errors: errorsSearchForm } =
  useValidate();
const { listPageQueryParams } = useLocalStorage();
const isSearchExpanded = ref(false);
const route = useRoute();
const isFirstLoad = ref(true);

const searchConditions = computed(() => {
  const query = route.query;
  return {
    enterpriseName: query.enterpriseName || '',
    name: query.name || '',
    startDate: query.startDate || '',
    endDate: query.endDate || '',
    code: query.code || '',
    licenseNumber: query.licenseNumber || '',
  };
});

const columns = [
  {
    name: 'title',
    label: 'グループ名',
    align: 'left',
    field: 'title',
    sortable: true,
  },
  {
    name: 'createdOn',
    label: '修正日',
    align: 'center',
    field: 'createdOn',
    sortable: true,
  },
  {
    name: 'netWeightInventory',
    align: 'right',
    label: '修正前の在庫量',
    field: 'netWeightInventory',
    format: val => `${val}g`,
    sortable: true,
  },
  {
    name: 'newNetWeightInventory',
    align: 'center',
    label: '修正後の在庫量',
    field: 'newNetWeightInventory',
    format: val => `${val}g`,
    sortable: true,
  },
];
const sortModel = ref({});
const pagination = ref({
  sortBy: listPageQueryParams.value?.inventoryEditedList?.sortBy ||
    SORT_BY_INVENTORY_ENUM.CREATED_ON,
  descending: true,
  page: listPageQueryParams.value?.inventoryEditedList?.page || 1,
  limit: listPageQueryParams.value?.inventoryEditedList?.limit || 10,
});
const searchFormData = ref({
  enterpriseName: '',
  name: '',
  startDate: '',
  endDate: '',
  code: '',
  licenseNumber: '',
});
const totalPage = ref(0);
const pageSize = ref(router.currentRoute.value.query.limit || 10);
const pageIndex = ref(1);
const sortBySelectedLabel = ref('');
const inventoryListData = ref([]);
const multiSortConditions = ref([]);
// Download states
const isDownloading = ref(false);
const downloadType = ref('');
const flagDownload = ref(false);
const dataExport = ref([]);
const dataExportPdf = ref([]);
// for manage state of has different page index
const searchForm = ref({
  enterpriseName: '',
  name: '',
  startDate: '',
  endDate: '',
  code: '',
  licenseNumber: '',
});

// #endregion

// #region export functions
const getDiffTypeText = type => {
  switch (type) {
    case TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH:
      return '死亡';
    case TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR:
      return '計測ミス';
    case TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER:
      return 'その他';
    default:
      return '';
  }
};

const getDataExport = async () => {
    flagDownload.value = false;

    const result = await inventoryManagementService.getInventoryEditedList({
      sortBy: pagination.value.sortBy,
      descending:
        pagination.value.sortBy === SORT_BY_INVENTORY_ENUM.GROUP
          ? false
          : pagination.value.descending,
      page: pagination.value.page,
      limit: pagination.value.limit,
      enterpriseName: searchFormData.value.enterpriseName || undefined,
      name: searchFormData.value.name || undefined,
      startDate: searchFormData.value.startDate || undefined,
      endDate: searchFormData.value.endDate || undefined,
      code: searchFormData.value.code || undefined,
      licenseNumber: searchFormData.value.licenseNumber || undefined,
    });
    if (result.code !== 0) {
      return;
    }

    flagDownload.value = true;
    dataExport.value = result.payload.items.map(item => ({
      title: item.inventory.group_name ?? item.inventory?.fallback_group_name,
      createdOn: FORMAT_DATE(item.created_on || ''),
      netWeightInventory: FORMAT_NUMBER(item.net_weight_inventory),
      newNetWeightInventory: FORMAT_NUMBER(item.new_net_weight_inventory),
      id: item.id,
      typeDiff: getDiffTypeText(+item.type_diff),
      reasonDiff: item.type_diff === TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER
        ? item.reason_diff : '',
    }));

    dataExportPdf.value = dataExport.value;

};

const exportCsvHandler = async () => {
    isDownloading.value = true;
    downloadType.value = 'csv';

    await getDataExport();
    if (!flagDownload.value) {
      return;
    }

    exportCSV(
      dataExport.value,
      [
        'createdOn',
        'title',
        'netWeightInventory',
        'newNetWeightInventory',
        'typeDiff',
        'reasonDiff',
      ],
      [
        '修正日時',
        '在庫ロット名',
        '修正前の在庫量[g]',
        '修正後の在庫量[g]',
        '修正理由',
        '修正内容詳細',
      ],
      `在庫修正実績_${FORMAT_DATE_TIME_CSV()}`
    );

    toast.access(
      MESSAGE.MSG_DOWNLOAD_CSV_SUCCESS ||
        'CSVファイルのダウンロードが完了しました。'
    );
    isDownloading.value = false;
    downloadType.value = '';
};

const exportPdfHandler = async () => {
    isDownloading.value = true;
    downloadType.value = 'pdf';

    await getDataExport();
    if (!flagDownload.value) {
      return;
    }

    nextTick(async () => {
      setLoadingManual(true);
      setLoading(true);
      const elementBody = document.getElementById('large-body-pdf');
      const rows = Array.from(elementBody.children);

      const worker = html2pdf().set({
        filename: `シラスウナギ出荷実績_${FORMAT_DATE_TIME_CSV()}.pdf`,
        margin: [0.5, 0.1, 0.6, 0.1],
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 4 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'landscape' },
      });

      const processBatches = async () => {
        for (let i = 0; i < rows.length; ) {
          let batchHeight = 0;
          let rowIndex = i;
          const batchElement = document.createElement('div');
          while (rowIndex < rows.length) {
            const row = rows[rowIndex];
            const rowHeight = row.getBoundingClientRect().height;
            if (batchHeight + rowHeight > 31000) {
              break;
            }
            batchHeight += rowHeight;
            rowIndex += 1;
            batchElement.appendChild(row.cloneNode(true));
          }

          const batchSize = rowIndex - i;
          i += batchSize;

          await worker
            .from(batchElement)
            .toContainer()
            .toCanvas()
            .toPdf()
            .get('pdf')
            .then(pdf => {
              if (i + batchSize < rows.length) {
                pdf.addPage();
              }
            });

          await worker.get('canvas').then(canvas => {
            // reset canvas size
            canvas.height = 0;
            canvas.width = 0;
            canvas.style.height = '0px';
            canvas.style.width = '0px';
          });
        }

        await worker.save();
        setLoading(false);
        setLoadingManual(false);
        isDownloading.value = false;
        downloadType.value = '';
        toast.access(MESSAGE.MSG_DOWNLOAD_SHIPPINGPDF_INFO);
      };

      await processBatches();
    });
};
// #endregion

// #region actions
const formatSearchFormToQuery = form => ({
  enterpriseName: form.enterpriseName || undefined,
  name: form.name || undefined,
  startDate: form.startDate || undefined,
  endDate: form.endDate || undefined,
  code: form.code || undefined,
  licenseNumber: form.licenseNumber || undefined,
});

const sortedRows = computed(() => {
  const rows = [...inventoryListData.value].map(row => ({
    ...row,
    createdOn: FORMAT_DATE(row.createdOn || ''),
    netWeightInventory: +row.netWeight,
    newNetWeightInventory: +row.newNetWeight,
  }));

  const lstKey = [];
  const lstOrder = [];
  for (const { key, order } of multiSortConditions.value) {
    lstKey.push(key);
    lstOrder.push(order);
  }
  return orderBy(rows, lstKey, lstOrder);
});

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }
};

const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const getShipmentList = async () => {
  const inventoryListResponse =
    await inventoryManagementService.getInventoryEditedList({
      ...pagination.value,
      enterpriseName: searchFormData.value.enterpriseName || undefined,
      name: searchFormData.value.name || undefined,
      startDate: searchFormData.value.startDate || undefined,
      endDate: searchFormData.value.endDate || undefined,
      code: searchFormData.value.code || undefined,
      licenseNumber: searchFormData.value.licenseNumber || undefined,
    });

  if (inventoryListResponse) {
    inventoryListData.value = inventoryListResponse.payload.items.map(
      item => ({
        title: item.inventory.group_name ?? item.inventory?.fallback_group_name,
        id: item.id,
        netWeightInventory: item.net_weight_inventory,
        newNetWeightInventory: item.new_net_weight_inventory,
        createdOn: FORMAT_DATE(item.created_on || ''),
        netWeight: +item.net_weight_inventory,
        newNetWeight: +item.new_net_weight_inventory,
      })
    );

    if (window.innerWidth < 960) {
      let key;
      let order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'created_on':
          key = 'createdOn';
          order = 'desc';
          break;
        case 'group_name':
          key = 'title';
          break;
        case 'net_weight_inventory':
          key = 'netWeight';
          break;
        case 'new_net_weight_inventory':
          key = 'newNetWeight';
          break;
        default:
          break;
      }

      inventoryListData.value = orderBy(inventoryListData.value, [key], [order]);
    }

    totalPage.value = Math.ceil(
      (inventoryListResponse.payload.total_item ?? 1) / pagination.value.limit
    );

    // check if page index is different from the value server return
    // if different, replace query to the new page index (value server return)
    if (inventoryListResponse.payload.page !== pageIndex.value) {
      router.replace({
        query: {
          page: inventoryListResponse.payload.page,
          ...pagination.value,
          ...formatSearchFormToQuery(searchFormData.value),
        },
      });
    }
  }
};

const handleSelectInventory = async (_, row) => {
  await router.push({
    name: 'inventoryEditedDetail',
    params: {
      id: row.id,
    },
  });
};

const goToPage = name => {
  router.push({ name });
};
// #endregion

// #region watch to get report list
watch(pageSize, async (newPageSize, oldPageSize) => {
  if (newPageSize === oldPageSize || isFirstLoad.value) {
    return;
  }

  pagination.value.limit = +newPageSize;
  totalPage.value = Math.ceil(inventoryListData.value.length / +newPageSize);

  if (listPageQueryParams.value.inventoryEditedList) {
    listPageQueryParams.value.inventoryEditedList.limit = +newPageSize;
  } else {
    listPageQueryParams.value.inventoryEditedList = {
      limit: +newPageSize,
    };
  }

  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
    page: 1,
  };

  router.push({
    query: {
      ...query,
    },
  });
});

watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.descending +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.limit +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.name +
    router.currentRoute.value.query.code +
    router.currentRoute.value.query.licenseNumber +
    router.currentRoute.value.query.startDate +
    router.currentRoute.value.query.endDate,
  async () => {
    const query = router.currentRoute.value.query;

    if (!listPageQueryParams.value.inventoryEditedList) {
      listPageQueryParams.value.inventoryEditedList = {};
    }

    if (query.limit) {
      listPageQueryParams.value.inventoryEditedList.limit = +query.limit;
    }
    if (query.page) {
      listPageQueryParams.value.inventoryEditedList.page = +query.page;
    }

    pageSize.value = +(query.limit || 10);
    pageIndex.value = +(query.page || 1);

    sortBySelectedLabel.value = query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(item => item.value === query.sortBy)
          ?.label || '新着順'
      : '新着順';

    pagination.value = {
      ...pagination.value,
      page: +(query.page || 1),
      limit: +(query.limit || 10),
      sortBy: query.sortBy || SORT_BY_INVENTORY_ENUM.CREATED_ON,
      descending: query.descending === 'true',
    };

    const searchParams = {
      enterpriseName: query.enterpriseName || '',
      name: query.name || '',
      startDate: query.startDate || '',
      endDate: query.endDate || '',
      code: query.code || '',
      licenseNumber: query.licenseNumber || '',
    };

    searchForm.value = { ...searchParams };
    searchFormData.value = { ...searchParams };

    listPageQueryParams.value.inventoryEditedList = {
      ...listPageQueryParams.value.inventoryEditedList,
      ...searchParams,
      sortBy: query.sortBy || SORT_BY_INVENTORY_ENUM.CREATED_ON,
      descending: query.descending === 'true',
    };

    if (window.innerWidth < 960) {
      let key;
      let order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'created_on':
          key = 'createdOn';
          order = 'desc';
          break;
        case 'group_name':
          key = 'title';
          break;
        case 'net_weight_inventory':
          key = 'netWeight';
          break;
        case 'new_net_weight_inventory':
          key = 'newNetWeight';
          break;
        default:
          break;
      }

      inventoryListData.value = orderBy(inventoryListData.value, [key], [order]);
    }

    if (isFirstLoad.value) {
      await getShipmentList();
      isFirstLoad.value = false;
    }
  }
);

watch(
  () => pagination.value.limit,
  async () => {
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
      },
    });
  }
);
// #endregion

// #region computed
const paginationComputed = computed(() => ({
  page: pagination.value.page,
  rowsPerPage: pagination.value.limit,
}));
// #endregion
provide('inventoryEditedListExport', dataExport);
provide('inventoryEditedListExportPdf', dataExportPdf);
provide('formDataExport', searchFormData);

// #region provide
const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      enterpriseName: '',
      name: '',
      code: '',
      licenseNumber: '',
      startDate: '',
      endDate: '',
    };
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getShipmentList();
      isSearchExpanded.value = false;
      return;
    }

    const data = {
      ...searchForm.value,
      startDate: searchForm.value.startDate || '',
      endDate: searchForm.value.endDate || '',
    };
    const validate = validateSearchForm(searchInventoryCondition, data);

    if (validate) {
      pageIndex.value = 1;
      await router.push({
        query: {
          ...pagination.value,
          ...formatSearchFormToQuery(searchForm.value),
          page: 1,
        },
      });

      isSearchExpanded.value = false;
    }
  },
  title: '在庫修正実績をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: '新着順', value: SORT_BY_INVENTORY_ENUM.CREATED_ON },
    { label: 'グループ名順', value: SORT_BY_INVENTORY_ENUM.GROUP },
    { label: '修正前の在庫量順', value: SORT_BY_INVENTORY_ENUM.WEIGHT_INVENTORY },
    { label: '修正後の在庫量順', value: SORT_BY_INVENTORY_ENUM.NEW_WEIGHT_INVENTORY },
  ],
  sortBySelectedLabel,
  async handleClickSortByItem(option) {
    sortBySelectedLabel.value = option.label;
    pagination.value.sortBy = option.value;
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors: errorsSearchForm,
  forHistory: true,
};
provide('searchFormProvideData', searchFormProvideData);
// #endregion

// #region mounted
onMounted(async () => {
  if (!listPageQueryParams.value.inventoryEditedList) {
    listPageQueryParams.value.inventoryEditedList = {};
  }
  if (!router.currentRoute.value?.query?.page) {
    sortModel.value = {
      value: listPageQueryParams.value.inventoryEditedList.sortBy,
      label:
        sortByDropdownProvideData.sortByOptions.find(
          item => item.value === listPageQueryParams.value.inventoryEditedList.sortBy
        )?.label || '',
    };
    isFirstLoad.value = true;
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  } else {
    const query = router.currentRoute.value.query;
    pageSize.value = +(query.limit || 10);
    pageIndex.value = +(query.page || 1);

    sortBySelectedLabel.value = query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(item => item.value === query.sortBy)?.label
      : '';

    sortModel.value = {
      value: query.sortBy || SORT_BY_INVENTORY_ENUM.CREATED_ON,
      label: sortBySelectedLabel.value,
    };

    pagination.value = {
      ...pagination.value,
      page: +(query.page || 1),
      limit: +(query.limit || 10),
      sortBy: query.sortBy || SORT_BY_INVENTORY_ENUM.CREATED_ON,
      descending: query.descending === 'true',
    };

    const searchParams = {
      enterpriseName: query.enterpriseName || '',
      name: query.name || '',
      startDate: query.startDate || '',
      endDate: query.endDate || '',
      code: query.code || '',
      licenseNumber: query.licenseNumber || '',
    };

    searchForm.value = { ...searchParams };
    searchFormData.value = { ...searchParams };

    await getShipmentList();
    isFirstLoad.value = false;
  }
});
// #endregion
</script>

<style scoped></style>
