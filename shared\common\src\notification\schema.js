const { sharedSchema } = require('../validations/sharedSchema');
const { NOTIFICATION_STATUS_ENUM, ROLES_ENUM_OPTIONS_VALUES } = require('../helpers/enum');
const { MESSAGE } = require('../utils/message');

// TODO: Add message for each error in Japanese
const getNotificationListSchema = {
  summary: 'Notification',
  description: 'Get notifications list',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    additionalProperties: false,
    type: 'object',
    required: ['sortBy', 'descending', 'page', 'limit', 'status'],
    properties: {
      sortBy: {
        type: 'string',
        errorMessage: {
          _: 'Invalid sortBy value.',
        },
      },
      descending: {
        type: 'boolean',
        default: false,
        errorMessage: {
          _: 'Invalid descending value. It should be a boolean',
        },
      },
      page: {
        type: 'number',
        errorMessage: {
          _: 'Invalid page value. It should be a number',
        },
      },
      limit: {
        type: 'number',
        errorMessage: {
          _: 'Invalid limit value. It should be a number',
        },
      },
      status: {
        type: 'string',
        enum: Object.values(NOTIFICATION_STATUS_ENUM),
        default: NOTIFICATION_STATUS_ENUM.ALL,
        errorMessage: {
          _: 'Invalid status value. It should be one of all, read, unread',
        },
      },
    }
  }
};

const getNotificationDetailSchema = {
  summary: 'Notification',
  description: 'Get notification detail',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

const checkHasUnreadNotificationSchema = {
  summary: 'Notification',
  description: 'Check has unread notification',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const getNotificationListManagerSchema = {
  summary: 'Notification',
  description: 'Get notifications list for admin site manager',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    additionalProperties: false,
    type: 'object',
    required: ['page'],
    properties: {
      page: {
        type: 'number',
        errorMessage: {
          _: 'Invalid page value. It should be a number',
        },
      },
      limit: {
        type: 'integer',
        default: 10,
        errorMessage: {
          _: 'page size invalid',
        }
      },
      sortBy: {
        type: 'string',
        errorMessage: {
          _: 'Invalid sortBy value.',
        },
      },
      startDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      endDate: {
        type: 'string',
        format: 'slash-date',
        formatMinimum: {
          $data: '1/startDate',
        },
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          formatMinimum: 'End date must be greater than or equal to start date',
        },
      },
      creator: {
        type: 'string',
        minLength: 1,
        errorMessage: {
          _: 'Invalid creator value. It should be a string',
        },
      },
      title: {
        type: 'string',
        minLength: 1,
        errorMessage: {
          _: 'Invalid title value. It should be a string',
        },
      },
      roles: {
        oneOf: [
          {
            type: 'array',
            items: {
              type: 'string',
              enum: Object.values(ROLES_ENUM_OPTIONS_VALUES)
                .filter(value => value !== ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE),
            },
            minItems: 1,
            maxItems: 4,
            errorMessage: {
              minItems: 'Roles is required',
              maxItems: 'Roles is too long (maximum is 5 roles)',
              _: 'Invalid roles value. It should be an array of roles',
            },
          },
          {
            type: 'string',
            errorMessage: {
              _: 'Invalid roles value. It should be an array of roles',
            },
          },
        ],
      },
    },
  },
};

const registerNotificationSchema = {
  summary: 'Notification',
  description: 'Register notification',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: ['title', 'content', 'roles'],
    properties: {
      title: {
        type: 'string',
        minLength: 1,
        maxLength: 256,
        errorMessage: {
          minLength: 'Title is required',
          maxLength: 'Title is too long (maximum is 256 characters)',
          _: 'Title is required',
        },
      },
      content: {
        type: 'string',
        minLength: 1,
        maxLength: 10000,
        errorMessage: {
          minLength: 'Content is required',
          maxLength: 'Content is too long (maximum is 10000 characters)',
          _: 'Content is required',
        },
      },
      roles: {
        type: 'array',
        minItems: 1,
        maxItems: 4,
        items: {
          type: 'string',
          enum: Object.values(ROLES_ENUM_OPTIONS_VALUES)
            .filter(value => value !== ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE),
        },
        errorMessage: {
          minItems: 'Roles is required',
          maxItems: 'Roles is too long (maximum is 5 roles)',
          _: 'Roles is required',
        },
      },
    },
    errorMessage: {
      additionalProperties: 'No additional properties are allowed',
    },
  },
};

const getNotificationDetailManagerSchema = {
  summary: 'Notification',
  description: 'Get notification detail - admin site manager',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

const deleteNotificationSchema = {
  summary: 'Notification',
  description: 'Delete notification - for admin site manager',
  tags: ['Notification'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

module.exports = {
  getNotificationListSchema,
  getNotificationDetailSchema,
  checkHasUnreadNotificationSchema,
  getNotificationListManagerSchema,
  registerNotificationSchema,
  getNotificationDetailManagerSchema,
  deleteNotificationSchema,
};
