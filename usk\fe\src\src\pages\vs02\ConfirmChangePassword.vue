<template>
  <div class="tw:p-3 tw:tl:p-6">
    <!-- title -->
    <div class="tw:text-center tw:text-2xl tw:tl:text-4xl tw:p-5 tw:font-bold">
      パスワード再登録
    </div>
    <!-- content -->
    <div>
      <div class="tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]">
        パスワード(英数8文字以上組み合わせ)<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-3"
        outlined
        v-model="newPassword"
        autocomplete="nope"
        :type="showNewPassword ? 'text' : 'password'"
        lazy-rules
        maxlength="256"
        :error="!!errors.password"
        :error-message="errors.password"
        no-error-icon
      >
        <template v-slot:append>
          <q-icon
            :name="showNewPassword ? 'visibility' : 'visibility_off'"
            @click.prevent="showNewPassword = !showNewPassword"
          />
        </template>
      </q-input>
      <div
        class="tw:text-base tw:mt-5 tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        パスワード確認用<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-3"
        outlined
        v-model="comparePassword"
        autocomplete="nope"
        :type="showComparePassword ? 'text' : 'password'"
        lazy-rules
        maxlength="256"
        :error="!!errors.passwordConfirm"
        :error-message="errors.passwordConfirm"
        no-error-icon
      >
        <template v-slot:append>
          <q-icon
            :name="showComparePassword ? 'visibility' : 'visibility_off'"
            @click.prevent="showComparePassword = !showComparePassword"
          />
        </template>
      </q-input>
      <q-btn
        class="tw:w-full tw:mt-5 tw:text-base tw:font-bold tw:text-white
        tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
        label="登録する"
        type="button"
        :class="`tw:bg-${colorSub}`"
        @click.prevent="changePassword"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import resetPassword from 'schemas/resetPassword';
import authService from 'services/auth.service';

const { colorSub } = storeToRefs(useAppStore());
const { errors, validateData } = useValidate();
const router = useRouter();

// ===== REF =====
const newPassword = ref('');
const comparePassword = ref('');
const showNewPassword = ref(false);
const showComparePassword = ref(false);

// ===== METHOD =====
const changePassword = async () => {
  // change password
  const valid = validateData(resetPassword, {
    password: newPassword.value,
    passwordConfirm: comparePassword.value,
  });
  if (!valid) {
    return;
  }
  const result = await authService.resetPassword(
    { password: newPassword.value },
    router.currentRoute.value.params?.code
  );
  if (result.code === 0) {
    // Go to the complete change pass
    await router.push({ name: 'completeChangePassword' });
  }
};
</script>
