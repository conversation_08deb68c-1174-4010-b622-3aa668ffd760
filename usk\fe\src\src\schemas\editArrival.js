import MESSAGE from 'helpers/message';

const editArrival = {
  type: 'object',
  additionalProperties: false,
  required: ['arrivalDate', 'arrivalGrossWeight'],
  properties: {
    arrivalDate: {
      type: 'string',
      format: 'slash-date',
      minLength: 1,
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_ARRIVAL_DATE_ERROR,
      },
    },
    arrivalGrossWeight: {
      type: 'number',
      exclusiveMinimum: 0,
      maximum: 999999,
      errorMessage: {
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        exclusiveMinimum: MESSAGE.MSG_ARRIVAL_GROSS_WEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_ARRIVAL_GROSS_WEIGHT_ERROR,
      },
    },
    arrivalTareWeight: {
      type: 'number',
      minimum: 0,
      checkFormatDecimal: true,
      exclusiveMaximum: {
        $data: '1/arrivalGrossWeight',
      },
      maximum: 999999,
      errorMessage: {
        minimum: MESSAGE.MSG_ARRIVAL_TARE_WEIGHT_MIN_ERROR,
        exclusiveMaximum: MESSAGE.MSG_ARRIVAL_TARE_WEIGHT_MAX_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_ARRIVAL_TARE_WEIGHT_ERROR,
      },
    },
    arrivalQuantity: {
      type: 'number',
      minimum: 1,
      maximum: 9999999,
      errorMessage: {
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        minimum: MESSAGE.MSG_ARRIVAL_QUANTITY_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_ARRIVAL_QUANTITY_ERROR,
      },
    },
  },
};

export default editArrival;
