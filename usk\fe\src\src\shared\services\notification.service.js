import BaseService from 'services/base.service';

class Notification extends BaseService {
  // #region user normal site
  async getNotificationList(params) {
    try {
      return this.dao.getNotificationList(params);
    } catch (error) {
      return null;
    }
  }

  async getNotificationDetail(id) {
    try {
      return this.dao.getNotificationDetail(id);
    } catch (error) {
      return null;
    }
  }

  async checkHasUnreadNotification() {
    try {
      return this.dao.checkHasUnreadNotification();
    } catch (error) {
      return null;
    }
  }
  // #endregion user normal site

  // #region admin site
  async adminGetNotificationList(params) {
    try {
      return this.dao.adminGetNotificationList(params);
    } catch (error) {
      return null;
    }
  }

  async adminRegisterNotification(data) {
    try {
      return this.dao.adminRegisterNotification(data);
    } catch (error) {
      return null;
    }
  }

  async adminGetNotificationDetail(id) {
    try {
      return this.dao.adminGetNotificationDetail(id);
    } catch (error) {
      return null;
    }
  }

  async adminDeleteNotification(id) {
    try {
      return this.dao.adminDeleteNotification(id);
    } catch (error) {
      return null;
    }
  }
  // #endregion admin site
}

export default new Notification('notification');
