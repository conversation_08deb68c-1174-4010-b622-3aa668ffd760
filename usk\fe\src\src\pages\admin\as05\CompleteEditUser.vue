<template>
  <div class="tw:flex tw:justify-center tw:flex-col tw:items-center">
    <div class="tw:w-[40%] tw:min-w-[550px]">
      <div class="tw:text-base tw:mt-4 tw:text-left">
        以下の内容で登録します。
      </div>
      <!-- province -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 都道府県 </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showProvinceName(dataAdminEditUser.provinceId) }}
        </div>
      </div>
      <!-- staff type -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          事業者/従事者区分
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showRole(dataAdminEditUser) }}
        </div>
      </div>
      <!-- enterprise_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 届出番号 </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ dataAdminEditUser.enterpriseCode }}
        </div>
      </div>
      <!-- user_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> ユーザーID </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ dataAdminEditUser.userCode }}
        </div>
      </div>
      <!-- enterprise_name -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 事業者名 </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14 tw:break-words">
          {{ dataAdminEditUser.name }}
        </div>
      </div>
      <!-- enterprise_name_kana -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          事業者名（カナ）
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14 tw:break-words">
          {{ dataAdminEditUser.nameKana }}
        </div>
      </div>
      <!-- license_number -->
      <div
        class="tw:flex tw:flex-col tw:my-4"
        v-if="
          dataAdminEditUser.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
          dataAdminEditUser.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
        "
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 許可番号 </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14 tw:break-words">
          {{
            dataAdminEditUser.licenseNumber
          }}
        </div>
      </div>
      <!-- phone -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 連絡先等 </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14 tw:break-words">
          {{ dataAdminEditUser.phone }}
        </div>
      </div>
      <!-- expiry_date -->
      <div
        class="tw:flex tw:flex-col tw:my-4"
        v-if="
          dataAdminEditUser.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
          (dataAdminEditUser.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
            && dataAdminEditUser.startExpiryDate && dataAdminEditUser.endExpiryDate)
        "
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          採捕許可有効期間
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
            {{ dataAdminEditUser.startExpiryDate }}
            <span v-if="dataAdminEditUser.startExpiryDate && dataAdminEditUser.endExpiryDate">~</span>
            {{ dataAdminEditUser.endExpiryDate }}
        </div>
      </div>
      <!-- status user -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 状態 </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showStatus(dataAdminEditUser.status) }}
        </div>
      </div>
      <!-- status register -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          アカウント登録状況
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{
            showStatusRegister(dataAdminEditUser.account_registration_status)
          }}
        </div>
      </div>
      <!-- temporary password -->
      <div
        v-if="
          dataAdminEditUser.account_registration_status ===
          USER_STATUS_REGISTER_ENUM.TEMPORARY
        "
        class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 初期パスワード </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14 tw:break-words">
          {{ dataAdminEditUser.password }}
        </div>
      </div>
      <!-- official password -->
      <div
        v-if="
          dataAdminEditUser.account_registration_status ===
          USER_STATUS_REGISTER_ENUM.OFFICIAL
        "
        class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1"> パスワード </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          **********
        </div>
      </div>
      <!-- note 1 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 備考1 </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          <span>
            {{ dataAdminEditUser.note1 }}
          </span>
        </div>
      </div>
      <!-- note 2 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 備考2 </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          <span>
            {{ dataAdminEditUser.note2 }}
          </span>
        </div>
      </div>
      <!-- enable export function -->
      <div
        v-if="
          dataAdminEditUser.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
          dataAdminEditUser.staff_type === STAFF_TYPE_ENUM.ENTERPRISE
        "
        class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:font-[400] tw:text-base tw:pb-1"> 輸出機能 </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          <span>
            {{ showEnableExport(dataAdminEditUser.enable_export_function) }}
          </span>
        </div>
      </div>
      <!-- customization_item -->
      <div
        v-if="
          dataAdminEditUser.province_custom_data &&
          dataAdminEditUser.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
        "
      >
        <div
          class="tw:flex tw:flex-col tw:my-4"
          v-for="item in Object.keys(dataAdminEditUser.province_custom_data)"
          :key="item"
        >
          <span class="tw:font-[400] tw:text-base tw:pb-1">
            {{ item }}
          </span>
          <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
            <span class="tw:whitespace-pre-line">
              {{
                dataAdminEditUser.province_custom_data[item]?.replaceAll(
                  ';',
                  '\n'
                )
              }}
            </span>
          </div>
        </div>
      </div>
      <!-- button -->
      <div class="tw:flex tw:gap-3 tw:justify-center">
        <BaseButton
          outline
          padding="0.75rem"
          :class="`tw:bg-white tw:text-${colorMain} tw:w-40 tw:text-[1rem]`"
          label="いいえ"
          @click.prevent="goBack"
        />
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-40 tw:text-[1rem]`"
          label="はい"
          @click.prevent="editHandler"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
// #region import
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import {
  showStatus,
  showEnableExport,
  showRole,
  showStatusRegister,
} from 'src/helpers/common';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';
import {
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
  STAFF_TYPE_ENUM,
  USER_STATUS_REGISTER_ENUM,
} from 'src/helpers/constants';
import BaseButton from 'src/components/base/BaseButton.vue';
import regionsService from 'src/shared/services/regions.service';
// #endregion import

const router = useRouter();
const { colorMain, colorSub, dataAdminEditUser } = storeToRefs(useAppStore());
const { setAdminEditUser } = useAppStore();
const userId = ref('');
const listProvince = ref([]);

const goBack = () => {
  router.back();
};

const showProvinceName = provinceId => {
  if (!listProvince.value || !Array.isArray(listProvince.value)) {
    return '';
  }
  const findProvince = listProvince.value.find(p => p.value === +provinceId);
  return findProvince ? findProvince.label : '';
};

const editHandler = async () => {
  if (!dataAdminEditUser.value.province_custom_data) {
    delete dataAdminEditUser.value.province_custom_data;
  }
  const payload = {
    ...dataAdminEditUser.value,
    startExpiryDate: dataAdminEditUser.value.startExpiryDate || null,
    endExpiryDate: dataAdminEditUser.value.endExpiryDate || null,
  };
  const result = await userAdminService.editUser(
    userId.value,
    payload
  );
  if (result.code === 0) {
    setAdminEditUser({});
    toast.access(MESSAGE.MSG_EDIT_EMPLOYEE_INFO_BY_ADMIN);
    router.push({
      name: 'adminUserDetail',
      params: {
        id: userId.value,
      },
    });
  }
};

// onMounted
onMounted(async () => {
  userId.value = router.currentRoute.value.params?.id;
  const listRegions = await regionsService.getRegionWithProvinces();
  listProvince.value = listRegions.payload.items.flatMap(item =>
    item.province.map(province => ({
      label: province.name,
      value: province.id,
    })));
});
</script>
