const { PrismaClient } = require('@prisma/client');
const { faker } = require('@faker-js/faker/locale/ja');

const prisma = new PrismaClient();

async function main() {
  await prisma.$transaction(async (tx) => {
    // clear all data in the database
    const data = Array.from({ length: 400 }).map(() => {
      return {
        code: `0206011241118${faker.number.int({
          min: 100,
          max: 999,
        })}`,
        shipping_date: '2024-11-18T01:20:07.000Z',
        shipping_gross_weight: 1000.0,
        shipping_tare_weight: 0.0,
        shipping_net_weight: 1000.0,
        shipping_quantity: 5000,
        arrival_gross_weight: null,
        arrival_tare_weight: null,
        arrival_net_weight: null,
        arrival_quantity: null,
        arrival_date: null,
        type_diff: null,
        shipping_type: 1,
        reason_diff: null,
        created_by_id: 177,
        latest_updated_by_id: null,
        created_on: '2024-11-18T01:20:07.080Z',
        latest_updated_on: null,
        delete_flag: false,
        qr_code: faker.string.alphanumeric(16),
        destination_enterprise_id: 1003,
        starting_enterprise_id: 67,
        inventory_id: null,
        ingredient:
          '[{"shipping_quantity": 5000, "shipping_tare_weight": 0, "shipping_gross_weight": 1000}]',
        setting: '{"unit_per_gram": 0.2, "display_shipment_weight": true}',
        destination_user_id: 1008,
        starting_user_id: 177,
      };
    });

    await tx.the_origins.createMany({
      data,
    })
  });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
