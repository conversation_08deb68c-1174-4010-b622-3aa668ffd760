const {
  sharedSchema,
} = require('../validations');
const { MESSAGE } = require('../utils/message');
const { PARTNER_TYPE_ENUM, ENTERPRISE_TYPE_ENUM } = require('../helpers/enum');

const searchLocatedTransactionSchema = {
  summary: 'Search located transaction',
  description: 'Search list located transaction',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  query: {
    type: 'object',
    properties: {
      page: {
        type: 'integer',
        default: 1,
        errorMessage: {
          _: MESSAGE.MSG_PAGE_INDEX_INVALID,
        }
      },
      limit: {
        type: 'integer',
        default: 10,
        errorMessage: {
          _: MESSAGE.MSG_PAGE_SIZE_INVALID,
        }
      },
      name: {
        type: 'string',
        errorMessage: {
          _: MESSAGE.MSG_NAME_INVALID
        }
      },
      enterpriseName: {
        type: 'string',
        errorMessage: {
          _: MESSAGE.MSG_NAME_INVALID
        }
      },
      licenseNumber: {
        type: 'string',
        errorMessage: {
          _: MESSAGE.MSG_NAME_INVALID
        }
      },
      partnerType: {
        type: 'integer',
        enum: [PARTNER_TYPE_ENUM.SUPPLIER, PARTNER_TYPE_ENUM.SHIPPER],
        errorMessage: {
          _: MESSAGE.MSG_PARTNER_TYPE_INVALID
        }
      },
      hiddenFlag: {
        type: 'boolean',
        errorMessage: {
          _: MESSAGE.MSG_HIDDEN_FLG_INVALID
        }
      },
      code: {
        type: 'string',
        pattern: '^\\d+$',
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR,
        },
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const getPartnerHybridListSchema = {
  summary: 'Get partner hybrid list',
  description: 'Get partner hybrid list',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  query: {
    additionalProperties: false,
    type: 'object',
    required: [],
    properties: {
      sortBy: {
        type: 'string',
        errorMessage: {
          _: 'Invalid sortBy value.',
        },
      },
      page: {
        type: 'number',
        errorMessage: {
          _: 'Invalid page value. It should be a number',
        },
      },
      rowsPerPage: {
        type: 'number',
        errorMessage: {
          _: 'Invalid limit value. It should be a number',
        },
      },
      type: {
        type: 'integer',
        enum: Object.values(ENTERPRISE_TYPE_ENUM),
      },
      name: {
        type: 'string',
        maxLength: 50,
      },
      userCode: {
        type: 'string',
        maxLength: 12,
        minLength: 1,
        pattern: '^[0-9,-]{0,12}$',
      },
      licenseNumber: {
        type: 'string',
        maxLength: 256,
      },
    }
  },
  response: sharedSchema.response,
};

const getPartnerHybridDetailSchema = {
  summary: 'Get partner hybrid detail',
  description: 'Get partner hybrid detail',
  tags: ['Partner'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
}

const registerPartnerHybridSchema = {
  summary: 'Register partner hybrid',
  description: 'Register partner hybrid',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  body: {
    additionalProperties: false,
    type: 'object',
    required: ['partner_id', 'partner_type'],
    properties: {
      partner_id: {
        type: 'integer',
        errorMessage: {
          _: 'Invalid partner_id value. It should be an integer',
        },
      },
      partner_type: {
        type: 'array',
        minItems: 1,
        items: {
          type: 'integer',
          enum: Object.values(PARTNER_TYPE_ENUM),
        },
        errorMessage: {
          minItems: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
  },
  response: sharedSchema.response,
};

const registerOneLocatedTransactionHandMadeSchema = {
  summary: 'Register located transaction',
  description: 'Insert one located transaction',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  body: {
    required: ['enterpriseType', 'enterpriseName', 'enterpriseCode', 'partnerType', 'province'],
    type: 'object',
    properties: {
      enterpriseType: {
        type: 'number',
        enum: Object.values(ENTERPRISE_TYPE_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      province: {
        type: 'number',
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      enterpriseName: {
        type: 'string',
        minLength: 1,
        maxLength: 50,
        errorMessage: {
          minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
          maxLength: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        }
      },
      enterpriseNameKana: {
        type: 'string',
        pattern: '^[ァ-ヴー　]+$',
        maxLength: 50,
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_KANA_ERROR
        },
      },
      enterpriseCode: {
        type: 'string',
        pattern: '^[0-9]{7}$',
        minLength: 1,
        errorMessage: {
          minLength: MESSAGE.MSG_LIMITS_NOTIFICATIONUMBER_ERROR,
          pattern: MESSAGE.MSG_LIMITS_NOTIFICATIONUMBER_ERROR,
        }
      },
      partnerType: {
        type: 'array',
        items: {
          type: 'integer',
          enum: Object.values(PARTNER_TYPE_ENUM),
        },
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR
        }
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const toggleShowSchema = {
  summary: 'Toggle show',
  description: 'Toggle show',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  response: sharedSchema.response,
};

const getLocatedTransactionDetailSchema = {
  summary: 'Get located transaction detail',
  description: 'Get located transaction detail',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
  response: sharedSchema.response,
}

const updateLocatedTransactionSchema = {
  summary: 'Update located transaction',
  description: 'Update located transaction',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  body: {
    required: ['partnerType'],
    type: 'object',
    properties: {
      partnerType: {
        type: 'array',
        minItems: 1,
        items: {
          type: 'number',
          enum: Object.values(PARTNER_TYPE_ENUM),
        },
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
}

const deleteLocatedTransactionSchema = {
  summary: 'Delete located transaction',
  description: 'Delete located transaction',
  tags: ['Partner'],
  security: [{ 'bearToken': [] }],
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
  response: sharedSchema.response,
}

module.exports = {
  searchLocatedTransactionSchema,
  getPartnerHybridListSchema,
  toggleShowSchema,
  registerOneLocatedTransactionHandMadeSchema,
  getLocatedTransactionDetailSchema,
  updateLocatedTransactionSchema,
  deleteLocatedTransactionSchema,
  getPartnerHybridDetailSchema,
  registerPartnerHybridSchema,
}