<template>
  <q-input
    v-model="model"
    maxlength="256"
    mask="date"
    bg-color="white"
    outlined
    input-class="tw:text-l-design tw:font-normal"
    autocomplete="off"
    no-error-icon
  >
    <template v-slot:append>
      <q-icon name="event" class="cursor-pointer">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-date v-model="model" :color="colorMain">
            <div class="row items-center justify-end">
              <q-btn
                v-close-popup
                label="閉じる"
                flat
                :class="`tw:bg-${colorSub}`"
                class="tw:text-white"
              />
            </div>
          </q-date>
        </q-popup-proxy>
      </q-icon>
    </template>
  </q-input>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';

const { colorMain, colorSub } = storeToRefs(useAppStore());

const model = defineModel();
</script>

<style scoped>
  :deep(.q-date__content) {
    width: 100%;
  }
</style>
