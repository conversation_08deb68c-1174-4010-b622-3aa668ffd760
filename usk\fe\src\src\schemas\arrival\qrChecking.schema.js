import MESSAGE from 'helpers/message';

export const checkQrArrivalSchema = {
  type: 'object',
  additionalProperties: false,
  required: ['qrCode'],
  properties: {
    qrCode: {
      type: 'string',
      pattern: '^[A-Za-z0-9]{16}$',
      errorMessage: {
        _: MESSAGE.MSG_INVALID_QRCODE_ERROR,
      },
    },
  },
};

export const checkQrUserSchema = {
  type: 'object',
  additionalProperties: false,
  required: ['qrCode'],
  properties: {
    qrCode: {
      type: 'string',
      pattern: `^${process.env.SITE_URL}/login/[a-zA-Z0-9]{16,16}$`,
      errorMessage: {
        _: MESSAGE.MSG_INVALID_QRCODE_ERROR,
      },
    },
  },
};
