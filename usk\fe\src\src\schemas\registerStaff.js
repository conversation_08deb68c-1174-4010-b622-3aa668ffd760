import MESSAGE from 'helpers/message';

const registerStaff = {
  additionalProperties: false,
  type: 'object',
  required: ['name', 'password', 'passwordConfirm'],
  properties: {
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    phone: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
      },
    },
    name: {
      type: 'string',
      minLength: 1,
      // pattern: '^[ａ-ｚＡ-Ｚ０-９、。，．・「」『』〜｛｝［］：；！？］＊＆＠＃％＋＝＞＜ぁ-んァ-ン一-龯]+$',
      maxLength: 50,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        maxLength: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    nameKana: {
      type: 'string',
      oneOf: [
        {
          minLength: 0,
          maxLength: 0,
        },
        {
          maxLength: 50,
          pattern: '^[ァ-ヴー　]+$',
        },
      ],
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_KANA_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
    },
    note2: {
      type: 'string',
      maxLength: 256,
    },
    password: {
      type: 'string',
      maxLength: 256,
      minLength: 1,
      pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
      },
    },
    passwordConfirm: {
      type: 'string',
      maxLength: 256,
      minLength: 1,
      const: {
        $data: '1/password',
      },
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        const: MESSAGE.MSG_NOTMATCH_PASSWARD_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      name: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      password: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      passwordConfirm: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default registerStaff;
