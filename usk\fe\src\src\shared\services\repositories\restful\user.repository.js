import BaseRepository from './base.repository';

class UserRepository extends BaseRepository {
  constructor() {
    super('users');
  }

  checkUserId(userId) {
    return this.client.get(`/check-userid/${userId}`);
  }

  linkUserId(body) {
    return this.client.post('/link-userid', body);
  }

  addUser(body) {
    return this.client.post('/add-user', body);
  }
}

export default new UserRepository();
