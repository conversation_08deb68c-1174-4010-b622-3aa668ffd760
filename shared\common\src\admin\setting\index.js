// ======== IMPORT ========================
const {
  getSystemSettingsSchema,
  updateSystemSettingsSchema,
  getSystemSettingsForNormalUserSchema,
} = require('./schemas');
const { auth, apiKeyVerify, authAdmin } = require('../../base/authorized');

// ===== 1. Injection ==============
// ===== 2. Router Functions ==============
async function getSystemSettingsHandler(request, reply) {
  const systemSettingsService = request.diScope.resolve(
    'adminSettingService'
  );
  const response = await systemSettingsService.getAdminSettings();
  return reply.send(response);
}

async function getSystemSettingsForNormalUserHandler(request, reply) {
  const systemSettingsService = request.diScope.resolve(
    'adminSettingService'
  );
  const response = await systemSettingsService.getSystemSettingsForNormalUser();
  return reply.send(response);
}

async function updateSystemSettingsHandler(request, reply) {
  const systemSettingsService = request.diScope.resolve(
    'adminSettingService'
  );
  const response = await systemSettingsService.updateSystemSettings(
    request.user,
    request.body
  );
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/get',
    {
      schema: getSystemSettingsSchema,
      onRequest: [apiKeyVerify, auth, authAdmin],
    },
    getSystemSettingsHandler
  );

  fastify.put(
    '/update',
    {
      schema: updateSystemSettingsSchema,
      onRequest: [apiKeyVerify, auth, authAdmin],
    },
    updateSystemSettingsHandler
  );

  fastify.get(
    '/get-for-normal-user',
    {
      schema: getSystemSettingsForNormalUserSchema,
      onRequest: [apiKeyVerify, auth],
    },
    getSystemSettingsForNormalUserHandler
  );
};
