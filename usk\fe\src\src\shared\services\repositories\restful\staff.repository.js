import BaseRepository from './base.repository';

class StaffRepository extends BaseRepository {
  constructor() {
    super('staff');
  }

  getStaff(query) {
    return this.client.get('/search-all', query);
  }

  registerStaff(body) {
    return this.client.post('/register', body);
  }

  editStaff(id, body) {
    return this.client.post(`/edit/${id}`, body);
  }

  delStaff(id) {
    return this.client.delete(`/del/${id}`);
  }

  getOneStaff(id) {
    return this.client.get(`/${id}`);
  }
}

export default new StaffRepository();
