AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Parameters:
  StageName:
    Type: String
    Default: staging
  SecurityGroupIds:
    Type: String
    Default: ''
  SubnetIds:
    Type: String
    Default: ''
  S3Bucket:
    Type: String
    Default: ''
  ScheduleExpression:
    Type: String
    Default: 'cron(0 15 * * ? *)'
    Description: 'Schedule expression for the Lambda function - runs at 0:00 JST (15:00 UTC)'
  DraftRetentionDays:
    Type: String
    Default: '7'
    Description: 'Number of days to retain DRAFT records'
  CompletedRetentionDays:
    Type: String
    Default: '30'
    Description: 'Number of days to retain COMPLETED records'
  EnableSchedule:
    Type: String
    Default: 'true'
    AllowedValues: ['true', 'false']
    Description: 'Enable or disable the scheduled execution'

Globals:
  Function:
    Runtime: nodejs20.x
    AutoPublishAlias: live
    DeploymentPreference:
      Type: AllAtOnce
    CodeUri: src/
    Timeout: 900
    MemorySize: 4096
    Environment:
      Variables:
        StageName:
          Ref: StageName
        SecurityGroupIds:
          Ref: SecurityGroupIds
        SubnetIds:
          Ref: SubnetIds
        DAFT:
          Ref: DraftRetentionDays
        COMPLETE:
          Ref: CompletedRetentionDays
    VpcConfig:
        SecurityGroupIds: !Split [ '|', !Ref SecurityGroupIds ]
        SubnetIds: !Split [ '|', !Ref SubnetIds ]

Resources:
  awsdelcodesuffixfn:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: awss3_delcodesuffix_fn
      Handler: index.handler
      Policies:
        - AmazonRDSFullAccess
        - AmazonEC2FullAccess
        - AmazonSESFullAccess
        - AmazonSSMFullAccess
        - AWSLambdaBasicExecutionRole
        - AWSLambdaRole
        - AWSLambdaVPCAccessExecutionRole
      Events:
        ScheduledEvent:
          Type: Schedule
          Properties:
            Schedule: !Ref ScheduleExpression
            Enabled: !Ref EnableSchedule
            Description: 'Scheduled execution for batch deletion of code suffixes'