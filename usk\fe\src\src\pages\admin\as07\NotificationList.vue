<template>
  <div class="tw:mx-10 tw:mt-3 tw:border-b tw:pb-3 tw:border-gray">
    <SearchForm />
    <div class="tw:flex tw:justify-end tw:gap-2">
      <BaseButton
        outline
        padding="0.75rem"
        :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
        label="クリア"
        @click.prevent="handleClickClear"
      />
      <BaseButton
        padding="0.75rem"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
        label="検索"
        @click.prevent="handleClickSearch"
      />
    </div>
  </div>

  <div class="tw:mx-10 tw:mt-3">
    <div class="tw:flex tw:justify-end tw:mb-3">
      <BaseButton
        padding="0.75rem 1rem"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:text-[1rem]`"
        label="お知らせを作成する"
        @click.prevent="handleClickCreateNotification"
      />
    </div>
    <BaseTable
      :rows="notificationListData"
      :columns="columns"
      v-model:pagination="paginationComputed"
      row-key="id"
    >
      <template v-slot:header>
        <q-tr :class="`table-header tw:bg-${colorMain} tw:text-[white]`">
          <q-th> 件名 </q-th>
          <q-th> 作成日時 </q-th>
          <q-th> 作成者ユーザーID </q-th>
          <q-th colspan="4" class="tw:px-0 tw:pt-2">
            <div class="tw:text-center">事業者区分</div>
            <div class="tw:grid tw:grid-cols-4 tw:px-2">
              <div
                class="tw:text-center"
                v-for="(item, index) in userRoleOptions"
                :key="index"
              >
                {{ item.label }}
              </div>
            </div>
          </q-th>
        </q-tr>
      </template>
      <template v-slot:body="data">
        <q-tr
          class="table-body tw:cursor-pointer"
          :props="data.props"
          @click="handleClickRow(_, data.props.row)"
        >
          <q-th
            v-for="(col, index) in data.props.cols"
            :key="col.name"
            :props="data.props"
            :class="`${index ? 'tw:min-w-[80px]' : 'tw:w-[450px]'}`"
          >
            <div></div>
            <div v-if="index < 3" :class="{'tw:max-w-[420px] tw:truncate': index === 0}">
              {{ data.props.row[col.name] }}
            </div>
            <div v-else class="tw:flex tw:justify-center">
              <q-icon
                v-if="data.props.row[col.name]"
                class="tw:block"
                name="check_circle"
                style="color: #3f83f8; font-size: 1.3rem"
              />
              <div v-else class="tw:w-[20px]"></div>
            </div>
          </q-th>
        </q-tr>
      </template>
    </BaseTable>

    <div class="tw:justify-end tw:flex tw:my-2">
      <PaginationItem />
    </div>
  </div>
</template>

<script setup>
import {
  provide, ref, computed, onMounted, watch,
} from 'vue';
import useValidate from 'composables/validate';
import { useAppStore } from 'src/stores/app-store';
import { storeToRefs } from 'pinia';
import BaseButton from 'components/base/BaseButton.vue';
import searchNotificationConditionSchema from 'src/schemas/admin/notification/searchNotificationCondition.schema';
import notificationService from 'src/shared/services/notification.service';
import BaseTable from 'components/base/BaseTable.vue';
import PaginationItem from 'components/PaginationItem.vue';
import { useRouter } from 'vue-router';
import { FORMAT_DATE } from 'src/helpers/common';
import { USER_ROLE_NOTIFY_OPTIONS } from 'src/helpers/constants';
import { isEqual } from 'lodash';

import SearchForm from './components/SearchForm.vue';

const router = useRouter();
const userRoleOptions = USER_ROLE_NOTIFY_OPTIONS;
const { colorSub, colorMain } = storeToRefs(useAppStore());
const { errors, validateData } = useValidate();
const notificationListData = ref([]);
const hasDiffPageIndex = ref(false);
const searchFormData = ref({
  title: '',
  creator: '',
  startDate: '',
  endDate: '',
  roles: [],
});
const searchForm = ref({
  title: '',
  creator: '',
  startDate: '',
  endDate: '',
  roles: [],
});
const totalPage = ref(0);
const pageIndex = ref(1);
const pagination = ref({
  sortBy: 'date_send',
  descending: true,
  page: 1,
  limit: 10,
});
const columns = [
  {
    name: 'title',
    label: '件名',
    align: 'left',
    field: 'title',
  },
  {
    name: 'date_send',
    label: '作成日時',
    align: 'left',
    field: 'date_send',
  },
  {
    name: 'user_code',
    label: '作成者ユーザーID',
    align: 'left',
    field: 'user_code',
    style: 'max-width: 230px',
  },
  ...userRoleOptions.map(item => ({
    name: item.value,
    label: item.label,
    align: 'center',
    field: item.value,
  })),
];

const formatSearchFormToQuery = form => ({
  title: form.title?.trim() || undefined,
  creator: form.creator?.trim() || undefined,
  startDate: form.startDate || undefined,
  endDate: form.endDate || undefined,
  roles: form.roles || undefined,
});

const getNotificationList = async () => {
  const notificationList = await notificationService.adminGetNotificationList({
    ...pagination.value,
    title: searchFormData.value.title?.trim() || undefined,
    creator: searchFormData.value.creator?.trim() || undefined,
    startDate: searchFormData.value.startDate || undefined,
    endDate: searchFormData.value.endDate || undefined,
    roles: searchFormData.value.roles || undefined,
  });

  if (notificationList) {
    notificationListData.value = notificationList.payload.items.map(item => {
      const roleColumnData = userRoleOptions.reduce((acc, cur) => {
        acc[cur.value] = false;
        return acc;
      }, {});
      item.role_notify.forEach(role => {
        roleColumnData[role] = true;
      });
      return {
        ...item,
        ...roleColumnData,
        date_send: FORMAT_DATE(item.date_send,'YYYY/MM/DD HH:mm'),
        user_code: item.creator?.user_code ?? '',
      };
    });

    totalPage.value = Math.ceil(
      (notificationList.payload.total_item ?? 1) / pagination.value.limit
    );

    if (notificationList.payload.page !== pageIndex.value) {
      hasDiffPageIndex.value = true;
      router.replace({
        query: {
          ...pagination.value,
          page: notificationList.payload.page,
          ...formatSearchFormToQuery(searchFormData.value),
        },
      });
    }
  }
};

const handleClickSearch = async () => {
  if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
    await getNotificationList();
    return;
  }

  const payload = {
    title: searchForm.value.title || undefined,
    creator: searchForm.value.creator || undefined,
    startDate: searchForm.value.startDate || undefined,
    endDate: searchForm.value.endDate || undefined,
    roles: searchForm.value.roles || undefined,
  };
  const validation = validateData(searchNotificationConditionSchema, payload);
  if (validation) {
    pageIndex.value = 1;
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchForm.value),
        page: 1,
      },
    });
  }
};

const handleClickClear = async () => {
  searchForm.value.title = '';
  searchForm.value.creator = '';
  searchForm.value.startDate = '';
  searchForm.value.endDate = '';
  searchForm.value.roles = [];
};

const handleClickCreateNotification = () => {
  router.push({
    name: 'adminNotificationRegister',
  });
};

const handleClickRow = (_, row) => {
  router.push({
    name: 'adminNotificationDetail',
    params: { id: row.id },
  });
};

// #region watch to get report list
watch(
  () => router.currentRoute.value.query.sortBy
    + router.currentRoute.value.query.descending
    + router.currentRoute.value.query.page
    + router.currentRoute.value.query.limit
    + router.currentRoute.value.query.title
    + router.currentRoute.value.query.creator
    + router.currentRoute.value.query.startDate
    + router.currentRoute.value.query.endDate
    + router.currentRoute.value.query.roles,
  async () => {
    if (!router.currentRoute.value?.query?.page) {
      router.replace({
        query: {
          ...pagination.value,
        },
      });
    } else {
      pageIndex.value = +router.currentRoute.value.query.page;
      pagination.value = {
        ...pagination.value,
        page: +router.currentRoute.value.query.page,
        limit: +router.currentRoute.value.query.limit,
        sortBy: router.currentRoute.value.query.sortBy,
        descending: router.currentRoute.value.query.descending === 'true',
      };
      searchForm.value = {
        title: router.currentRoute.value.query.title || '',
        creator: router.currentRoute.value.query.creator || '',
        startDate: router.currentRoute.value.query.startDate || '',
        endDate: router.currentRoute.value.query.endDate || '',
        roles: router.currentRoute.value.query.roles ?? [],
      };
      searchFormData.value = {
        title: router.currentRoute.value.query.title || '',
        creator: router.currentRoute.value.query.creator || '',
        startDate: router.currentRoute.value.query.startDate || '',
        endDate: router.currentRoute.value.query.endDate || '',
        roles: router.currentRoute.value.query.roles ?? [],
      };
      if (!hasDiffPageIndex.value) {
        await getNotificationList();
      } else {
        hasDiffPageIndex.value = false;
      }
    }
  }
);
// #endregion

onMounted(async () => {
  if (!router.currentRoute.value?.query?.page) {
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  } else {
    pageIndex.value = +router.currentRoute.value.query.page;
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      title: router.currentRoute.value.query.title || '',
      creator: router.currentRoute.value.query.creator || '',
      startDate: router.currentRoute.value.query.startDate || '',
      endDate: router.currentRoute.value.query.endDate || '',
      roles: router.currentRoute.value.query.roles
        ? typeof router.currentRoute.value.query.roles === 'string'
          ? [router.currentRoute.value.query.roles]
          : router.currentRoute.value.query.roles
        : [],
    };
    searchFormData.value = {
      title: router.currentRoute.value.query.title || '',
      creator: router.currentRoute.value.query.creator || '',
      startDate: router.currentRoute.value.query.startDate || '',
      endDate: router.currentRoute.value.query.endDate || '',
      roles: router.currentRoute.value.query.roles
        ? typeof router.currentRoute.value.query.roles === 'string'
          ? [router.currentRoute.value.query.roles]
          : router.currentRoute.value.query.roles
        : [],
    };
    await getNotificationList();
  }
});

// #region computed
const paginationComputed = computed(() => ({
  ...pagination.value,
  page: 1,
  rowsPerPage: pagination.value.limit,
}));
// #endregion

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);
const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);
</script>
<style scoped>
:deep(.table-header th) {
  font-weight: 400;
  font-size: 1rem;
  line-height: 2.2rem;
  text-align: left;
}

:deep(.table-body th) {
  font-weight: 400;
  font-size: 1rem;
  text-align: left;
  border-top: 1px solid #e5e7eb;
}
</style>
