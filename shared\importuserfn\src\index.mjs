
import _ from 'lodash';
import { getDataFromS3 } from './base/processs3.mjs';
import { processImportCSV } from './base/processimportcsv.mjs';
import { rdbConnect, wdbConnect } from './base/knex.mjs';
import { updateProcessImport, insertImportUsersData } from './base/writeprocessstep.mjs'
import dayjs from './base/dayjs.mjs';
import { ApplicationException } from './base/errors.mjs';

const groupDataByUserType = (dataUsers) => {
  return _.chain(dataUsers)
    // Group the elements of Array based on `color` property
    .groupBy('staff_type')
    // `key` is group's name (color), `value` is the array of objects
    .map((value, key) => ({ isEnterprise: key, datas: value }))
    .value();
}


const insertStaffs = async (trx, staffsInsertData) => {
  const resultStaffInsertData = [];
  if (staffsInsertData.length) {
    const sDIA = staffsInsertData.splice(0, 1);
    const sDI = sDIA[0];
    resultStaffInsertData.push(await processImportCSV(trx, sDI));
    if (staffsInsertData.length) {
      resultStaffInsertData.push(...await insertStaffs(trx, staffsInsertData));
    }
  }
  return resultStaffInsertData;
}

const process = async (rdb, wdb, record) => {
  const { s3 } = record;
  const { bucket, object } = s3;
  const { key } = object;
  const { name: bucketName } = bucket;
  const keyFile = decodeURI(key);
  const isolationLevel = 'read committed';
  const trx = await wdb.transaction({ isolationLevel });
  try {
    await updateProcessImport({
      fileKey: keyFile,
      status: 'PROCESSING',
      contents: '',
      datetimeprocess: dayjs.getDateFromJST().format('YYYY-MM-DD HH:mm:ss'),
      insertedcount: 0,
      updatedcount: 0,
      changedcount: 0,
    });
    const { rawDataImports, header } = await getDataFromS3(bucketName, keyFile);
    const dataImports = rawDataImports.map((data, index) => {
      return {
        line_number: index + 2,
        ...data,
      }
    });
    const dataUsers = groupDataByUserType(dataImports);
    const enterprisesData = dataUsers.filter((dt) => dt.isEnterprise === "0")
      .map((dt) => dt.datas).flat(1);
    const staffsData = dataUsers.filter((dt) => dt.isEnterprise === "1")
      .map((dt) => dt.datas).flat(1);
    const staffsUpdate = staffsData.filter((sd) => sd.user_code);
    const staffsInsert = staffsData.filter((sd) => !sd.user_code);
    const enterpriseUpSertData = await Promise.all(enterprisesData.map((ed) => {
      return processImportCSV(trx, ed);
    }));
    const staffUpSertData = await Promise.all(staffsUpdate.map((ed) => {
      return processImportCSV(trx, ed);
    }));
    const staffInsertData = await insertStaffs(trx, staffsInsert);    
    const dataImportsSuccess = [...enterpriseUpSertData, ...staffUpSertData, ...staffInsertData];
    const insertedCount = dataImportsSuccess.filter((dataI) => !dataI.is_updated).length;
    const updatedCount = dataImportsSuccess.filter((dataI) => dataI.is_updated).length;
    const changedCount = dataImportsSuccess.filter((dataI) => dataI.is_changed === 1).length;
    await insertImportUsersData(keyFile, dataImportsSuccess, header);
    await updateProcessImport({
      fileKey: keyFile,
      status: 'SUCCESS',
      contents: '',
      datetimeprocess: dayjs.getDateFromJST().format('YYYY-MM-DD HH:mm:ss'),
      insertedcount: insertedCount,
      updatedcount: updatedCount,
      changedcount: changedCount,
    });
    await trx.commit({ isolationLevel });
  } catch (ex) {
    await trx.rollback();
    await updateProcessImport({
      fileKey: keyFile,
      status: 'ERROR',
      contents: ex.message,
      datetimeprocess: dayjs.getDateFromJST().format('YYYY-MM-DD HH:mm:ss'),
      insertedcount: 0,
      updatedcount: 0,
      changedcount: 0,
    });
    throw ex;
  }
}

export const handler = async (event) => {
  const { Records } = event;
  const rdb = rdbConnect();
  const wdb = wdbConnect();
  try {
    await Promise.all(Records.map((record) => process(rdb, wdb, record)));
  } catch (ex) {
    if (!(ex instanceof ApplicationException)) {
      console.error(ex.stack);
    }
  }
  finally {
    rdb.destroy();
    wdb.destroy();
  }
};
