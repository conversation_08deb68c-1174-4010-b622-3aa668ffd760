const { sharedSchema } = require('../validations/sharedSchema');
const { SORT_BY_INVENTORY_ENUM, TYPE_DIFFERENCE_WEIGHT_ENUM } = require('../helpers/enum');
const { MESSAGE } = require('../utils/message');

// TODO: Add message for each error in Japanese
const getTotalWeightInventorySchema = {
  summary: 'Inventory',
  description: 'Get total weight inventory',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const getInventoryListSchema = {
  summary: 'Inventory',
  description: 'Get inventory',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  query: {
    additionalProperties: false,
    type: 'object',
    required: ['sortBy', 'descending', 'page', 'limit'],
    properties: {
      sortBy: {
        type: 'string',
        enum: Object.values(SORT_BY_INVENTORY_ENUM),
        default: SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE,
        errorMessage: {
          _: "Invalid sortBy value. It should be one of 'arrival_date', 'weight_inventory', 'group'",
        },
      },
      descending: {
        type: 'boolean',
        default: false,
        errorMessage: {
          _: 'Invalid descending value. It should be a boolean',
        },
      },
      page: {
        type: 'integer',
        errorMessage: {
          _: 'Invalid page value. It should be a number',
        },
      },
      limit: {
        type: 'integer',
        errorMessage: {
          _: 'Invalid limit value. It should be a number',
        },
      },
      group: {
        type: 'string',
        errorMessage: {
          _: 'Invalid group value. It should be a string',
        },
      },
      supplier: {
        type: 'string',
        errorMessage: {
          _: 'Invalid supplier value. It should be a string',
        },
      },
      startDate: {
        type: 'string',
        format: 'slash-date',
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        },
      },
      endDate: {
        type: 'string',
        format: 'slash-date',
        formatMinimum: {
          $data: '1/startDate',
        },
        errorMessage: {
          format: MESSAGE.MSG_LIMITS_DATE_ERROR,
          formatMinimum: 'End date must be greater than or equal to start date',
        },
      },
    },
  },
};

// TODO: Add message for each error in Japanese
const getInventoryDetailSchema = {
  summary: 'Inventory',
  description: 'Get inventory detail',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

const editInventorySchema = {
  summary: 'Inventory',
  description: 'Edit inventory',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
  body: {
    type: 'object',
    additionalProperties: false,
    required: [
      'grossWeightInventory',
      'typeDiff',
    ],
    properties: {
      grossWeightInventory: {
        type: 'number',
        exclusiveMinimum: 0,
        maximum:9999999999999.99,
        errorMessage: {
          exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
          maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      tareWeightInventory: {
        type: 'number',
        minimum: 0,
        exclusiveMaximum: {
          $data: '1/grossWeightInventory',
        },
        errorMessage: {
          minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
          exclusiveMaximum:
            'Tare weight must be greater than or equal to gross weight',
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      groupName: {
        type: 'string',
        minLength: 1,
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.MSG_GROUP_SETTING_INVALID,
        },
      },
      typeDiff: {
        type: 'number',
        enum: Object.values(TYPE_DIFFERENCE_WEIGHT_ENUM),
        errorMessage: {
          enum: "Invalid typeDiff value",
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      reasonDiff: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.REASON_DIFFERENCE_INVALID,
        },
      },
    },
    if: {
      properties: {
        typeDiff: {
          const: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
        },
      },
    },
    then: {
      required: ['reasonDiff'],
      properties: {
        reasonDiff: {
          type: 'string',
          maxLength: 256,
          minLength: 1,
          errorMessage: {
            maxLength: MESSAGE.REASON_DIFFERENCE_INVALID,
            _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          },
        },
      },
    },
  },
};

const getInventoryEditedDetailSchema = {
  summary: 'Inventory',
  description: 'Get inventory edited detail',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

const resetInventorySchema = {
  summary: 'Inventory',
  description: 'Reset inventory',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

const undoChangeInventorySchema = {
  summary: 'Inventory',
  description: 'Undo change inventory',
  tags: ['Inventory Management Feature'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
};

module.exports = {
  getTotalWeightInventorySchema,
  getInventoryListSchema,
  getInventoryDetailSchema,
  editInventorySchema,
  resetInventorySchema,
  getInventoryEditedDetailSchema,
  undoChangeInventorySchema,
};
