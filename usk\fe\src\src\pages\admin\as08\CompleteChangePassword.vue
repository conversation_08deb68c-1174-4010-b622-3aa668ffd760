<template>
  <div class="tw:w-[40%] tw:min-w-[550px] tw:m-auto">
    <!-- title -->
    <div class="tw:my-5 tw:font-bold tw:text-center tw:text-2xl tw:border-b tw:border-gray tw:pb-4">
      パスワード再登録完了
    </div>
    <!-- content -->
    <div
      class="tw:my-5 tw:leading-[2rem] tw:text-sm"
    >
      <div>パスワードの再登録が完了しました。</div>
      <div class="tw:mt-3">ログイン画面から再ログインしてください</div>
    </div>
    <q-btn
      class="tw:w-full tw:mt-5 tw:font-bold tw:leading-[2rem] tw:text-base"
      label="ログイン画面へ"
      type="button"
      outline
      :color="`${colorSub}`"
      @click.prevent="backToLogin"
    />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();

// ===== REF =====

// ===== METHOD =====
const backToLogin = async () => {
  await router.push({ name: 'adminLogin' });
};
</script>
