export const MESSASES = {
  USER_CODE_NOT_EXITS: '[ユーザーID]列項目に未登録のIDが指定されています。登録済みのユーザーIDのみを指定してください。\n行数：%1$s行目　エラー対象列：トレサビシステムユーザーID',
  ENTERPRIES_NOT_EXITS: '[事業者/従事者区分]列項目に「1」が指定されていますが、対象行の事業者は存在しません。[事業者/従事者区分]列項目に「1」を指定する場合、存在する事業者を指定してください。',
  PHONE_NUMBER_EXITS: '「%1$s」列項目に登録済みの携帯電話番号が指定されています。「%1$s」列項目に値を指定する場合、未登録の携帯電話番号のみを指定してください。',
  IMPORT_USER_NOK: '一括登録が失敗しました。%1$s行をご確認ください。',
  MSG_INVARID_ITEM_WITHOUTSAIHO_ERROR: '採捕事業者および採捕従事者以外に不要な項目が入力されています。不要な項目を削除してください。\n行数：%1$s行目　エラー対象列：事業者種別',
   MSG_USED_NOTIFICATIONUMBER_ERROR:"この届出/事業者割振り番号はすでに使用されています。\n行数：%1$s行目　エラー対象列：届出番号",
  MSG_INVARID_NOTIFICATIONUMBER_CHANGE_ERROR:"養鰻事業者以外は届出番号の変更または削除はできません。\n行数：%1$s行目　エラー対象列：届出番号",
  MSG_INVARID_CREATEUSER_NOTWORKER_ERROR: "この届出番号の事業者が存在しないため、従事者を登録できません。事業者を作成のうえ、従事者を登録してください。\n行数：%1$s行目　エラー対象列：届出番号",
  MSG_INVARID_ADDRESSFORMAT_ERROR:"不正な都道府県が入力されています。正しい都道府県を入力してください。\n行数：%1$s行目　エラー対象列：都道府県",
  MSG_INVALID_ACCOUNT_REGISTRATION_STATUS_CHANGE_ERROR: 'ユーザーID「%1$s」のアカウント登録状況は「0：仮登録」です。「0：仮登録」のユーザーを「1：本登録」に更新できません。\n行数：%2$s行目　エラー対象列：アカウント登録状況'
}