const dayjs = require('../boot/dayjs');
const BaseService = require('../base/serviceFn');
const { PAGINATION, NOTIFICATION_STATUS_ENUM, ENTERPRISE_TYPE_ENUM, STAFF_TYPE_ENUM, ROLES_ENUM_OPTIONS_VALUES } = require('../helpers/enum');
const { MESSAGE } = require('../utils/message');

class NotificationService extends BaseService {
  // ====== 1. Private method ==========

  // ====== 2. Public method ==========
  /**
   * Get notification list
   * @param {Object} user - user information
   * @param {Object} queries - queries for get notification list
   * @returns {Promise<Object>} - notification list {total_item, items}
   * @throws {Error}
   */
  async getNotificationList(user, queries) {
    const connect = this.DB.READ;

    // get queries
    const {
      descending = PAGINATION.DEFAULT_DESCENDING,
      page = PAGINATION.FIRST_PAGE,
      limit = PAGINATION.PAGE_SIZE,
      status,
    } = queries;
    const { enterprise_type: enterpriseType, staff_type: staffType } = user;
    let formatRoles = '';

    if (enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE && staffType === STAFF_TYPE_ENUM.STAFF) {
      formatRoles = ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF;
    } else if (enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE && staffType === STAFF_TYPE_ENUM.ENTERPRISE) {
      formatRoles = ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE;
    } else if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE && staffType === STAFF_TYPE_ENUM.STAFF) {
      formatRoles = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF;
    } else if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE && staffType === STAFF_TYPE_ENUM.ENTERPRISE) {
      formatRoles = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE;
    }

    // build search condition
    const searchCondition = {
      date_send: {
        lte: dayjs().toDate(),
      },
      role_notify: {
        has: formatRoles,
      },
      delete_flag: false,
    };

    switch (status) {
      case NOTIFICATION_STATUS_ENUM.READ:
        searchCondition.readers = {
          some: {
            reader_id: user.id,
          },
        };
        break;
      case NOTIFICATION_STATUS_ENUM.UNREAD:
        searchCondition.readers = {
          none: {
            reader_id: user.id,
          },
        };
        break;
      default:
        break;
    }


    // calculate total item
    const total_item = await connect.notifications.count({
      where: searchCondition,
    });

    // if total item is 0, return empty items
    if (!total_item) {
      return this.SUCCESS({
        total_item,
        items: [],
        page: 1,
        page_size: +limit,
      });
    }

    // calculate offset
    const offset = (+page - 1) * +limit;

    let tempPage = +page;
    let tempOffset = offset;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= total_item) {
      tempPage = 1,
        tempOffset = (tempPage - 1) * +limit;
    }

    // get notification list
    const items = await connect.notifications.findMany({
      select: {
        id: true,
        title: true,
        content: true,
        date_send: true,
        readers: {
          select: {
            reader_id: true,
          },
        },
      },
      where: searchCondition,
      orderBy: {
        date_send: descending ? 'desc' : 'asc',
      },
      skip: tempOffset,
      take: Number(limit),
    });

    items.map((item) => {
      item.is_read = item.readers.some(
        (reader) => reader.reader_id === user.id
      );
      delete item.readers;
      return item;
    });

    return this.SUCCESS({
      total_item,
      items,
      page: tempPage,
      page_size: +limit,
    });
  }

  /**
   * get notification detail
   * @param {*} user user information
   * @param {*} id notification id
   * @returns notification detail - {id, title, content, date_send, is_read}
   */
  async getNotificationDetail(user, id) {
    const connect = this.DB.WRITE;
    const { enterprise_type: enterpriseType, staff_type: staffType } = user;
    let role;
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.STAFF) {
      role = ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF;
    }
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.ENTERPRISE) {
      role = ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE;
    }
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.STAFF) {
      role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF;
    }
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.ENTERPRISE) {
      role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE;
    }


    // get notification
    const notification = await connect.notifications.findUnique({
      where: {
        id: Number(id),
        date_send: {
          lte: dayjs().toDate(),
        },
        role_notify: {
          has: role,
        },
        delete_flag: false,
      },
      select: {
        id: true,
        title: true,
        content: true,
        date_send: true,
        readers: {
          select: {
            reader_id: true,
          },
        },
      },
    });

    if (!notification) {
      return this.ERROR(MESSAGE.MSG_NO_DATA_NOTIFICATION);
    }

    // check if user has read this notification
    const isRead = notification.readers.some(
      (reader) => reader.reader_id === user.id
    );
    if (!isRead) {
      await connect.notifications.update({
        where: {
          id: Number(id),
        },
        data: {
          readers: {
            create: {
              reader_id: user.id,
              created_by_id: user.id,
              created_on: dayjs().toDate(),
            },
          },
        },
      });
    }

    notification.is_read = isRead;
    delete notification.readers;
    return this.SUCCESS(notification);
  }

  /**
   * Check has unread notification
   * @param {Object} user - user information
   * @returns {Promise<Object>} - has_unread: boolean
   * @throws {Error}
   */
  async checkHasUnreadNotification(user) {
    const connect = this.DB.READ;

    const { enterprise_type: enterpriseType, staff_type: staffType } = user;
    let role = ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE;
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.STAFF) {
      role = ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF;
    }
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.ENTERPRISE) {
      role = ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE;
    }
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.STAFF) {
      role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF;
    }
    if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
      && staffType === STAFF_TYPE_ENUM.ENTERPRISE) {
      role = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE;
    }

    if (enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
      return this.SUCCESS({
        has_unread: false,
      });
    }

    // get unread notification
    const unreadNotification = await connect.notifications.findFirst({
      where: {
        date_send: {
          lte: dayjs().toDate(),
        },
        readers: {
          none: {
            reader_id: user.id,
          },
        },
        role_notify: {
          has: role,
        },
        delete_flag: false,
      },
    });

    return this.SUCCESS({
      has_unread: !!unreadNotification,
    });
  }

  // #region using admin site
  /**
   * get notifications for notification manager
   * @param {*} params conditions
   * @returns notifications
   */
  async getNotificationListManager(params) {

    const { page, title, startDate, endDate, creator, roles } = params;
    const connect = this.DB.READ;
    const formatRoles = typeof roles === 'string' ? [roles] : roles;
    const searchCondition = {
      delete_flag: false,
      title: title
        ? {
          contains: title,
        }
        : undefined,
      date_send: {
        gte: startDate
          ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate()
          : undefined,
        lte: endDate
          ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate()
          : undefined,
      },
      creator: creator
        ? {
          user_code: {
            startsWith: creator.replace(/\s+/g, ''),
          },
        }
        : undefined,
      role_notify: formatRoles?.length
        ? {
          hasEvery: formatRoles,
        }
        : undefined,
    };
    const totalItem = await connect.notifications.count({
      where: searchCondition,
    });

    // if total item is 0, return empty items
    if (!totalItem) {
      return this.SUCCESS({
        items: [],
        total_item: 0,
        page: 1,
        page_size: 10,
      });
    }

    const offset = (page - 1) * 10;
    let tempPage = page;
    let tempOffset = offset;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= totalItem) {
      tempPage = Math.ceil(offset / 10);
      tempOffset = (tempPage - 1) * 10;
    }
    const items = await connect.notifications.findMany({
      select: {
        id: true,
        title: true,
        date_send: true,
        role_notify: true,
        creator: {
          select: {
            user_code: true,
          },
        },
      },
      where: searchCondition,
      orderBy: {
        date_send: 'desc',
      },
      skip: tempOffset,
      take: 10,
    });

    return this.SUCCESS({
      items,
      total_item: totalItem,
      page: tempPage,
      page_size: 10,
    });
  }

  /**
   * register notification for notification manager admin site
   * @param {*} user user information
   * @param {*} data body data
   * @returns new notification
   */
  async registerNotificationManager(user, data) {
    const connect = this.DB.WRITE;
    const { title, content, roles } = data;
    const notification = await connect.notifications.create({
      data: {
        title,
        content,
        // TODO: need confirm about date_send
        date_send: dayjs().toDate(),
        role_notify: roles,
        created_by_id: user.id,
        created_on: dayjs().toDate(),
        latest_updated_by_id: user.id,
        latest_updated_on: dayjs().toDate(),
      },
    });
    return this.SUCCESS(notification);
  }

  /**
   * get notification detail for notification manager admin site
   * @param {*} id notification id
   * @returns notification detail - {id, title, content, date_send, role_notify, creator: {user_code}}
   */
  async getNotificationDetailManager(id) {
    const connect = this.DB.READ;
    const notification = await connect.notifications.findUnique({
      where: {
        id: Number(id),
        delete_flag: false,
      },
      select: {
        id: true,
        title: true,
        content: true,
        date_send: true,
        role_notify: true,
        creator: {
          select: {
            user_code: true,
          },
        },
      },
    });

    if (!notification) {
      return this.ERROR(MESSAGE.MSG_NO_DATA_NOTIFICATION);
    }

    return this.SUCCESS(notification);
  }

  /**
   * delete notification for notification manager admin site
   * @param {*} id notification id
   * @returns success or error
   */
  async deleteNotificationManager(user, id) {
    const connect = this.DB.WRITE;
    const notification = await connect.notifications.update({
      where: {
        delete_flag: false,
        id: Number(id),
      },
      data: {
        delete_flag: true,
        latest_updated_by_id: user.id,
        latest_updated_on: dayjs().toDate(),
      },
    });

    if (!notification) {
      return this.ERROR(MESSAGE.MSG_NO_DATA_NOTIFICATION);
    }

    return this.SUCCESS();
  }
  // #endregion using admin site
}

module.exports = NotificationService;
