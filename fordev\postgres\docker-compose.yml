version: "3.9"
# define all services
services:
  dn-db-postgres:
    restart: "no"
    container_name: dn-db-postgres
    ports:
      - 5432:5432
    # privileged: "true"
    image: postgres:17.2
    volumes:
      - ./sql/:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=uskdb
      - POSTGRES_USER=ssv
      - POSTGRES_PASSWORD=Ab123456a@
      - TZ=GMT+9
      - PGTZ=GMT+9
    networks:
      - app-network
    # region: to avoid the error coverity - remove to run in local
    read_only: true
    no_new_privileges: true
    cap_drop:
      - ALL
    # endregion

#Docker Networks
networks:
  app-network:
    driver: bridge
