<template>
  <div v-if="createState" class="tw:mx-auto tw:max-w-[600px] tw:mt-3">
      <div class="tw:mb-1">
        <span class="tw:text-[1rem] tw:block tw:mb-1"
          >件名<span class="tw:text-red">*</span></span
        >
        <q-input
          outlined
          input-class="tw:w-full"
          class="tw:rounded-lg"
          v-model.trim="form.title"
          autocomplete="nope"
          maxlength="256"
          :error="!!errors.title"
          :error-message="errors.title"
          no-error-icon
        />
      </div>

      <div class="tw:mb-1">
        <span class="tw:text-[1rem] tw:block tw:mb-1"
          >本文<span class="tw:text-red">*</span></span
        >
        <q-input
          v-model="form.content"
          outlined
          input-class="tw:min-h-[200px] tw:text-[1rem]"
          autocomplete="nope"
          type="textarea"
          maxlength="1000"
          :error="!!errors.content"
          :error-message="errors.content"
          no-error-icon
          counter
        />
      </div>

      <div class="tw:mb-1">
        <span class="tw:text-[1rem] tw:block"
          >送付対象ロール<span class="tw:text-red">*</span></span
        >
        <q-field :error="!!errors.roles" no-error-icon borderless>
          <template v-slot:control>
            <div class="tw:grid-cols-4 tw:grid tw:w-full tw:gap-x-3">
              <q-checkbox
                class="tw:text-[1rem]"
                v-for="(item, index) in userRoleOptions"
                :key="index"
                v-model="form.roles"
                :label="item.label"
                :val="item.value"
                size="lg"
              />
            </div>
          </template>
          <template v-slot:error>
            <div class="tw:pl-3 -tw:translate-y-3">
              {{ errors.roles }}
            </div>
          </template>
        </q-field>
      </div>
    <div class="tw:flex tw:justify-center tw:gap-3 tw:mt-4">
      <BaseButton
        outline
        padding="0.75rem"
        :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
        label="戻る"
        @click.prevent="handleClickCancel"
      />
      <BaseButton
        padding="0.75rem"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
        label="確認する"
        @click.prevent="handleClickPreview"
      />
    </div>
  </div>
  <div v-else class="tw:mx-auto tw:max-w-[600px] tw:mt-3">
    <p class="tw:text-[1rem] tw:mb-2">以下の内容で登録します。</p>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">件名</span>
      <div :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`">
        {{ form.title }}
      </div>
    </div>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">本文</span>
      <div
        :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded tw:break-words`"
        v-html="clearHTML(linkify(form.content))"
      ></div>
    </div>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">送付対象ロール</span>
      <div :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`">
        <p v-for="(item, index) in form.roles" :key="index">
          {{ userRoleOptions.find((option) => option.value === item)?.label }}
        </p>
      </div>
    </div>
    <div class="tw:flex tw:justify-center tw:gap-3 tw:mt-4">
      <BaseButton
        outline
        padding="0.75rem"
        :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
        label="いいえ"
        @click.prevent="handleClickCancelPreview"
      />
      <BaseButton
        padding="0.75rem"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
        label="はい"
        @click.prevent="handleClickSave"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { USER_ROLE_NOTIFY_OPTIONS } from 'src/helpers/constants';
import BaseButton from 'components/base/BaseButton.vue';
import useValidate from 'composables/validate';
import notificationService from 'src/shared/services/notification.service';
import { useAppStore } from 'src/stores/app-store';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import registerAdminNotificationSchema from 'schemas/admin/notification/registerNotification.schema';
import { clearHTML, linkify } from 'src/helpers/common';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';

const { colorSub } = storeToRefs(useAppStore());
const { errors, validateData } = useValidate();
const router = useRouter();
const userRoleOptions = USER_ROLE_NOTIFY_OPTIONS;
const form = ref({
  title: '',
  content: '',
  roles: [],
});
const createState = ref(true);

const handleClickCancel = () => {
  router.push({ name: 'adminNotificationManager' });
};

const handleClickPreview = () => {
  const validate = validateData(registerAdminNotificationSchema, form.value);
  if (validate) {
    createState.value = false;
  }
};

const handleClickCancelPreview = () => {
  createState.value = true;
};

const handleClickSave = async () => {
  const result = await notificationService.adminRegisterNotification(form.value);
  if (result.code === 0) {
    toast.access(MESSAGE.MSG_COMPOSE_NOTICE_INFO);
    router.push({ name: 'adminNotificationManager' });
  }
};
</script>

<style scoped>
:deep(a) {
  color: #007bff !important;
}
</style>
