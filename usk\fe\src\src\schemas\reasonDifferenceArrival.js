import MESSAGE from 'helpers/message';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'helpers/constants';

const reasonDifferenceArrival = {
  type: 'object',
  additionalProperties: false,
  required: ['typeDiff'],
  properties: {
    typeDiff: {
      type: 'integer',
      enum: [
        TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH,
        TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR,
        TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER,
      ],
      errorMessage: {
        _: MESSAGE.TYPE_DIFFERENCE_REQUIRED,
      },
    },
    reasonDiff: {
      type: 'string',
      minLength: 1,
      maxLength: 300,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.REASON_DIFFERENCE_INVALID,
      },
    },
  },
};

export default reasonDifferenceArrival;
