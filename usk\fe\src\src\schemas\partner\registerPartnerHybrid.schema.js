import { PARTNER_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';

const registerPartnerHybridSchema = {
  additionalProperties: false,
  type: 'object',
  properties: {
    partner_id: {
      type: 'integer',
    },
    partner_type: {
      type: 'array',
      minItems: 1,
      items: {
        type: 'integer',
        enum: Object.values(PARTNER_TYPE_ENUM),
      },
      errorMessage: {
        minItems: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export default registerPartnerHybridSchema;
