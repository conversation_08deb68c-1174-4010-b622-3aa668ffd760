<template>
  <div class="tw:p-6">
    <!-- content -->
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        漁法
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ gyohou }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        船舶
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ sennpaku }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        区域
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ kuiki }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        船名
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ sennmei }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        漁船登録番号
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ gyosentourokubangou }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        総トン数
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ soutonsuu }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        種類
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ syurui }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        馬力数
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ barikisuu }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        船名２
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ sennmei2 }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        漁船登録番号２
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ gyosentourokubangou2 }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        総トン数２
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ soutonsuu2 }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        種類２
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ syurui2 }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        馬力数２
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ barikisuu2 }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        検量１行目
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ kenryou1gyoume }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        検量２行目
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ kenryou2gyoume }}
      </div>
    </div>
    <div class="tw:flex tw:flex-col tw:my-4">
      <span class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1">
        たも網の使用
      </span>
      <div class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4
      tw:tl:text-[1.53rem] tw:min-h-[3.5rem] tw:tl:min-h-[4.31rem] tw:dt:min-h-[3.5rem]">
        {{ tamoaminosiyou }}
      </div>
    </div>
  </div>
</template>

<script setup>

import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// ===== REF =====
const gyohou = ref();
const sennpaku = ref();
const kuiki = ref();
const sennmei = ref();
const gyosentourokubangou = ref();
const soutonsuu = ref();
const syurui = ref();
const barikisuu = ref();
const sennmei2 = ref();
const gyosentourokubangou2 = ref();
const soutonsuu2 = ref();
const syurui2 = ref();
const barikisuu2 = ref();
const kenryou1gyoume = ref();
const kenryou2gyoume = ref();
const tamoaminosiyou = ref();

// ===== METHOD =====

onMounted(() => {
  gyohou.value = route.query?.gyohou;
  sennpaku.value = route.query?.sennpaku;
  kuiki.value = route.query?.kuiki;
  sennmei.value = route.query?.sennmei;
  gyosentourokubangou.value = route.query?.gyosentourokubangou;
  soutonsuu.value = route.query?.soutonsuu;
  syurui.value = route.query?.syurui;
  barikisuu.value = route.query?.barikisuu;
  sennmei2.value = route.query?.sennmei2;
  gyosentourokubangou2.value = route.query?.gyosentourokubangou2;
  soutonsuu2.value = route.query?.soutonsuu2;
  syurui2.value = route.query?.syurui2;
  barikisuu2.value = route.query?.barikisuu2;
  kenryou1gyoume.value = route.query?.kenryou1gyoume;
  kenryou2gyoume.value = route.query?.kenryou2gyoume;
  tamoaminosiyou.value = route.query?.tamoaminosiyou;
});

</script>
