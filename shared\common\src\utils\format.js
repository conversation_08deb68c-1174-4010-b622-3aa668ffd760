const FORMAT_LOG = (value, keys, data) => {
  if (Array.isArray(value)) {
    data[keys] = `Array(${value.length})`;
  } else if (typeof value === 'object') {
    const keysNumber = Object.keys(value).filter(key => Number(key) || Number(key) === 0);
    if (keysNumber.length) {
      data[keys] = `Array(${keysNumber.length})`
    } else {
      const result = {};
      Object.keys(value).forEach(key => {
        FORMAT_LOG(value[key], key, result)
      });
      data[keys] = result;
    }
  } else {
    data[keys] = value
  }
}

module.exports = {
  FORMAT_LOG
}