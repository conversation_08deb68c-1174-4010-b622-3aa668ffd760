<template>
  <q-page class="h-full tw:space-y-4 tw:mb-[13rem] tw:xl:mb-[7rem]">
    <q-card class="tw:flex tw:flex-row tw:justify-between tw:items-center tw:px-4 tw:py-2">
      <div>
        <span class="tw:font-bold tw:text-m-design"> 総在庫量: </span>
      </div>
      <div class="tw:font-bold tw:text-xl-design">{{ FORMAT_NUMBER(totalNetWeight) || 0 }} g</div>
    </q-card>
    <ExpansionSearchItem v-model:expanded="isSearchExpanded">
      <SearchForm />
    </ExpansionSearchItem>
    <div
      v-if="
        searchConditions.enterpriseName ||
        searchConditions.name ||
        searchConditions.code ||
        searchConditions.licenseNumber ||
        (searchConditions.startDate && searchConditions.endDate)
      "
      class="tw:mt-4 tw:mb-4 tw:text-xs-design tw:flex tw:items-center tw:space-x-2"
    >
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]">検索条件</span>
      <span
        v-if="searchConditions.enterpriseName"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.enterpriseName }}
      </span>
      <span
        v-if="searchConditions.name"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white tw:truncate tw:max-w-[90vw]"
      >
        {{ searchConditions.name }}
      </span>
      <span
        v-if="searchConditions.startDate && searchConditions.endDate"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.startDate }} ~ {{ searchConditions.endDate }}
      </span>
      <span
        v-if="searchConditions.code"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.code }}
      </span>
      <span
        v-if="searchConditions.licenseNumber"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white"
      >
        {{ searchConditions.licenseNumber }}
      </span>
    </div>

    <q-card class="tw:p-4 tw:mt-4 tw:mb-4 tw:bg-white">
      <!-- filter start -->
      <!-- filter end -->
      <!-- sort btn start -->
      <div
        class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:border-neutral-500 tw:pt-1"
      >
        <div class="tw:flex tw:tl:flex-row tw:flex-col tw:items-start tw:space-y-4">
          <div class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]">
            <span class="tw:text-xs-design">表示件数</span>
            <PageSizeDropdownSP v-model="pageSize" />
          </div>
          <SortByDropdownSP v-model="sortModel"
            class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden tw:mb-[1rem] tw:tl:mb-0"
          />
        </div>
        <div
          class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0"
        >
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block q-py-sm">
        <q-table
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="paginationComputed"
          :rows-per-page-options="[50]"
        >
          <template v-slot:header="props">
            <q-tr :props="props" class="tw:text-s-design" :class="`tw:bg-[#E2E3EA]`">
              <q-th
                @click="handleClickSort(props.cols[0].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[0].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:text-s-design tw:font-bold tw:text-center tw:dt:w-[15rem] tw:tl:w-[15rem] tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[1].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[2].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:dt:w-[16rem] tw:tl:w-[16rem] tw:border-r tw:border-[#D2D2D2]"
              >
                {{ props.cols[2].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[2].name)"
                  :name="
                    getSortOrder(props.cols[2].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[3].name)"
                class="tw:text-s-design tw:font-bold tw:text-left tw:dt:w-[16rem] tw:tl:w-[16rem]"
              >
                {{ props.cols[3].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[3].name)"
                  :name="
                    getSortOrder(props.cols[3].name) === 'asc' ? 'arrow_upward' : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr
              class="tw:cursor-pointer tw:w-full"
              :props="props"
              @click.prevent="handleSelectInventory(_, props.row)"
            >
              <q-td
                key="title"
                :props="props"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:font-bold tw:border-r tw:border-[#D2D2D2]"
              >
                <div
                  :class="`tw:truncate tw:tl:max-w-[300px] tw:lg:max-w-[350px]
                    tw:xl:max-w-[540px] tw:dt:max-w-[490px] tw:underline tw:underline-offset-1 tw:text-[#004AB9]`"
                >
                  {{ props.row?.title }}
                </div>
              </q-td>
              <q-td
                key="latestArrivalDate"
                :props="props"
                class="text-center tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:dt:w-[15rem] tw:tl:w-[15rem]">
                  {{ props.row.latestArrivalDate }}
                </div>
              </q-td>
              <q-td
                key="netWeightTotal"
                :props="props"
                class="text-right tw:text-s-design tw:border-r tw:border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]">
                  {{ FORMAT_NUMBER(props.row.netWeightTotal) }}g
                </div>
              </q-td>
              <q-td key="netWeightInventory" :props="props" class="text-right tw:text-s-design">
                <div class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]">
                  {{ FORMAT_NUMBER(props.row.netWeightInventory) }}g
                </div>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div class="tw:w-full tw:text-center tw:text-s-design">データが見つかりません。</div>
          </template>
        </q-table>
      </div>
      <!-- start table for smartphone -->
      <div class="tw:block tw:tl:hidden q-py-sm">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-1"
          card-class="tw:w-full"
          :rows="inventoryListData"
          v-model:pagination="paginationComputed"
          row-key="id"
          hide-pagination
          hide-header
        >
          <template v-slot:item="props">
            <div class="tw:w-full" @click.prevent="handleSelectInventory(_, props.row)">
              <q-card flat bordered>
                <q-card-section class="tw:text-left tw:cursor-pointer">
                  <div class="tw:flex-1 tw:flex tw:items-center tw:justify-between">
                    <div>
                      <strong class="tw:text-s-design tw:text-[#004AB9]">
                        {{ props.row.title }}</strong
                      >
                      <br />
                      <div class="tw:text-[#333333] tw:text-s-design">
                        <span>最新の入荷日: {{ `${props.row.latestArrivalDate}` }}</span>
                        <br />
                        <span>総入荷量: {{ `${props.row.netWeightTotal}g` }}</span>
                        <br />
                        <span>総在庫量: {{ `${props.row.netWeightInventory}g` }}</span>
                      </div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </template>
          <template v-slot:no-data="">
            <div :class="`tw:w-full tw:text-center tw:text-s-design`">データが見つかりません。</div>
          </template>
        </q-table>
      </div>
      <!-- table end -->
      <div class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0">
        <PaginationNotifi />
      </div>
    </q-card>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
      tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
        label="トップに戻る"
        @click.prevent="goToPage('home')"
      />
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[28rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-[94.5%]`"
        label="在庫修正一覧を表示する"
        @click.prevent="handleClickGoToInventoryEditedList"
      />
    </q-footer>
  </q-page>
</template>

<script setup>
// #region import
import PageSizeDropdownSP from 'components/PageSizeDropdownSP.vue';
import useValidate from 'composables/validate';
import { FORMAT_DATE, FORMAT_NUMBER } from 'helpers/common';
import { SORT_BY_INVENTORY_ENUM } from 'helpers/constants';
import searchInventoryCondition from 'schemas/inventory-management/searchInventoryCondition.schema';
import inventoryManagementService from 'services/inventoryManagement.service';
import { computed, onMounted, provide, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import SortByDropdownSP from 'components/SortByDropdownSP.vue';
import BaseButton from 'components/base/vs/BaseButton.vue';
import { isEqual } from 'lodash';
import { useLocalStorage } from 'composables/localstorage';
import PaginationNotifi from 'components/PaginationNotifi.vue';
import { orderBy } from 'lodash';

import SearchForm from './components/SearchForm.vue';

// #endregion

// #region state
const router = useRouter();
const { validateData: validateSearchForm, errors: errorsSearchForm } = useValidate();
const totalNetWeight = ref(0);
const { listPageQueryParams } = useLocalStorage();
const isSearchExpanded = ref(false);
const route = useRoute();
const isFirstLoad = ref(true);

const searchConditions = computed(() => {
  const query = route.query;
  return {
    enterpriseName: query.enterpriseName || '',
    name: query.name || '',
    startDate: query.startDate || '',
    endDate: query.endDate || '',
    code: query.code || '',
    licenseNumber: query.licenseNumber || '',
  };
});

const columns = [
  {
    name: 'title',
    label: 'グループ名',
    align: 'left',
    field: 'title',
    sortable: true,
  },
  {
    name: 'latestArrivalDate',
    label: '最新の入荷日',
    align: 'center',
    field: 'latestArrivalDate',
    sortable: true,
  },
  {
    name: 'netWeightTotal',
    align: 'right',
    label: '総入荷量',
    field: 'netWeightTotal',
    format: val => `${val}g`,
    sortable: true,
  },
  {
    name: 'netWeightInventory',
    align: 'center',
    label: '総在庫量',
    field: 'netWeightInventory',
    format: val => `${val}g`,
    sortable: true,
  },
];
const sortModel = ref({});
const pagination = ref({
  sortBy: listPageQueryParams.value?.inventoryList?.sortBy || SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE,
  descending: true,
  page: listPageQueryParams.value?.inventoryList?.page || 1,
  limit: listPageQueryParams.value?.inventoryList?.limit || 10,
});
const searchFormData = ref({
  enterpriseName: '',
  name: '',
  startDate: '',
  endDate: '',
  code: '',
  licenseNumber: '',
});
const totalPage = ref(0);
const pageSize = ref(router.currentRoute.value.query.limit || 10);
const pageIndex = ref(pagination.value.page || 1);
const sortBySelectedLabel = ref('');
const inventoryListData = ref([]);
const multiSortConditions = ref([]);
// for manage state of has different page index
const searchForm = ref({
  enterpriseName: '',
  name: '',
  startDate: '',
  endDate: '',
  code: '',
  licenseNumber: '',
});
// #endregion

// #region actions
const formatSearchFormToQuery = form => ({
  enterpriseName: form.enterpriseName || undefined,
  name: form.name || undefined,
  startDate: form.startDate || undefined,
  endDate: form.endDate || undefined,
  code: form.code || undefined,
  licenseNumber: form.licenseNumber || undefined,
});

const sortedRows = computed(() => {
  const rows = [...inventoryListData.value].map(row => ({
    ...row,
    latestArrivalDate: FORMAT_DATE(row.latestArrivalDate || ''),
  }));
  const lstKey = [];
  const lstOrder = [];
  for (const { key, order } of multiSortConditions.value) {
    lstKey.push(key);
    lstOrder.push(order);
  }
  return orderBy(rows, lstKey, lstOrder);
});

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }
};

const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const getShipmentList = async () => {
  const inventoryListResponse = await inventoryManagementService.getInventoryList({
    ...pagination.value,
    enterpriseName: searchFormData.value.enterpriseName || undefined,
    name: searchFormData.value.name || undefined,
    startDate: searchFormData.value.startDate || undefined,
    endDate: searchFormData.value.endDate || undefined,
    code: searchFormData.value.code || undefined,
    licenseNumber: searchFormData.value.licenseNumber || undefined,
  });

  if (inventoryListResponse.code === 0) {
    inventoryListData.value = inventoryListResponse.payload.items.map(item => ({
      title: item.group_name ?? item?.fallback_group_name,
      id: item.id,
      netWeightInventory: item.net_weight_inventory,
      netWeightTotal: item.net_weight_total,
      latestArrivalDate: FORMAT_DATE(item.latest_arrival_date || ''),
      netWeight: +item.net_weight_inventory,
      netTotal: +item.net_weight_total,
    }));

    if (window.innerWidth < 960) {
      let key;
      let order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'latest_arrival_date':
          key = 'latestArrivalDate';
          order = 'desc';
          break;
        case 'group_name':
          key = 'title';
          break;
        case 'net_weight_inventory':
          key = 'netWeight';
          break;
        case 'net_weight_total':
          key = 'netTotal';
          break;
        default:
          break;
      }

      inventoryListData.value = orderBy(inventoryListData.value, [key], [order]);
    }
    totalPage.value = Math.ceil(
      (inventoryListResponse.payload.total_item ?? 1) / pagination.value.limit
    );
    totalNetWeight.value = inventoryListResponse.payload.total_net_weight;

    // check if page index is different from the value server return
    // if different, replace query to the new page index (value server return)
    if (inventoryListResponse.payload.page !== pageIndex.value) {
      router.replace({
        query: {
          ...pagination.value,
          ...formatSearchFormToQuery(searchFormData.value),
          page: inventoryListResponse.payload.page,
        },
      });
    }
  }
};

const goToPage = name => {
  router.push({ name });
};

const handleSelectInventory = async (evt, row) => {
  await router.push({
    name: 'inventoryDetail',
    params: {
      id: row.id,
    },
  });
};

const handleClickGoToInventoryEditedList = async () => {
  await router.push({
    name: 'inventoryEditedList',
  });
};

// #region watch to get report list
watch(pageSize, async (newPageSize, oldPageSize) => {
  if (newPageSize === oldPageSize || isFirstLoad.value) {
    return;
  }

  pagination.value.limit = +newPageSize;
  totalPage.value = Math.ceil(inventoryListData.value.length / +newPageSize);

  if (listPageQueryParams.value.inventoryList) {
    listPageQueryParams.value.inventoryList.limit = +newPageSize;
  } else {
    listPageQueryParams.value.inventoryList = {
      limit: +newPageSize,
    };
  }

  const query = {
    ...pagination.value,
    ...formatSearchFormToQuery(searchFormData.value),
    page: 1,
  };

  router.push({
    query: {
      ...query,
    },
  });
});

watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.descending +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.limit +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.name +
    router.currentRoute.value.query.code +
    router.currentRoute.value.query.licenseNumber +
    router.currentRoute.value.query.startDate +
    router.currentRoute.value.query.endDate,
  async () => {
    const query = router.currentRoute.value.query;

    if (!listPageQueryParams.value.inventoryList) {
      listPageQueryParams.value.inventoryList = {};
    }

    if (query.limit) {
      listPageQueryParams.value.inventoryList.limit = +query.limit;
    }
    if (query.page) {
      listPageQueryParams.value.inventoryList.page = +query.page;
    }

    pageSize.value = +(query.limit || 10);
    pageIndex.value = +(query.page || 1);

    sortBySelectedLabel.value = query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(item => item.value === query.sortBy)
          ?.label || '新着順'
      : '新着順';

    pagination.value = {
      ...pagination.value,
      page: +(query.page || 1),
      limit: +(query.limit || 10),
      sortBy: query.sortBy || SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE,
      descending: query.descending === 'true',
    };

    const searchParams = {
      enterpriseName: query.enterpriseName || '',
      name: query.name || '',
      startDate: query.startDate || '',
      endDate: query.endDate || '',
      code: query.code || '',
      licenseNumber: query.licenseNumber || '',
    };

    searchForm.value = { ...searchParams };
    searchFormData.value = { ...searchParams };

    listPageQueryParams.value.inventoryList = {
      ...listPageQueryParams.value.inventoryList,
      ...searchParams,
      sortBy: query.sortBy || SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE,
      descending: query.descending === 'true',
    };

    if (window.innerWidth < 960) {
      let key;
      let order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case 'latest_arrival_date':
          key = 'latestArrivalDate';
          order = 'desc';
          break;
        case 'group_name':
          key = 'title';
          break;
        case 'net_weight_inventory':
          key = 'netWeight';
          break;
        case 'net_weight_total':
          key = 'netTotal';
          break;
        default:
          break;
      }

      inventoryListData.value = orderBy(inventoryListData.value, [key], [order]);
    }

    if (isFirstLoad.value) {
      await getShipmentList();
      isFirstLoad.value = false;
    }
  }
);

watch(
  () => pagination.value.limit,
  async () => {
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
      },
    });
  }
);
// #endregion

// #region computed
const paginationComputed = computed(() => ({
  page: pagination.value.page,
  rowsPerPage: pagination.value.limit,
}));
// #endregion

// #region provide
const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      enterpriseName: '',
      name: '',
      startDate: '',
      endDate: '',
      code: '',
      licenseNumber: '',
    };
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getShipmentList();
      isSearchExpanded.value = false;
      return;
    }

    const data = {
      ...searchForm.value,
      startDate: searchForm.value.startDate || '',
      endDate: searchForm.value.endDate || '',
    };
    const validate = validateSearchForm(searchInventoryCondition, data);

    if (validate) {
      pageIndex.value = 1;
      await router.push({
        query: {
          ...pagination.value,
          ...formatSearchFormToQuery(searchForm.value),
          page: 1,
        },
      });

      isSearchExpanded.value = false;
    }
  },
  title: '在庫をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: '新着順', value: SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE },
    { label: 'グループ名順', value: SORT_BY_INVENTORY_ENUM.GROUP },
    { label: '総入荷量順', value: SORT_BY_INVENTORY_ENUM.WEIGHT_INVENTORY },
    { label: '総在庫量順', value: SORT_BY_INVENTORY_ENUM.WEIGHT_TOTAL },
  ],
  sortBySelectedLabel,
  handleClickSortByItem: async option => {
    sortBySelectedLabel.value = option.label;
    pagination.value.sortBy = option.value;
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors: errorsSearchForm,
  forHistory: false,
};
provide('searchFormProvideData', searchFormProvideData);

// #endregion

// region mounted
onMounted(async () => {
  if (!listPageQueryParams.value.inventoryList) {
    listPageQueryParams.value.inventoryList = {};
  }

  if (!router.currentRoute.value?.query?.page) {
    sortModel.value = {
      value: listPageQueryParams.value.inventoryList.sortBy,
      label:
        sortByDropdownProvideData.sortByOptions.find(
          item => item.value === listPageQueryParams.value.inventoryList.sortBy
        )?.label || '',
    };
    isFirstLoad.value = true;
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  } else {
    const query = router.currentRoute.value.query;

    pageSize.value = +(query.limit || 10);
    pageIndex.value = +(query.page || 1);

    sortBySelectedLabel.value = query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(item => item.value === query.sortBy)?.label
      : '';

    sortModel.value = {
      value: query.sortBy || SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE,
      label: sortBySelectedLabel.value,
    };

    pagination.value = {
      ...pagination.value,
      page: +(query.page || 1),
      limit: +(query.limit || 10),
      sortBy: query.sortBy || SORT_BY_INVENTORY_ENUM.ARRIVAL_DATE,
      descending: query.descending === 'true',
    };

    const searchParams = {
      enterpriseName: query.enterpriseName || '',
      name: query.name || '',
      startDate: query.startDate || '',
      endDate: query.endDate || '',
      code: query.code || '',
      licenseNumber: query.licenseNumber || '',
    };

    searchForm.value = { ...searchParams };
    searchFormData.value = { ...searchParams };

    await getShipmentList();
    isFirstLoad.value = false;
  }
});
// #endregion
</script>

<style scoped></style>
