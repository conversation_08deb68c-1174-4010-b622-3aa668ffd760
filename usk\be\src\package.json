{"name": "usk-backend", "version": "0.0.0", "description": "usk API process.", "main": "server.js", "directories": {"test": "test"}, "scripts": {"test": "tap \"test/**/*.test.js\"", "start": "fastify start -l info app.js", "dev": "fastify start -w -l info -P app.js"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/autoload": "^6.3.1", "@fastify/awilix": "^8.0.0", "@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/sensible": "^6.0.3", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@fastify/view": "^11.1.0", "@prisma/client": "^6.10.1", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "ajv-keywords": "^5.1.0", "awilix": "^12.0.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "fastify": "^5.4.0", "fastify-cli": "^7.4.0", "fastify-plugin": "^5.0.1", "handlebars": "^4.7.8", "nodemailer": "^7.0.3", "short-unique-id": "^5.3.2", "sprintf-js": "^1.1.3", "traverse": "^0.6.11"}, "devDependencies": {"@eslint/js": "^9.32.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@faker-js/faker": "^9.8.0", "eslint": "^9.32.0", "globals": "^16.3.0", "prisma": "^6.10.1", "tap": "^21.1.0"}}