import BaseService from 'services/base.service';

class OutboundShipmentService extends BaseService {
  async registerOutboundShipment(body) {
    try {
      return this.dao.registerOutboundShipment(body);
    } catch (error) {
      return null;
    }
  }

  async checkPermissionForProxyOutboundShipment(proxyUserId) {
    try {
      return this.dao.checkPermissionForProxyOutboundShipment(proxyUserId);
    } catch (error) {
      return null;
    }
  }

  async registerProxyOutboundShipment(body) {
    try {
      return this.dao.registerProxyOutboundShipment(body);
    } catch (error) {
      return null;
    }
  }

  async getOutboundShipmentDetail(query) {
    try {
      return this.dao.getOutboundShipmentDetail(query);
    } catch (error) {
      return null;
    }
  }

  async getInventoryListOptions(query) {
    try {
      return this.dao.getInventoryListOptions(query);
    } catch (error) {
      return null;
    }
  }
}

export default new OutboundShipmentService('outbound-shipment');
