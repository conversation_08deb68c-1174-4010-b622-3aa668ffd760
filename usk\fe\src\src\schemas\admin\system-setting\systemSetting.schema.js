import ME<PERSON><PERSON><PERSON> from 'helpers/message';
import { ADMIN_SYSTEM_SETTING_KEYS_ENUM } from 'helpers/constants';

const adminSystemSettings = {
  type: 'object',
  additionalProperties: false,
  required: Object.values(ADMIN_SYSTEM_SETTING_KEYS_ENUM),
  properties: {
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]: {
      anyOf: [
        {
          type: 'integer',
          minimum: 0,
          not: { const: '' },
          errorMessage: {
            not: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
            type: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
            minimum: MESSAGE.MSG_WEIGHT_ALERT_THRESHOLD_MIN_ERROR,
            _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
          },
        },
      ],
    },
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL]: {
      type: 'integer',
      minimum: 0,
      not: { const: '' },
      errorMessage: {
        not: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
        minimum: MESSAGE.MSG_EDIT_DEADLINE_FOR_ARRIVAL_MIN_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    [ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD]: {
      type: 'integer',
      minimum: 0,
      not: { const: '' },
      errorMessage: {
        not: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
        minimum: MESSAGE.MSG_CATCHING_RESERVE_PERIOD_MIN_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
  errorMessage: {
    additionalProperties: 'No additional properties are allowed',
  },
};

export default adminSystemSettings;
