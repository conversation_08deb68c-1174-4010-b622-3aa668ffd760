{"name": "usk-backend", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "usk-backend", "version": "0.0.0", "license": "ISC", "dependencies": {"@fastify/autoload": "^6.3.1", "@fastify/awilix": "^8.0.0", "@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/sensible": "^6.0.3", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@fastify/view": "^11.1.0", "@prisma/client": "^6.10.1", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "ajv-keywords": "^5.1.0", "awilix": "^12.0.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "fastify": "^5.4.0", "fastify-cli": "^7.4.0", "fastify-plugin": "^5.0.1", "handlebars": "^4.7.8", "nodemailer": "^7.0.3", "short-unique-id": "^5.3.2", "sprintf-js": "^1.1.3", "traverse": "^0.6.11"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "prisma": "^6.10.1", "tap": "^21.1.0"}}, "node_modules/@alcalzone/ansi-tokenize": {"version": "0.1.3", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "is-fullwidth-code-point": "^4.0.0"}, "engines": {"node": ">=14.13.1"}}, "node_modules/@alcalzone/ansi-tokenize/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@alcalzone/ansi-tokenize/node_modules/is-fullwidth-code-point": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@base2/pretty-print-object": {"version": "1.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@bcoe/v8-coverage": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@faker-js/faker": {"version": "9.8.0", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/fakerjs"}], "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}, "node_modules/@fastify/accept-negotiator": {"version": "2.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT"}, "node_modules/@fastify/ajv-compiler": {"version": "4.0.2", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^3.0.1", "fast-uri": "^3.0.0"}}, "node_modules/@fastify/autoload": {"version": "6.3.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT"}, "node_modules/@fastify/awilix": {"version": "8.0.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"awilix-manager": "^6.0.0", "fastify-plugin": "^5.0.1"}, "peerDependencies": {"awilix": ">=9.0.0", "fastify": "^5.0.0"}}, "node_modules/@fastify/cookie": {"version": "11.0.2", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"cookie": "^1.0.0", "fastify-plugin": "^5.0.0"}}, "node_modules/@fastify/cors": {"version": "11.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"fastify-plugin": "^5.0.0", "toad-cache": "^3.7.0"}}, "node_modules/@fastify/deepmerge": {"version": "2.0.2", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT"}, "node_modules/@fastify/error": {"version": "4.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT"}, "node_modules/@fastify/fast-json-stringify-compiler": {"version": "5.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"fast-json-stringify": "^6.0.0"}}, "node_modules/@fastify/forwarded": {"version": "3.0.0", "license": "MIT"}, "node_modules/@fastify/jwt": {"version": "9.1.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@fastify/error": "^4.0.0", "@lukeed/ms": "^2.0.2", "fast-jwt": "^5.0.0", "fastify-plugin": "^5.0.0", "steed": "^1.1.3"}}, "node_modules/@fastify/merge-json-schemas": {"version": "0.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/@fastify/proxy-addr": {"version": "5.0.0", "license": "MIT", "dependencies": {"@fastify/forwarded": "^3.0.0", "ipaddr.js": "^2.1.0"}}, "node_modules/@fastify/send": {"version": "4.1.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@lukeed/ms": "^2.0.2", "escape-html": "~1.0.3", "fast-decode-uri-component": "^1.0.1", "http-errors": "^2.0.0", "mime": "^3"}}, "node_modules/@fastify/sensible": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@lukeed/ms": "^2.0.2", "dequal": "^2.0.3", "fastify-plugin": "^5.0.0", "forwarded": "^0.2.0", "http-errors": "^2.0.0", "type-is": "^1.6.18", "vary": "^1.1.2"}}, "node_modules/@fastify/static": {"version": "8.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@fastify/accept-negotiator": "^2.0.0", "@fastify/send": "^4.0.0", "content-disposition": "^0.5.4", "fastify-plugin": "^5.0.0", "fastq": "^1.17.1", "glob": "^11.0.0"}}, "node_modules/@fastify/swagger": {"version": "9.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"fastify-plugin": "^5.0.0", "json-schema-resolver": "^3.0.0", "openapi-types": "^12.1.3", "rfdc": "^1.3.1", "yaml": "^2.4.2"}}, "node_modules/@fastify/swagger-ui": {"version": "5.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@fastify/static": "^8.0.0", "fastify-plugin": "^5.0.0", "openapi-types": "^12.1.3", "rfdc": "^1.3.1", "yaml": "^2.4.1"}}, "node_modules/@fastify/view": {"version": "11.1.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"fastify-plugin": "^5.0.0", "toad-cache": "^3.7.0"}}, "node_modules/@isaacs/balanced-match": {"version": "4.0.1", "license": "MIT", "engines": {"node": "20 || >=22"}}, "node_modules/@isaacs/brace-expansion": {"version": "5.0.0", "license": "MIT", "dependencies": {"@isaacs/balanced-match": "^4.0.1"}, "engines": {"node": "20 || >=22"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/ts-node-temp-fork-for-pr-2009": {"version": "10.9.7", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node14": "*", "@tsconfig/node16": "*", "@tsconfig/node18": "*", "@tsconfig/node20": "*", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=4.2"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/@isaacs/ts-node-temp-fork-for-pr-2009/node_modules/diff": {"version": "4.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.3", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@lukeed/ms": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@npmcli/agent": {"version": "2.2.2", "dev": true, "license": "ISC", "dependencies": {"agent-base": "^7.1.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.1", "lru-cache": "^10.0.1", "socks-proxy-agent": "^8.0.3"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@npmcli/agent/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/@npmcli/fs": {"version": "3.1.1", "dev": true, "license": "ISC", "dependencies": {"semver": "^7.3.5"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/git": {"version": "5.0.8", "dev": true, "license": "ISC", "dependencies": {"@npmcli/promise-spawn": "^7.0.0", "ini": "^4.1.3", "lru-cache": "^10.0.1", "npm-pick-manifest": "^9.0.0", "proc-log": "^4.0.0", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^4.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@npmcli/git/node_modules/isexe": {"version": "3.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/@npmcli/git/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/@npmcli/git/node_modules/which": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/installed-package-contents": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"npm-bundled": "^3.0.0", "npm-normalize-package-bin": "^3.0.0"}, "bin": {"installed-package-contents": "bin/index.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/node-gyp": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/package-json": {"version": "5.2.1", "dev": true, "license": "ISC", "dependencies": {"@npmcli/git": "^5.0.0", "glob": "^10.2.2", "hosted-git-info": "^7.0.0", "json-parse-even-better-errors": "^3.0.0", "normalize-package-data": "^6.0.0", "proc-log": "^4.0.0", "semver": "^7.5.3"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@npmcli/package-json/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@npmcli/package-json/node_modules/jackspeak": {"version": "3.4.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/@npmcli/package-json/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/@npmcli/package-json/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@npmcli/package-json/node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@npmcli/promise-spawn": {"version": "7.0.2", "dev": true, "license": "ISC", "dependencies": {"which": "^4.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@npmcli/promise-spawn/node_modules/isexe": {"version": "3.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/@npmcli/promise-spawn/node_modules/which": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/redact": {"version": "2.0.1", "dev": true, "license": "ISC", "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@npmcli/run-script": {"version": "8.1.0", "dev": true, "license": "ISC", "dependencies": {"@npmcli/node-gyp": "^3.0.0", "@npmcli/package-json": "^5.0.0", "@npmcli/promise-spawn": "^7.0.0", "node-gyp": "^10.0.0", "proc-log": "^4.0.0", "which": "^4.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@npmcli/run-script/node_modules/isexe": {"version": "3.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/@npmcli/run-script/node_modules/which": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@prisma/client": {"version": "6.10.1", "hasInstallScript": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "peerDependencies": {"prisma": "*", "typescript": ">=5.1.0"}, "peerDependenciesMeta": {"prisma": {"optional": true}, "typescript": {"optional": true}}}, "node_modules/@prisma/config": {"version": "6.10.1", "devOptional": true, "license": "Apache-2.0", "dependencies": {"jiti": "2.4.2"}}, "node_modules/@prisma/debug": {"version": "6.10.1", "devOptional": true, "license": "Apache-2.0"}, "node_modules/@prisma/engines": {"version": "6.10.1", "devOptional": true, "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"@prisma/debug": "6.10.1", "@prisma/engines-version": "6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c", "@prisma/fetch-engine": "6.10.1", "@prisma/get-platform": "6.10.1"}}, "node_modules/@prisma/engines-version": {"version": "6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c", "devOptional": true, "license": "Apache-2.0"}, "node_modules/@prisma/fetch-engine": {"version": "6.10.1", "devOptional": true, "license": "Apache-2.0", "dependencies": {"@prisma/debug": "6.10.1", "@prisma/engines-version": "6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c", "@prisma/get-platform": "6.10.1"}}, "node_modules/@prisma/get-platform": {"version": "6.10.1", "devOptional": true, "license": "Apache-2.0", "dependencies": {"@prisma/debug": "6.10.1"}}, "node_modules/@sigstore/bundle": {"version": "2.3.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/protobuf-specs": "^0.3.2"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@sigstore/core": {"version": "1.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@sigstore/protobuf-specs": {"version": "0.3.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/@sigstore/sign": {"version": "2.3.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/bundle": "^2.3.2", "@sigstore/core": "^1.0.0", "@sigstore/protobuf-specs": "^0.3.2", "make-fetch-happen": "^13.0.1", "proc-log": "^4.2.0", "promise-retry": "^2.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@sigstore/tuf": {"version": "2.3.4", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/protobuf-specs": "^0.3.2", "tuf-js": "^2.2.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@sigstore/verify": {"version": "1.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/bundle": "^2.3.2", "@sigstore/core": "^1.1.0", "@sigstore/protobuf-specs": "^0.3.2"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@tapjs/after": {"version": "3.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"is-actual-promise": "^1.0.1"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/after-each": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"function-loop": "^4.0.0"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/asserts": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/stack": "4.0.0", "is-actual-promise": "^1.0.1", "tcompare": "9.0.0", "trivial-deferred": "^2.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/before": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"is-actual-promise": "^1.0.1"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/before-each": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"function-loop": "^4.0.0"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/chdir": {"version": "3.0.1", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/config": {"version": "5.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/core": "4.0.1", "@tapjs/test": "4.0.1", "chalk": "^5.2.0", "jackspeak": "^4.0.1", "polite-json": "^5.0.0", "tap-yaml": "4.0.0", "walk-up-path": "^4.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1", "@tapjs/test": "4.0.1"}}, "node_modules/@tapjs/config/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@tapjs/core": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/processinfo": "^3.1.8", "@tapjs/stack": "4.0.0", "@tapjs/test": "4.0.1", "async-hook-domain": "^4.0.1", "diff": "^5.2.0", "is-actual-promise": "^1.0.1", "minipass": "^7.0.4", "signal-exit": "4.1", "tap-parser": "18.0.0", "tap-yaml": "4.0.0", "tcompare": "9.0.0", "trivial-deferred": "^2.0.0"}, "engines": {"node": "20 || >=22"}}, "node_modules/@tapjs/error-serdes": {"version": "4.0.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"minipass": "^7.0.4"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@tapjs/filter": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/fixture": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"mkdirp": "^3.0.0", "rimraf": "^6.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/intercept": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/after": "3.0.1", "@tapjs/stack": "4.0.0"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/mock": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/after": "3.0.1", "@tapjs/stack": "4.0.0", "resolve-import": "^2.0.0", "walk-up-path": "^4.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/node-serialize": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/error-serdes": "4.0.0", "@tapjs/stack": "4.0.0", "tap-parser": "18.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/processinfo": {"version": "3.1.8", "dev": true, "license": "ISC", "dependencies": {"pirates": "^4.0.5", "process-on-spawn": "^1.0.0", "signal-exit": "^4.0.2", "uuid": "^8.3.2"}, "engines": {"node": ">=16.17"}}, "node_modules/@tapjs/reporter": {"version": "4.0.2", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/config": "5.0.1", "@tapjs/stack": "4.0.0", "chalk": "^5.2.0", "ink": "^5.0.1", "minipass": "^7.0.4", "ms": "^2.1.3", "patch-console": "^2.0.0", "prismjs-terminal": "^1.2.3", "react": "^18.2.0", "string-length": "^6.0.0", "tap-parser": "18.0.0", "tap-yaml": "4.0.0", "tcompare": "9.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/reporter/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@tapjs/run": {"version": "4.0.2", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/after": "3.0.1", "@tapjs/before": "4.0.1", "@tapjs/config": "5.0.1", "@tapjs/processinfo": "^3.1.8", "@tapjs/reporter": "4.0.2", "@tapjs/spawn": "4.0.1", "@tapjs/stdin": "4.0.1", "@tapjs/test": "4.0.1", "c8": "^10.1.2", "chalk": "^5.3.0", "chokidar": "^3.6.0", "foreground-child": "^3.1.1", "glob": "^11.0.0", "minipass": "^7.0.4", "mkdirp": "^3.0.1", "opener": "^1.5.2", "pacote": "^18.0.6", "path-scurry": "^2.0.0", "resolve-import": "^2.0.0", "rimraf": "^6.0.0", "semver": "^7.6.0", "signal-exit": "^4.1.0", "tap-parser": "18.0.0", "tap-yaml": "4.0.0", "tcompare": "9.0.0", "trivial-deferred": "^2.0.0", "which": "^4.0.0"}, "bin": {"tap-run": "dist/esm/index.js"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/run/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@tapjs/run/node_modules/chokidar": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/@tapjs/run/node_modules/isexe": {"version": "3.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/@tapjs/run/node_modules/readdirp": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/@tapjs/run/node_modules/which": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "node_modules/@tapjs/snapshot": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"is-actual-promise": "^1.0.1", "tcompare": "9.0.0", "trivial-deferred": "^2.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/spawn": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/stack": {"version": "4.0.0", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@tapjs/stdin": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/test": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/ts-node-temp-fork-for-pr-2009": "^10.9.7", "@tapjs/after": "3.0.1", "@tapjs/after-each": "4.0.1", "@tapjs/asserts": "4.0.1", "@tapjs/before": "4.0.1", "@tapjs/before-each": "4.0.1", "@tapjs/chdir": "3.0.1", "@tapjs/filter": "4.0.1", "@tapjs/fixture": "4.0.1", "@tapjs/intercept": "4.0.1", "@tapjs/mock": "4.0.1", "@tapjs/node-serialize": "4.0.1", "@tapjs/snapshot": "4.0.1", "@tapjs/spawn": "4.0.1", "@tapjs/stdin": "4.0.1", "@tapjs/typescript": "3.1.0", "@tapjs/worker": "4.0.1", "glob": "11", "jackspeak": "^4.0.1", "mkdirp": "^3.0.0", "package-json-from-dist": "^1.0.0", "resolve-import": "^2.0.0", "rimraf": "^6.0.0", "sync-content": "^2.0.1", "tap-parser": "18.0.0", "tshy": "^3.0.2", "typescript": "5.5", "walk-up-path": "^4.0.0"}, "bin": {"generate-tap-test-class": "dist/esm/build.mjs"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/typescript": {"version": "3.1.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/ts-node-temp-fork-for-pr-2009": "^10.9.7"}, "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tapjs/worker": {"version": "4.0.1", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": "20 || >=22"}, "peerDependencies": {"@tapjs/core": "4.0.1"}}, "node_modules/@tsconfig/node14": {"version": "14.1.4", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "16.1.4", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node18": {"version": "18.2.4", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node20": {"version": "20.1.6", "dev": true, "license": "MIT"}, "node_modules/@tufjs/canonical-json": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@tufjs/models": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"@tufjs/canonical-json": "2.0.0", "minimatch": "^9.0.4"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/@tufjs/models/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "24.0.8", "dev": true, "license": "MIT", "peer": true, "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/abbrev": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/abstract-logging": {"version": "2.0.1", "license": "MIT"}, "node_modules/acorn": {"version": "8.15.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "7.1.3", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/aggregate-error": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/aggregate-error/node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-errors": {"version": "3.0.0", "license": "MIT", "peerDependencies": {"ajv": "^8.0.1"}}, "node_modules/ajv-formats": {"version": "3.0.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-keywords": {"version": "5.1.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/ansi-escapes": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"environment": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "4.1.3", "dev": true, "license": "MIT"}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/asn1.js": {"version": "5.4.1", "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "safer-buffer": "^2.1.0"}}, "node_modules/async-function": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/async-hook-domain": {"version": "4.0.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/atomic-sleep": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/auto-bind": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/avvio": {"version": "9.1.0", "license": "MIT", "dependencies": {"@fastify/error": "^4.0.0", "fastq": "^1.17.1"}}, "node_modules/awilix": {"version": "12.0.5", "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "fast-glob": "^3.3.3"}, "engines": {"node": ">=16.3.0"}}, "node_modules/awilix-manager": {"version": "6.1.0", "engines": {"node": ">=16"}, "peerDependencies": {"awilix": ">=9.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/binary-extensions": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bn.js": {"version": "4.12.2", "license": "MIT"}, "node_modules/brace-expansion": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/c8": {"version": "10.1.3", "dev": true, "license": "ISC", "dependencies": {"@bcoe/v8-coverage": "^1.0.1", "@istanbuljs/schema": "^0.1.3", "find-up": "^5.0.0", "foreground-child": "^3.1.1", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-report": "^3.0.1", "istanbul-reports": "^3.1.6", "test-exclude": "^7.0.1", "v8-to-istanbul": "^9.0.0", "yargs": "^17.7.2", "yargs-parser": "^21.1.1"}, "bin": {"c8": "bin/c8.js"}, "engines": {"node": ">=18"}, "peerDependencies": {"monocart-coverage-reports": "^2"}, "peerDependenciesMeta": {"monocart-coverage-reports": {"optional": true}}}, "node_modules/c8/node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/c8/node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/c8/node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/c8/node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/c8/node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cacache": {"version": "18.0.4", "dev": true, "license": "ISC", "dependencies": {"@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "glob": "^10.2.2", "lru-cache": "^10.0.1", "minipass": "^7.0.3", "minipass-collect": "^2.0.1", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "p-map": "^4.0.0", "ssri": "^10.0.0", "tar": "^6.1.11", "unique-filename": "^3.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/cacache/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/cacache/node_modules/jackspeak": {"version": "3.4.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/cacache/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/cacache/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/cacache/node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/call-bind": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/camel-case": {"version": "4.1.2", "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chokidar": {"version": "4.0.3", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/chownr": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/clean-stack": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-boxes": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-cursor": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/cli-truncate/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/cli-truncate/node_modules/is-fullwidth-code-point": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate/node_modules/slice-ansi": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.0.0", "is-fullwidth-code-point": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/cli-truncate/node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cliui": {"version": "8.0.1", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/cliui/node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/close-with-grace": {"version": "2.2.0", "license": "MIT"}, "node_modules/code-excerpt": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"convert-to-spaces": "^2.0.1"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "license": "MIT"}, "node_modules/commist": {"version": "3.2.0", "license": "MIT"}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/convert-to-spaces": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/cookie": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/data-view-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/dateformat": {"version": "4.6.3", "license": "MIT", "engines": {"node": "*"}}, "node_modules/dayjs": {"version": "1.11.13", "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dequal": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/diff": {"version": "5.2.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dotenv": {"version": "16.6.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/encoding": {"version": "0.1.13", "dev": true, "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/environment": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/err-code": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/es-abstract": {"version": "1.24.0", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-toolkit": {"version": "1.39.5", "dev": true, "license": "MIT", "workspaces": ["docs", "benchmarks"]}, "node_modules/escalade": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/events-to-array": {"version": "2.0.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/exponential-backoff": {"version": "3.1.2", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-copy": {"version": "3.0.2", "license": "MIT"}, "node_modules/fast-decode-uri-component": {"version": "1.0.1", "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stringify": {"version": "6.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@fastify/merge-json-schemas": "^0.2.0", "ajv": "^8.12.0", "ajv-formats": "^3.0.1", "fast-uri": "^3.0.0", "json-schema-ref-resolver": "^2.0.0", "rfdc": "^1.2.0"}}, "node_modules/fast-jwt": {"version": "5.0.6", "license": "Apache-2.0", "dependencies": {"@lukeed/ms": "^2.0.2", "asn1.js": "^5.4.1", "ecdsa-sig-formatter": "^1.0.11", "mnemonist": "^0.40.0"}, "engines": {"node": ">=20"}}, "node_modules/fast-querystring": {"version": "1.1.2", "license": "MIT", "dependencies": {"fast-decode-uri-component": "^1.0.1"}}, "node_modules/fast-redact": {"version": "3.5.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/fast-safe-stringify": {"version": "2.1.1", "license": "MIT"}, "node_modules/fast-uri": {"version": "3.0.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/fastfall": {"version": "1.5.1", "license": "MIT", "dependencies": {"reusify": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fastify": {"version": "5.4.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"@fastify/ajv-compiler": "^4.0.0", "@fastify/error": "^4.0.0", "@fastify/fast-json-stringify-compiler": "^5.0.0", "@fastify/proxy-addr": "^5.0.0", "abstract-logging": "^2.0.1", "avvio": "^9.0.0", "fast-json-stringify": "^6.0.0", "find-my-way": "^9.0.0", "light-my-request": "^6.0.0", "pino": "^9.0.0", "process-warning": "^5.0.0", "rfdc": "^1.3.1", "secure-json-parse": "^4.0.0", "semver": "^7.6.0", "toad-cache": "^3.7.0"}}, "node_modules/fastify-cli": {"version": "7.4.0", "license": "MIT", "dependencies": {"@fastify/deepmerge": "^2.0.0", "chalk": "^4.1.2", "chokidar": "^4.0.0", "close-with-grace": "^2.1.0", "commist": "^3.0.0", "dotenv": "^16.0.0", "fastify": "^5.0.0", "fastify-plugin": "^5.0.0", "generify": "^4.0.0", "help-me": "^5.0.0", "is-docker": "^2.0.0", "pino-pretty": "^13.0.0", "pkg-up": "^3.1.0", "resolve-from": "^5.0.0", "semver": "^7.3.5", "yargs-parser": "^21.1.1"}, "bin": {"fastify": "cli.js"}}, "node_modules/fastify-plugin": {"version": "5.0.1", "license": "MIT"}, "node_modules/fastparallel": {"version": "2.4.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4", "xtend": "^4.0.2"}}, "node_modules/fastq": {"version": "1.17.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fastseries": {"version": "1.7.2", "license": "ISC", "dependencies": {"reusify": "^1.0.0", "xtend": "^4.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-my-way": {"version": "9.3.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-querystring": "^1.0.0", "safe-regex2": "^5.0.0"}, "engines": {"node": ">=20"}}, "node_modules/find-up": {"version": "3.0.0", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/for-each": {"version": "0.3.5", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/foreground-child": {"version": "3.3.1", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fromentries": {"version": "1.3.2", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/fs-minipass": {"version": "3.0.3", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.3"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/fsevents": {"version": "2.3.3", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function-loop": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/function.prototype.name": {"version": "1.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/generify": {"version": "4.2.0", "license": "MIT", "dependencies": {"isbinaryfile": "^4.0.2", "pump": "^3.0.0", "split2": "^3.0.0", "walker": "^1.0.6"}, "bin": {"generify": "generify.js"}}, "node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-east-asian-width": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/glob": {"version": "11.0.3", "license": "ISC", "dependencies": {"foreground-child": "^3.3.1", "jackspeak": "^4.1.1", "minimatch": "^10.0.3", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^2.0.0"}, "bin": {"glob": "dist/esm/bin.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/globalthis": {"version": "1.0.4", "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC"}, "node_modules/handlebars": {"version": "4.7.8", "license": "MIT", "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.2", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^3.1.4"}}, "node_modules/has-bigints": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/help-me": {"version": "5.0.0", "license": "MIT"}, "node_modules/hosted-git-info": {"version": "7.0.2", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^10.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/hosted-git-info/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/html-escaper": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/http-cache-semantics": {"version": "4.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore-walk": {"version": "6.0.5", "dev": true, "license": "ISC", "dependencies": {"minimatch": "^9.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/ignore-walk/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "4.1.3", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/ink": {"version": "5.2.1", "dev": true, "license": "MIT", "dependencies": {"@alcalzone/ansi-tokenize": "^0.1.3", "ansi-escapes": "^7.0.0", "ansi-styles": "^6.2.1", "auto-bind": "^5.0.1", "chalk": "^5.3.0", "cli-boxes": "^3.0.0", "cli-cursor": "^4.0.0", "cli-truncate": "^4.0.0", "code-excerpt": "^4.0.0", "es-toolkit": "^1.22.0", "indent-string": "^5.0.0", "is-in-ci": "^1.0.0", "patch-console": "^2.0.0", "react-reconciler": "^0.29.0", "scheduler": "^0.23.0", "signal-exit": "^3.0.7", "slice-ansi": "^7.1.0", "stack-utils": "^2.0.6", "string-width": "^7.2.0", "type-fest": "^4.27.0", "widest-line": "^5.0.0", "wrap-ansi": "^9.0.0", "ws": "^8.18.0", "yoga-layout": "~3.2.1"}, "engines": {"node": ">=18"}, "peerDependencies": {"@types/react": ">=18.0.0", "react": ">=18.0.0", "react-devtools-core": "^4.19.1"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "react-devtools-core": {"optional": true}}}, "node_modules/ink/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ink/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/ink/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/ink/node_modules/signal-exit": {"version": "3.0.7", "dev": true, "license": "ISC"}, "node_modules/ink/node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ink/node_modules/wrap-ansi": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "string-width": "^7.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/internal-slot": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ip-address": {"version": "9.0.5", "dev": true, "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ipaddr.js": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/is-actual-promise": {"version": "1.0.2", "dev": true, "license": "BlueOak-1.0.0"}, "node_modules/is-array-buffer": {"version": "3.0.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-async-function": {"version": "2.1.1", "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-view": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-in-ci": {"version": "1.0.0", "dev": true, "license": "MIT", "bin": {"is-in-ci": "cli.js"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-lambda": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-map": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-plain-object": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-string": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakmap": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/isbinaryfile": {"version": "4.0.10", "license": "MIT", "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jackspeak": {"version": "4.1.1", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/jiti": {"version": "2.4.2", "devOptional": true, "license": "MIT", "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/joycon": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/js-tokens": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/jsbn": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "3.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/json-schema-ref-resolver": {"version": "2.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/json-schema-resolver": {"version": "3.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.1", "fast-uri": "^3.0.5", "rfdc": "^1.1.4"}, "engines": {"node": ">=20"}, "funding": {"url": "https://github.com/Eomm/json-schema-resolver?sponsor=1"}}, "node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/jsonparse": {"version": "1.3.1", "dev": true, "engines": ["node >= 0.2.0"], "license": "MIT"}, "node_modules/light-my-request": {"version": "6.6.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"cookie": "^1.0.1", "process-warning": "^4.0.0", "set-cookie-parser": "^2.6.0"}}, "node_modules/light-my-request/node_modules/process-warning": {"version": "4.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT"}, "node_modules/locate-path": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/loose-envify": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "2.0.2", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lru-cache": {"version": "11.1.0", "license": "ISC", "engines": {"node": "20 || >=22"}}, "node_modules/make-dir": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-error": {"version": "1.3.6", "dev": true, "license": "ISC"}, "node_modules/make-fetch-happen": {"version": "13.0.1", "dev": true, "license": "ISC", "dependencies": {"@npmcli/agent": "^2.0.0", "cacache": "^18.0.0", "http-cache-semantics": "^4.1.1", "is-lambda": "^1.0.1", "minipass": "^7.0.2", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "proc-log": "^4.2.0", "promise-retry": "^2.0.1", "ssri": "^10.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/makeerror": {"version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "3.0.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimatch": {"version": "10.0.3", "license": "ISC", "dependencies": {"@isaacs/brace-expansion": "^5.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/minipass-collect": {"version": "2.0.1", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.3"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/minipass-fetch": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.0.3", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/minipass-flush": {"version": "1.0.5", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-flush/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-pipeline": {"version": "1.2.4", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-pipeline/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized": {"version": "1.0.3", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mkdirp": {"version": "3.0.1", "dev": true, "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mnemonist": {"version": "0.40.3", "license": "MIT", "dependencies": {"obliterator": "^2.0.4"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.4", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "node_modules/no-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-gyp": {"version": "10.3.1", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.0", "exponential-backoff": "^3.1.1", "glob": "^10.3.10", "graceful-fs": "^4.2.6", "make-fetch-happen": "^13.0.0", "nopt": "^7.0.0", "proc-log": "^4.1.0", "semver": "^7.3.5", "tar": "^6.2.1", "which": "^4.0.0"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/node-gyp/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/node-gyp/node_modules/isexe": {"version": "3.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/node-gyp/node_modules/jackspeak": {"version": "3.4.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/node-gyp/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/node-gyp/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/node-gyp/node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/node-gyp/node_modules/which": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "node_modules/nodemailer": {"version": "7.0.4", "license": "MIT-0", "engines": {"node": ">=6.0.0"}}, "node_modules/nopt": {"version": "7.2.1", "dev": true, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/normalize-package-data": {"version": "6.0.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^7.0.0", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-bundled": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"npm-normalize-package-bin": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-install-checks": {"version": "6.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^7.1.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-normalize-package-bin": {"version": "3.0.1", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-package-arg": {"version": "11.0.3", "dev": true, "license": "ISC", "dependencies": {"hosted-git-info": "^7.0.0", "proc-log": "^4.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^5.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/npm-packlist": {"version": "8.0.2", "dev": true, "license": "ISC", "dependencies": {"ignore-walk": "^6.0.4"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-pick-manifest": {"version": "9.1.0", "dev": true, "license": "ISC", "dependencies": {"npm-install-checks": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "npm-package-arg": "^11.0.0", "semver": "^7.3.5"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/npm-registry-fetch": {"version": "17.1.0", "dev": true, "license": "ISC", "dependencies": {"@npmcli/redact": "^2.0.0", "jsonparse": "^1.3.1", "make-fetch-happen": "^13.0.0", "minipass": "^7.0.2", "minipass-fetch": "^3.0.0", "minizlib": "^2.1.2", "npm-package-arg": "^11.0.0", "proc-log": "^4.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/obliterator": {"version": "2.0.5", "license": "MIT"}, "node_modules/on-exit-leak-free": {"version": "2.1.2", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openapi-types": {"version": "12.1.3", "license": "MIT"}, "node_modules/opener": {"version": "1.5.2", "dev": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/own-keys": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-map": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "node_modules/pacote": {"version": "18.0.6", "dev": true, "license": "ISC", "dependencies": {"@npmcli/git": "^5.0.0", "@npmcli/installed-package-contents": "^2.0.1", "@npmcli/package-json": "^5.1.0", "@npmcli/promise-spawn": "^7.0.0", "@npmcli/run-script": "^8.0.0", "cacache": "^18.0.0", "fs-minipass": "^3.0.0", "minipass": "^7.0.2", "npm-package-arg": "^11.0.0", "npm-packlist": "^8.0.0", "npm-pick-manifest": "^9.0.0", "npm-registry-fetch": "^17.0.0", "proc-log": "^4.0.0", "promise-retry": "^2.0.1", "sigstore": "^2.2.0", "ssri": "^10.0.0", "tar": "^6.1.11"}, "bin": {"pacote": "bin/index.js"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/pascal-case": {"version": "3.1.2", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/patch-console": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/path-exists": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-scurry": {"version": "2.0.0", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^11.0.0", "minipass": "^7.1.2"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pino": {"version": "9.7.0", "license": "MIT", "dependencies": {"atomic-sleep": "^1.0.0", "fast-redact": "^3.1.1", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^2.0.0", "pino-std-serializers": "^7.0.0", "process-warning": "^5.0.0", "quick-format-unescaped": "^4.0.3", "real-require": "^0.2.0", "safe-stable-stringify": "^2.3.1", "sonic-boom": "^4.0.1", "thread-stream": "^3.0.0"}, "bin": {"pino": "bin.js"}}, "node_modules/pino-abstract-transport": {"version": "2.0.0", "license": "MIT", "dependencies": {"split2": "^4.0.0"}}, "node_modules/pino-abstract-transport/node_modules/split2": {"version": "4.2.0", "license": "ISC", "engines": {"node": ">= 10.x"}}, "node_modules/pino-pretty": {"version": "13.0.0", "license": "MIT", "dependencies": {"colorette": "^2.0.7", "dateformat": "^4.6.3", "fast-copy": "^3.0.2", "fast-safe-stringify": "^2.1.1", "help-me": "^5.0.0", "joycon": "^3.1.1", "minimist": "^1.2.6", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^2.0.0", "pump": "^3.0.0", "secure-json-parse": "^2.4.0", "sonic-boom": "^4.0.1", "strip-json-comments": "^3.1.1"}, "bin": {"pino-pretty": "bin.js"}}, "node_modules/pino-pretty/node_modules/secure-json-parse": {"version": "2.7.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/pino-std-serializers": {"version": "7.0.0", "license": "MIT"}, "node_modules/pirates": {"version": "4.0.7", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-up": {"version": "3.1.0", "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/polite-json": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/prisma": {"version": "6.10.1", "devOptional": true, "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"@prisma/config": "6.10.1", "@prisma/engines": "6.10.1"}, "bin": {"prisma": "build/index.js"}, "engines": {"node": ">=18.18"}, "peerDependencies": {"typescript": ">=5.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/prismjs": {"version": "1.30.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/prismjs-terminal": {"version": "1.2.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"chalk": "^5.2.0", "prismjs": "^1.29.0", "string-length": "^6.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/prismjs-terminal/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/proc-log": {"version": "4.2.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/process-on-spawn": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"fromentries": "^1.2.0"}, "engines": {"node": ">=8"}}, "node_modules/process-warning": {"version": "5.0.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT"}, "node_modules/promise-inflight": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/promise-retry": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/pump": {"version": "3.0.0", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quick-format-unescaped": {"version": "4.0.4", "license": "MIT"}, "node_modules/react": {"version": "18.3.1", "dev": true, "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-element-to-jsx-string": {"version": "15.0.0", "dev": true, "license": "MIT", "dependencies": {"@base2/pretty-print-object": "1.0.1", "is-plain-object": "5.0.0", "react-is": "18.1.0"}, "peerDependencies": {"react": "^0.14.8 || ^15.0.1 || ^16.0.0 || ^17.0.1 || ^18.0.0", "react-dom": "^0.14.8 || ^15.0.1 || ^16.0.0 || ^17.0.1 || ^18.0.0"}}, "node_modules/react-is": {"version": "18.1.0", "dev": true, "license": "MIT"}, "node_modules/react-reconciler": {"version": "0.29.2", "dev": true, "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "4.1.2", "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/real-require": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 12.13.0"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/require-directory": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-import": {"version": "2.0.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"glob": "^11.0.0", "walk-up-path": "^4.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/restore-cursor": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/restore-cursor/node_modules/signal-exit": {"version": "3.0.7", "dev": true, "license": "ISC"}, "node_modules/ret": {"version": "0.5.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/retry": {"version": "0.12.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.3.1", "license": "MIT"}, "node_modules/rimraf": {"version": "6.0.1", "dev": true, "license": "ISC", "dependencies": {"glob": "^11.0.0", "package-json-from-dist": "^1.0.0"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-push-apply": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex2": {"version": "5.0.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "MIT", "dependencies": {"ret": "~0.5.0"}}, "node_modules/safe-stable-stringify": {"version": "2.5.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/scheduler": {"version": "0.23.2", "dev": true, "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/secure-json-parse": {"version": "4.0.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/semver": {"version": "7.6.0", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/set-cookie-parser": {"version": "2.7.1", "license": "MIT"}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/short-unique-id": {"version": "5.3.2", "license": "Apache-2.0", "bin": {"short-unique-id": "bin/short-unique-id", "suid": "bin/short-unique-id"}}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/sigstore": {"version": "2.3.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/bundle": "^2.3.2", "@sigstore/core": "^1.0.0", "@sigstore/protobuf-specs": "^0.3.2", "@sigstore/sign": "^2.3.2", "@sigstore/tuf": "^2.3.4", "@sigstore/verify": "^1.2.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/slice-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "is-fullwidth-code-point": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slice-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/slice-ansi/node_modules/is-fullwidth-code-point": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"get-east-asian-width": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/smart-buffer": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.8.5", "dev": true, "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "8.0.5", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}, "engines": {"node": ">= 14"}}, "node_modules/sonic-boom": {"version": "4.2.0", "license": "MIT", "dependencies": {"atomic-sleep": "^1.0.0"}}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/spdx-correct": {"version": "3.2.0", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.21", "dev": true, "license": "CC0-1.0"}, "node_modules/split2": {"version": "3.2.2", "license": "ISC", "dependencies": {"readable-stream": "^3.0.0"}}, "node_modules/sprintf-js": {"version": "1.1.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ssri": {"version": "10.0.6", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.3"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/stack-utils": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/steed": {"version": "1.1.3", "license": "MIT", "dependencies": {"fastfall": "^1.5.0", "fastparallel": "^2.2.0", "fastq": "^1.3.0", "fastseries": "^1.7.0", "reusify": "^1.0.0"}}, "node_modules/stop-iteration-iterator": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-length": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"strip-ansi": "^7.1.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/sync-content": {"version": "2.0.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"glob": "^11.0.0", "mkdirp": "^3.0.1", "path-scurry": "^2.0.0", "rimraf": "^6.0.0", "tshy": "^3.0.0"}, "bin": {"sync-content": "dist/esm/bin.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/tap": {"version": "21.1.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@tapjs/after": "3.0.1", "@tapjs/after-each": "4.0.1", "@tapjs/asserts": "4.0.1", "@tapjs/before": "4.0.1", "@tapjs/before-each": "4.0.1", "@tapjs/chdir": "3.0.1", "@tapjs/core": "4.0.1", "@tapjs/filter": "4.0.1", "@tapjs/fixture": "4.0.1", "@tapjs/intercept": "4.0.1", "@tapjs/mock": "4.0.1", "@tapjs/node-serialize": "4.0.1", "@tapjs/run": "4.0.2", "@tapjs/snapshot": "4.0.1", "@tapjs/spawn": "4.0.1", "@tapjs/stdin": "4.0.1", "@tapjs/test": "4.0.1", "@tapjs/typescript": "3.1.0", "@tapjs/worker": "4.0.1", "resolve-import": "2"}, "bin": {"tap": "dist/esm/run.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/tap-parser": {"version": "18.0.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"events-to-array": "^2.0.3", "tap-yaml": "4.0.0"}, "bin": {"tap-parser": "bin/cmd.cjs"}, "engines": {"node": "20 || >=22"}}, "node_modules/tap-yaml": {"version": "4.0.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"yaml": "^2.4.1", "yaml-types": "^0.4.0"}, "engines": {"node": "20 || >=22"}}, "node_modules/tar": {"version": "6.2.1", "dev": true, "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar/node_modules/fs-minipass": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/tar/node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tar/node_modules/minipass": {"version": "5.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/tar/node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/tcompare": {"version": "9.0.0", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"diff": "^5.2.0", "react-element-to-jsx-string": "^15.0.0"}, "engines": {"node": "20 || >=22"}}, "node_modules/test-exclude": {"version": "7.0.1", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^10.4.1", "minimatch": "^9.0.4"}, "engines": {"node": ">=18"}}, "node_modules/test-exclude/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/test-exclude/node_modules/jackspeak": {"version": "3.4.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/test-exclude/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/test-exclude/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/test-exclude/node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/thread-stream": {"version": "3.1.0", "license": "MIT", "dependencies": {"real-require": "^0.2.0"}}, "node_modules/tmpl": {"version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toad-cache": {"version": "3.7.0", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/traverse": {"version": "0.6.11", "license": "MIT", "dependencies": {"gopd": "^1.2.0", "typedarray.prototype.slice": "^1.0.5", "which-typed-array": "^1.1.18"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/trivial-deferred": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 8"}}, "node_modules/tshy": {"version": "3.0.2", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"chalk": "^5.3.0", "chokidar": "^3.6.0", "foreground-child": "^3.1.1", "minimatch": "^10.0.0", "mkdirp": "^3.0.1", "polite-json": "^5.0.0", "resolve-import": "^2.0.0", "rimraf": "^6.0.0", "sync-content": "^2.0.1", "typescript": "^5.5.3", "walk-up-path": "^4.0.0"}, "bin": {"tshy": "dist/esm/index.js"}, "engines": {"node": "20 || >=22"}}, "node_modules/tshy/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/tshy/node_modules/chokidar": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/tshy/node_modules/readdirp": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/tuf-js": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"@tufjs/models": "2.0.1", "debug": "^4.3.4", "make-fetch-happen": "^13.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/type-fest": {"version": "4.41.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typedarray.prototype.slice": {"version": "1.0.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "math-intrinsics": "^1.1.0", "typed-array-buffer": "^1.0.3", "typed-array-byte-offset": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.5.4", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/uglify-js": {"version": "3.17.4", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici-types": {"version": "7.8.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/unique-filename": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"unique-slug": "^4.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/unique-slug": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/uuid": {"version": "8.3.2", "dev": true, "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/v8-to-istanbul": {"version": "9.3.0", "dev": true, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/v8-to-istanbul/node_modules/@jridgewell/trace-mapping": {"version": "0.3.28", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/validate-npm-package-name": {"version": "5.0.1", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/walk-up-path": {"version": "4.0.0", "dev": true, "license": "ISC", "engines": {"node": "20 || >=22"}}, "node_modules/walker": {"version": "1.0.8", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.19", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/widest-line": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"string-width": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/widest-line/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/widest-line/node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/wordwrap": {"version": "1.0.0", "license": "MIT"}, "node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/wrap-ansi-cjs/node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/ws": {"version": "8.18.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xtend": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/yaml": {"version": "2.8.0", "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yaml-types": {"version": "0.4.0", "dev": true, "license": "ISC", "engines": {"node": ">= 16", "npm": ">= 7"}, "peerDependencies": {"yaml": "^2.3.0"}}, "node_modules/yargs": {"version": "17.7.2", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yargs/node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/yargs/node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yoga-layout": {"version": "3.2.1", "dev": true, "license": "MIT"}}}