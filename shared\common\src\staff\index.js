const {
  searchStaffSchema,
  registerStaffSchema,
  editStaffSchema,
  deleteStaffSchema,
  getStaffSchema
} = require('./schema');
const { auth, apiKeyVerify, authEnterprise } = require('../base/authorized');
// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
async function searchStaffHandler(request, reply) {
  const { query, user } = request;
  query.userId = user.id;
  query.enterpriseId = user.enterprise_id;
  const staffService = request.diScope.resolve('staffService');
  const response = await staffService.searchAll(query);
  return reply.send(response);
}

async function registerStaffHandler(request, reply) {
  const { body, user } = request;
  body.userId = user.id;
  body.enterpriseId = user.enterprise_id;
  body.userRole = user.role;
  body.enterpriseType = user.enterprise_type;
  const staffService = request.diScope.resolve('staffService');
  const response = await staffService.registerNewStaff(body);
  return reply.send(response);
}

async function editStaffHandler(request, reply) {
  const { body, user, params } = request;
  body.userId = user.id;
  body.enterpriseId = user.enterprise_id;
  body.id = params.id;
  const staffService = request.diScope.resolve('staffService');
  const response = await staffService.editStaff(body);
  return reply.send(response);
}

async function deleteStaffHandler(request, reply) {
  const { user, params } = request;
  const staffService = request.diScope.resolve('staffService');
  const response = await staffService.deleteStaff({
    userId: user.id,
    enterpriseId: user.enterprise_id,
    id: params.id,
  });
  return reply.send(response);
}

async function getStaffHandler(request, reply) {
  const { user, params } = request;
  const staffService = request.diScope.resolve('staffService');
  const response = await staffService.getStaff({
    userId: user.id,
    enterpriseId: user.enterprise_id,
    id: params.id,
  });
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get('/search-all', { schema: searchStaffSchema, onRequest: [apiKeyVerify, auth, authEnterprise] }, searchStaffHandler);
  fastify.post('/register', { schema: registerStaffSchema, onRequest: [apiKeyVerify, auth, authEnterprise] }, registerStaffHandler);
  fastify.post(`/edit/:id`, { schema: editStaffSchema, onRequest: [apiKeyVerify, auth, authEnterprise] }, editStaffHandler);
  fastify.delete(`/del/:id`, { schema: deleteStaffSchema, onRequest: [apiKeyVerify, auth, authEnterprise] }, deleteStaffHandler);
  fastify.get(`/:id`, { schema: getStaffSchema, onRequest: [apiKeyVerify, auth, authEnterprise] }, getStaffHandler);
};