<template>
  <q-input
    v-model="model"
    v-bind="$attrs"
    ref="inputRef"
    class="vs tw:text-l-design tw:h-[4.4rem]"
    :input-class="[$attrs['input-class'] ?? 'tw:text-l-design', 'tw:min-h-full']"
    popup-content-class="popup-content-class-custom tw:text-l-design"
    hide-bottom-space
    no-error-icon
    @blur="handleBlur"
    @focus="rawValue = model"
  >
    <template v-for="(_, name) in $slots" v-slot:[name]="props" :key="name">
      <slot :name="name" :props="props" />
    </template>
    <template v-if="isClearable" v-slot:append>
      <img
        v-if="model"
        :src="ClearSvg"
        class="tw:cursor-pointer tw:mr-4 tw:h-[1.875rem] tw:w-[1.875rem]"
        @click="handleClear"
      />
    </template>
  </q-input>
</template>

<script setup>
import IMask from 'imask';
import { onMounted, ref } from 'vue';
import ClearSvg from 'assets/ClearSvg.svg';

defineOptions({ inheritAttrs: false });
const rawValue = ref();
const inputRef = ref();
const props = defineProps({
  mask: Object,
  isClearable: {
    type: Boolean,
    default: false,
  },
});
const handleClear = () => {
  model.value = '';
};
const model = defineModel();
const handleBlur = () => {
  if (!model.value) {
    return;
  }
  if (props.mask && Object.keys(props.mask).length) {
    model.value = rawValue.value;
  }
};
onMounted(() => {
  if (props.mask && Object.keys(props.mask).length) {
    const inputElement = inputRef.value.$el.querySelector('input');
    if (inputElement) {
      // Áp dụng IMask
      const masked = new IMask(inputElement, props.mask);
      masked.on('accept', () => {
        model.value = masked.value;
        rawValue.value = masked.value;
      });
    }
  }
});
</script>

<style scoped>
.q-field--outlined .q-field__control {
  padding-right: 0px !important;
}
</style>
