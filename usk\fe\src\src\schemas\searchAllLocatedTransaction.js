import <PERSON><PERSON><PERSON><PERSON> from 'helpers/message';
import { PARTNER_TYPE_ENUM } from 'helpers/constants';

const searchAllLocatedTransaction = {
  additionalProperties: false,
  type: 'object',
  properties: {
    pageIndex: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_INDEX_INVALID,
      },
    },
    pageSize: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_SIZE_INVALID,
      },
    },
    hiddenFlag: {
      type: 'boolean',
      errorMessage: {
        _: MESSAGE.MSG_HIDDEN_FLG_INVALID,
      },
    },
    partnerType: {
      type: 'string',
      enum: [PARTNER_TYPE_ENUM.SHIPPER, PARTNER_TYPE_ENUM.SUPPLIER],
      errorMessage: {
        _: MESSAGE.MSG_PARTNER_TYPE_INVALID,
      },
    },
    code: {
      type: 'string',
      pattern: '^\\d+$',
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    name: {
      type: 'string',
      errorMessage: {
        pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_NAME_INVALID,
      },
    },
    enterpriseName: {
      type: 'string',
      errorMessage: {
        pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_NAME_INVALID,
      },
    },
    licenseNumber: {
      type: 'string',
      errorMessage: {
        pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_NAME_INVALID,
      },
    },
  },
};

export default searchAllLocatedTransaction;
