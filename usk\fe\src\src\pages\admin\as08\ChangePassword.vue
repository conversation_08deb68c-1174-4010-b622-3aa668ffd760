<template>
  <div class="tw:w-[40%] tw:min-w-[550px] tw:m-auto">
    <!-- title -->
    <div class="tw:my-5 tw:font-bold tw:text-center tw:text-2xl tw:border-b tw:border-gray tw:pb-4">
      パスワード再登録
    </div>
    <!-- content -->
    <div class="tw:my-5 tw:text-sm">
      <div>
        パスワード再登録用のURLを記載たてSMSを送信します。登録している携帯電話番号を入力してください。SMS受信後、記載のURLからパスワードの再登録をお願いいたします。
      </div>
      <div class="tw:mt-3">
        携帯電話番号が不明な方はシステム管理者にお問い合わせください。
      </div>
    </div>
    <div>
      <div class="tw:leading-[2rem] tw:text-base">
        携帯電話番号<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-3"
        outlined
        type="tel" inputmode="numeric"
        v-model="phone"
        autocomplete="off"
        lazy-rules
        :error="!!errors.mobileNumber"
        :error-message="errors.mobileNumber" no-error-icon
        maxlength="11"
      />
      <q-btn
        class="tw:w-full tw:mt-5 tw:font-bold tw:text-white tw:leading-[2rem] tw:text-base"
        label="送信する"
        type="button"
        :class="`tw:bg-${colorSub}`"
        @click.prevent="sentSMS"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import inputMobile from 'schemas/inputMobile';
import authService from 'services/auth.service';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();
const { errors, validateData } = useValidate();

// ===== REF =====
const phone = ref('');

// ===== METHOD =====
const sentSMS = async () => {
  // Sent SMS
  const valid = validateData(inputMobile, { mobileNumber: phone.value });
  if (!valid) {
    return;
  }
  const result = await authService.sendSmsPassword({ mobileNumber: phone.value });
  if (result.code === 0) {
    // Go to the SMS sending completion screen
    await router.push({ name: 'adminCompleteSMS' });
  }
};
</script>
