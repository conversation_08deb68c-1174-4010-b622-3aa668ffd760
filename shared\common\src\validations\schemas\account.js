const { MESSAGE } = require('../../utils/message');
const { ROLES_ENUM } = require('../../helpers/enum');

const accountSchema = {
  tenant_code: {
    type: 'string',
    minLength: 1,
    maxLength: 10,
    errorMessage: {
      _: 'tenant_code is invalid',
    },
  },
  email: {
    type: 'string',
    minLength: 1,
    maxLength: 50,
    // eslint-disable-next-line no-useless-escape
    pattern: '^[^@\s]+@[^@\s]+\.[^@\s]+$',
    errorMessage: {
      _: 'email is invalid',
    },
  },
  name: {
    type: 'string',
    minLength: 1,
    maxLength: 80,
    errorMessage: {
      _: 'name is invalid',
    },
  },
  password: {
    type: 'string',
    maxLength: 256,
    minLength: 1,
    pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
    },
  },
  passwordConfirm: {
    type: 'string',
    minLength: 1,
    const: {
      $data: '1/password',
    },
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_NOTMATCH_PASSWARD_ERROR,
    },
  },
  // This PASSWORD regex will enforce these rules:
  // At least one upper case English letter, (?=.*?[A-Z])
  // At least one lower case English letter, (?=.*?[a-z])
  // At least one digit, (?=.*?[0-9])
  // At least one special character, (?=.*?[#?!@$%^&*-])
  // Minimum eight in length .{8,} (with the anchors)
  role_kbn: {
    type: 'string',
    enum: ['01', '02', '03', '99'], // 01: Tenant owner/administrator, 02: Normal tenant user, 99: Super admin/system owner
  },
  status_kbn: {
    type: 'string',
    enum: ['01', '02', '03'], // 01: Active, 02: Inactive, 03: Blocked
  },
  is_deleted: {
    type: 'boolean',
  },
  qrCode: {
    type: 'string',
  },
  userid: {
    type: 'string',
    minLength: 1,
    maxLength: 12,
    pattern: '^\\d{7}-\\d{4}$',
    errorMessage: {
      _: MESSAGE.MSG_NA_USERID_ERROR,
    },
  },
  enterpriseName: {
    type: 'string',
    minLength: 1,
    maxLength: 50,
    errorMessage: {
      _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  enterpriseCode: {
    type: 'string',
    pattern: '^\\d{7}$',
    errorMessage: {
      _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  firstName: {
    type: 'string',
    minLength: 1,
    pattern: '^[a-zA-Z0-9]+$',
    errorMessage: {
      _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  lastName: {
    type: 'string',
    minLength: 1,
    pattern: '^[a-zA-Z0-9]+$',
    errorMessage: {
      _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  enterpriseNameKana: {
    type: 'string',
    oneOf: [
      {
        maxLength: 50,
        pattern: '^[ァ-ヴー　]+$',
      },
      {
        minLength: 0,
        maxLength: 0,
      },
    ],
    errorMessage: {
      _: MESSAGE.MSG_LIMITS_KANA_ERROR
    },
  },
  role: {
    minItems: 1,
    type: 'array',
    items: {
      enum: Object.values(ROLES_ENUM),
    },
    errorMessage: {
      _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR
    },
  },
  mobileNumber: {
    type: 'string',
    oneOf: [
      {
        pattern: '^\\d{11}$',
      },
      {
        minLength: 0,
        maxLength: 0,
      },
    ],
    errorMessage: {
      _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR,
    },
  },
  licenseId: {
    type: 'integer',
    minimum: 1,
  },
  licenseCode: {
    type: 'string',
    pattern: '^[A-Za-z0-9]{16}$',
  },
  userIdLogin: {
    type: 'string',
    minLength: 1,
    maxLength: 12,
    pattern: '^\\d{7}-\\d{4}$',
    errorMessage: {
      _: MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR,
    },
  },
  passwordLogin: {
    type: 'string',
    maxLength: 256,
    errorMessage: {
      _: MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR,
    },
  },
};

module.exports = {
  accountSchema,
};