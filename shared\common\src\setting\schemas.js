const {
  SHOW_DEFAULT_SCAN_QR_ENUM,
  RECEIPT_NUMBER_VALUE_ENUM,
  REPORT_TYPE_ENUM,
  INVENTORY_CONTROL_TYPE_ENUM,
  INCLUDE_TAX_TYPE_ENUM
} = require('../helpers/enum');
const { sharedSchema } = require('../validations');
const { MESSAGE } = require('../utils/message');

const getSettingSchema = {
  summary: 'Get setting',
  description: 'Get setting',
  tags: ['Setting'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
};

const updateSettingSchema = {
  summary: 'Update setting',
  description: 'Update setting',
  tags: ['Setting'],
  security: [{ bearToken: [] }],
  response: sharedSchema.response,
  body: {
    type: 'object',
    additionalProperties: false,
    required: [
      'enable_session_timeout',
      'display_actual_received',
      'qr_scan_init',
      'inventory_control_type',
      'unit_per_gram',
      'report_type',
      'display_shipment_weight',
      'price_per_kilogram',
      'price_per_quantity',
      'include_tax_type',
      'receipt_number',
    ],
    properties: {
      enable_session_timeout: {
        type: 'boolean',
      },
      session_expirytime: {
        type: 'integer',
        minimum: 1,
        maximum: 24,
        errorMessage: {
          minimum: MESSAGE.MSG_LIMITS_AUTOLOGOUTTIME_ERROR,
          maximum: MESSAGE.MSG_LIMITS_AUTOLOGOUTTIME_ERROR,
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      display_actual_received: {
        type: 'boolean',
      },
      qr_scan_init: {
        type: 'integer',
        enum: Object.values(SHOW_DEFAULT_SCAN_QR_ENUM),
      },
      destination_id: {
        type: 'integer',
      },
      inventory_control_type: {
        type: 'integer',
        enum: Object.values(INVENTORY_CONTROL_TYPE_ENUM),
      },
      unit_per_gram: {
        type: 'number',
        exclusiveMinimum: 0,
        maximum: 999999,
        errorMessage: {
          _: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
        },
      },
      report_type: {
        type: 'integer',
        enum: Object.values(REPORT_TYPE_ENUM),
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      display_shipment_weight: {
        type: 'boolean',
      },
      price_per_kilogram: {
        type: 'array',
        maxItems: 3,
        minItems: 1,
        items: {
          type: 'number',
          exclusiveMinimum: 0,
          maximum: 99999999,
          errorMessage: {
            _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
          },
        },
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      price_per_quantity: {
        type: 'array',
        maxItems: 3,
        minItems: 1,
        items: {
          type: 'number',
          exclusiveMinimum: 0,
          maximum: 99999,
          errorMessage: {
            _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
          },
        },
        errorMessage: {
          _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        },
      },
      include_tax_type: {
        type: 'integer',
        enum: Object.values(INCLUDE_TAX_TYPE_ENUM),
      },
      receipt_number: {
        type: 'integer',
        enum: Object.values(RECEIPT_NUMBER_VALUE_ENUM),
      },
    },
    if: {
      properties: {
        enable_session_timeout: { const: true },
      },
    },
    then: {
      required: ['session_expirytime'],
    },
  },
};

module.exports = {
  getSettingSchema,
  updateSettingSchema,
};
