const {
  sharedSchema,
  accountSchema,
} = require('../validations');
const { MESSAGE } = require('../utils/message');


const loginSchema = {
  summary: 'Log in',
  description: 'Login account',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['userid', 'password'],
    properties: {
      userid: {
        ...accountSchema.userIdLogin,
      },
      password: {
        ...accountSchema.passwordLogin,
      },
    },
    errorMessage: {
      required: {
        userid: 'UserID is required',
        password: 'Password is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const adminLoginSchema = {
  summary: 'Admin login',
  description: 'Admin login',
  tags: ['Authentication'],
  security: [{ 'bearToken': [], 'apiKey': [] }],
  body: {
    type: 'object',
    required: ['userid', 'password'],
    properties: {
      userid: {
        type: 'string',
        minLength: 1,
        maxLength: 256,
        pattern: '^[a-zA-Z0-9._\\-]+$',
        errorMessage: {
          _: MESSAGE.MSG_NOTMATCH_USERIDPASS_ERROR,
        },
      },
      password: {
        ...accountSchema.passwordLogin,
      },
    },
    errorMessage: {
      required: {
        userid: 'UserID is required',
        password: 'Password is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const adminLoginProxyUserSchema = {
  summary: 'Admin login proxy user',
  description: 'Admin login proxy user',
  tags: ['Authentication'],
  security: [{ 'apiKey': [] }],
  params: {
    additionalProperties: false,
    type: 'object',
    required: ['id'],
    properties: {
      id: {
        type: 'string',
        errorMessage: {
          _: 'Invalid id value. It should be a string',
        },
      },
    },
  },
  response: sharedSchema.response,
};

const loginLinkSchema = {
  summary: 'Login with link',
  description: 'Login with link',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['qrCode'],
    properties: {
      qrCode: {
        ...accountSchema.qrCode,
      }
    },
    errorMessage: {
      required: {
        qrCode: 'qrCode is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const checkLoginSchema = {
  summary: 'Check login account status',
  description: 'Check login account status',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  response: sharedSchema.response,
};

const smsPasswordSchema = {
  summary: 'SMS Password Schema',
  description: 'SMS Password Schema',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['mobileNumber'],
    properties: {
      mobileNumber: {
        ...accountSchema.mobileNumber,
      }
    },
    errorMessage: {
      required: {
        mobileNumber: 'mobileNumber is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const resetPasswordSchema = {
  summary: 'Reset password',
  description: 'Allow users to change their password',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['password'],
    properties: {
      password: {
        ...accountSchema.password
      },
    },
    errorMessage: {
      required: {
        password: 'Password is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const recoveryPasswordSchema = {
  summary: 'Recovery password',
  description: 'Recovery password',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['password'],
    properties: {
      password: {
        ...accountSchema.password
      },
    },
    errorMessage: {
      required: {
        password: 'Password is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

const loginInsteadSchema = {
  summary: 'Login Instead',
  description: 'Login account',
  tags: ['Authentication'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['userid'],
    properties: {
      userid: {
        type: 'string',
        minLength: 1,
        maxLength: 12,
        pattern: '^\\d{7}-\\d{4}$',
        errorMessage: {
          _: MESSAGE.MSG_NA_USERID_ERROR,
        },
      }
    },
    errorMessage: {
      required: {
        userid: 'UserID is required',
      },
    },
    additionalProperties: false,
  },
  response: sharedSchema.response,
};

module.exports = {
  loginSchema,
  loginLinkSchema,
  checkLoginSchema,
  smsPasswordSchema,
  resetPasswordSchema,
  adminLoginSchema,
  recoveryPasswordSchema,
  adminLoginProxyUserSchema,
  loginInsteadSchema,
}