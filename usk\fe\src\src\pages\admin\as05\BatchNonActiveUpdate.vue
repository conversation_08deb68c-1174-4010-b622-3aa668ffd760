<template>
  <div class="tw:flex tw:justify-center">
    <div class="tw:w-[40%] tw:min-w-[550px]">
      <span class="tw:text-base tw:mt-4 tw:text-left">
        以下の内容で対象ユーザーの状態を「無許可」に更新します。
      </span>
      <!-- 事業者種別 -->
      <div class="tw:mb-1 tw:mt-4">
        <span class="tw:text-[1rem] tw:block"
          >対象事業者種別(複数選択可)<span class="tw:text-red">*</span></span
        >
        <q-field :error="!!errors.roles" no-error-icon borderless hide-bottom-space>
          <template v-slot:control>
            <div class="tw:grid-cols-4 tw:grid tw:w-full tw:gap-x-3">
              <q-checkbox
                class="tw:text-[1rem]"
                v-for="(item, index) in userRoleOptions"
                :key="index"
                v-model="form.roles"
                :label="item.label"
                :val="item.value"
                size="lg"
                color="black"
              />
            </div>
          </template>
          <template v-slot:error>
            <div class="tw:pl-3 -tw:translate-y-3">
              {{ errors.roles }}
            </div>
          </template>
        </q-field>
      </div>
      <!-- 都道府県 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          都道府県(複数選択可)<span class="tw:text-red">*</span>
        </span>
        <q-select
        outlined
        multiple
        fill-input
        emit-value
        map-options
        v-model="form.provinceId"
        :options="optionsRegions"
        :display-value="selectedProvinceLabels.join(', ')"
        class="tw:text-base tw:rounded-2xl"
        :error="!!errors.provinceId"
        :error-message="errors.provinceId"
        no-error-icon
        hide-bottom-space
      >
        <!-- Region label -->
        <template v-slot:option="scope">
          <div v-if="scope.opt.__type === 'region'" class="q-pl-sm q-pt-xs q-pb-xs text-weight-bold text-black">
            {{ scope.opt.label }}
          </div>
          <q-item
            v-else
            v-bind="scope.itemProps"
          >
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <q-tooltip>
          {{ selectedProvinceLabels.join(', ') }}
        </q-tooltip>
      </q-select>
      </div>
      <div class="tw:flex tw:justify-center tw:gap-3 tw:mt-4">
      <BaseButton
        outline
        padding="0.75rem"
        :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
        label="戻る"
        @click.prevent="handleClickCancel"
      />
      <BaseButton
        padding="0.75rem"
        :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
        label="更新する"
        @click.prevent="handleClickUpdate"
      />
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import useValidate from 'src/composables/validate';
import regionsService from 'src/shared/services/regions.service';
import { ENTERPRISE_TYPE_OPTIONS } from 'src/helpers/constants';
import BaseButton from 'src/components/base/BaseButton.vue';
import { useAppStore } from 'src/stores/app-store';
import { useRouter } from 'vue-router';
import batchNonActiveUpdateSchema from 'src/schemas/admin/user/batchNonActiveUpdate';
import { storeToRefs } from 'pinia';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();

const { errors, validateData } = useValidate();
const userRoleOptions = ENTERPRISE_TYPE_OPTIONS;

const form = ref({
  roles: [],
  provinceId: [], // multiple IDs
});

const optionsRegions = ref([]); // region + province options

const selectedProvinceLabels = computed(() =>
  form.value.provinceId
    .map(id => {
      const found = optionsRegions.value.find(
        o => o.__type !== 'region' && o.value === id
      );
      return found?.label || '';
    })
    .filter(Boolean));

const handleClickCancel = () => {
  router.back();
};

const handleClickUpdate = async() => {
  const validate = validateData(batchNonActiveUpdateSchema, form.value);

  if (validate) {
    const payload = {
      roles: form.value.roles,
      provinceIds: form.value.provinceId,
    };
    const result = await userAdminService.bulkUpdateUserStatus(payload);
    if (result.code === 0) {
      toast.access(MESSAGE.MSG_UPDATE_USER_STATUS_INFO);
      router.push('adminUserManager');
    }
  }
};

onMounted(async () => {
  const res = await regionsService.getRegionWithProvinces();
  const flat = [];

  res.payload.items.forEach(region => {
    flat.push({
      label: region.region_name,
      __type: 'region', // special type for display
    });

    region.province.forEach(p => {
      flat.push({
        label: p.name,
        value: p.id,
      });
    });
  });

  optionsRegions.value = flat;
});
</script>
