<template>
  <q-page class="tw:flex tw:items-start tw:pt-4 tw:pb-[5rem] tw:xl:pb-0">
    <q-card
      flat
      class="tw:bg-white tw:rounded-md tw:w-full tw:tl-mb-[10rem] tw:mb-[8rem] tw:min-h-[calc(100vh-24rem)]
      tw:tl:min-h-[calc(100vh-20rem)] tw:dt:min-h-[calc(100vh-18rem)]"
    >
      <div
        class="tw:w-full tw:text-[#333333] tw:bg-white tw:border-0 tw:border-inherit tw:pl-3 tw:pr-3 tw:rounded-md"
      >
        <div class="tw:pb-4 tw:pt-4 tw:border-blue-400 tw:rounded-md">
          <!-- Title -->
          <div class="tw:text-l-design tw:font-bold">
            {{ notificationDetail?.title }}
          </div>

          <!-- Date -->
          <div class="tw:text-s-design tw:mt-2 tw:font-normal">
            {{ notificationDetail?.dateSend }}
          </div>

          <!-- Content -->
          <div class="tw:text-m-design tw:break-words tw:font-normal
          tw:mt-4" v-html="notificationDetail?.content"></div>
        </div>
      </div>
    </q-card>

    <!-- Footer Button -->
    <q-footer elevated class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row">
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[23.45rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="お知らせ一覧に戻る"
        @click.prevent="goBack"
      />
    </q-footer>
  </q-page>
</template>

<script setup>
import { FORMAT_DATE, linkify, clearHTML } from 'helpers/common';
import notificationService from 'src/shared/services/notification.service';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from 'stores/app-store';
import BaseButton from 'src/components/base/vs/BaseButton.vue';

// #region state
const router = useRouter();
const notificationDetail = ref(null);
const { setHasUnreadNotificationFlag } = useAppStore();
// #endregion

//  #region actions
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  const notificationId = router.currentRoute.value?.params?.id;
  if (!notificationId) {
    await router.push({ name: 'notificationList' });
  }
  const notificationDetailResponse =
    await notificationService.getNotificationDetail(notificationId);
  notificationDetail.value = {
    ...notificationDetailResponse.payload,
    content: clearHTML(linkify(notificationDetailResponse.payload.content)),
    dateSend: FORMAT_DATE(notificationDetailResponse.payload.date_send),
    isRead: notificationDetailResponse.payload.is_read,
  };

  if (!notificationDetail.value.isRead) {
    const checkHasUnreadNotification =
      await notificationService.checkHasUnreadNotification();
    setHasUnreadNotificationFlag(checkHasUnreadNotification.payload.has_unread);
  }
});

const goBack = () => {
  router.back();
};
// #endregion
</script>

<style scoped>
:deep(a) {
  color: #007bff !important;
}
</style>
