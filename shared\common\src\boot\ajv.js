const Ajv = require('ajv');
const AjvErrors = require('ajv-errors');
const dayjs = require('../boot/dayjs');

const ajv = new Ajv({
  allErrors: true,
  removeAdditional: true,
  useDefaults: true,
  coerceTypes: true,
  $data: true,
});

ajv.addFormat('slash-date', {
  type: 'string',
  validate: (data) => dayjs(data, 'YYYY/MM/DD', true).isValid() || !data,
  compare: (data1, data2) => {
    return new Date(data1).getTime() - new Date(data2).getTime();
  },
});
// enhance the ajv instance
AjvErrors(ajv);

require('ajv-keywords')(ajv);
require('ajv-formats')(ajv);

module.exports = ajv;