<template>
  <div class="tw:hidden">
    <div id="shipment-pdf-file-data" class="">
      <h1 class="tw:text-[50px] tw:font-bold tw:text-center">
        シラスウナギ出荷情報
      </h1>
      <p class="tw:text-right tw:text-[28px] tw:py-[32px] tw:px-2 tw:font-bold">
        {{ `出荷日: ${provideData.shipping_date}` }}
      </p>
      <div class="tw:border">
        <div class="row-item tw:border-b">
          <span class="label">漁獲/荷口番号</span>
          <span
            class="value"
            >{{ maskCodeString(provideData.code) }}</span
          >
        </div>
        <div class="row-item tw:border-b">
          <span class="label">品目</span>
          <span class="value"
            >シラスウナギ</span
          >
        </div>
        <div class="row-item tw:border-b">
          <span class="label">事業者名</span>
          <div
            class="tw:max-w-[490px] dynamic-font-size"
            id="starting-enterprise"
          >
          </div>
        </div>
        <div class="tw:flex">
          <div class="tw:flex-1">
            <div
              :class="`row-item ${
                !!provideData.weight ? 'tw:border-b' : ''
              }`"
            >
              <span class="label"
                >出荷先(届出事業者)</span
              >
              <div
                class="tw:max-w-[490px] dynamic-font-size"
                id="destination-enterprise"
              >
              </div>
            </div>
            <div v-show="!!provideData.weight" class="row-item">
              <span class="label">出荷量</span>
              <span class="value"
                >{{ FORMAT_NUMBER(provideData.weight) }}g</span
              >
            </div>
          </div>
          <div class="tw:border" v-if="qrCode.value">
            <img
              alt=""
              id="shipment-qr-code"
              :src="qrCode.value"
              spinner-color="white"
              class="tw:h-[180px] tw:w-[180px] tw:aspect-square tw:max-w-none"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, watch, onMounted, nextTick } from 'vue';
import { useQRCode } from '@vueuse/integrations/useQRCode';
import { maskCodeString, FORMAT_NUMBER } from 'helpers/common';

const provideData = inject('outBoundShipmentPDFProvideData');
const qrCode = computed(() => (provideData.value.qr_code ? useQRCode(`${provideData.value.qr_code}`) : ''));

// Function to format enterprise text based on length rules
const formatEnterpriseText = text => {
  if (!text) {return '';}

  const length = text.length;

  if (length <= 15) {
    // 15 characters or less: 1 line, normal font size
    return text;
  } else if (length <= 30) {
    // 16–30 characters: 2 lines, normal font size
    const line1 = text.substring(0, 15);
    const line2 = text.substring(15, 30);
    return `${line1}<br/>${line2}`;
  } else if (length <= 60) {
    // 31–60 characters: 2 lines, smaller font size (half)
    const line1 = text.substring(0, 30);
    const line2 = text.substring(30, 60);
    return `${line1}<br/>${line2}`;
  } else if (length <= 90) {
    // 61–90 characters: 3 lines, smaller font size (half)
    const line1 = text.substring(0, 30);
    const line2 = text.substring(30, 60);
    const line3 = text.substring(60, 90);
    return `${line1}<br/>${line2}<br/>${line3}`;
  } else if (length <= 102) {
    // 91–102 characters: 4 lines, smaller font size (half)
    const line1 = text.substring(0, 30);
    const line2 = text.substring(30, 60);
    const line3 = text.substring(60, 90);
    const line4 = text.substring(90, 102);
    return `${line1}<br/>${line2}<br/>${line3}<br/>${line4}`;
  }

  // Case where text exceeds 102 characters (should not happen by requirement)
  return text.substring(0, 102);
};

const adjustFontSize = () => {
  // Handle starting_enterprise
  const startingElement = document.getElementById('starting-enterprise');
  if (startingElement && provideData.value.starting_enterprise) {
    const startingLength = provideData.value.starting_enterprise.length;
    const formattedText = formatEnterpriseText(provideData.value.starting_enterprise);
    startingElement.innerHTML = formattedText;

    if (startingLength <= 30) {
      // Normal font size (32px)
      startingElement.style.fontSize = '32px';
      startingElement.style.lineHeight = '40px';
      startingElement.style.transform = 'translateY(-14px)';
    } else {
      // Smaller font size (16px, half of 32px)
      startingElement.style.fontSize = '16px';
      startingElement.style.lineHeight = '20px';
      startingElement.style.transform = 'translateY(-8px)';
    }
    startingElement.style.textAlign = 'center';
    startingElement.style.justifyContent = 'center';
  }

  // Handle destination_enterprise
  const destinationElement = document.getElementById('destination-enterprise');
  if (destinationElement && provideData.value.destination_enterprise) {
    const destinationLength = provideData.value.destination_enterprise.length;
    const formattedText = formatEnterpriseText(provideData.value.destination_enterprise);
    destinationElement.innerHTML = formattedText;

    if (destinationLength <= 30) {
      // Normal font size (32px)
      destinationElement.style.fontSize = '32px';
      destinationElement.style.lineHeight = '40px';
      destinationElement.style.transform = 'translateY(-14px)';
    } else {
      // Smaller font size (16px, half of 32px)
      destinationElement.style.fontSize = '16px';
      destinationElement.style.lineHeight = '20px';
      destinationElement.style.transform = 'translateY(-8px)';
    }
    destinationElement.style.textAlign = 'center';
    destinationElement.style.justifyContent = 'center';
  }
};

watch(
  () => [provideData.value.starting_enterprise, provideData.value.destination_enterprise],
  () => {
    nextTick(() => {
      adjustFontSize();
    });
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    adjustFontSize();
  });
});

</script>

<style scoped>
.row-item {
  display: flex;
  min-height: 90px;
}

.label {
  font-size: 20px;
  padding: 0 10px;
  width: 200px !important;
}

.value {
  display: flex;
  align-items: center;
  font-size: 32px;
  line-height: 40px;
  padding: 0;
  margin: 0;
  transform: translateY(-14px);
}

.dynamic-font-size {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0;
  margin: 0;
  transform: translateY(-14px);
  word-wrap: break-word;
  white-space: normal;
}
</style>
