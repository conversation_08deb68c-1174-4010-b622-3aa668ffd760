'use strict';

const path = require('path');
const AutoLoad = require('@fastify/autoload');
const { fastifyAwilixPlugin } = require('@fastify/awilix');
const { diContainer } = require('@fastify/awilix');
const { asClass, asValue, Lifetime } = require('awilix');
const { Prisma } = require('@prisma/client');
const ajv = require('./boot/ajv');
const Env = require('./boot/env');
const { BadRequestException, ApplicationException } = require('./base/errors');
const DB = require('./helpers/db');
const { error: responseError } = require('./helpers/response');
const { sprintf } = require('sprintf-js');

const PREFIX = '/api/%1$s/:serviceId';

const setPrefix = (subPrefix, defaultVersion = 'v1') => {
  return sprintf(PREFIX, defaultVersion).concat(`/${subPrefix}`);
}

const ArrivalService = require('./arrival/service');
const OutboundService = require('./outbound/service');
const ShippingService = require('./shipping/services');
const InventoryManagementService = require('./inventory-management/service');
const FAService = require('./fa/services');
const { APP_LOGGER } = require('./boot/logger');
const { MESSAGE } = require('./utils/message');
const BaseService = require('./base/serviceFn');

async function DI(request) {
  request.diScope.register({
    currentUser: asValue(request.user),
    arrivalService: asClass(ArrivalService),
    shippingService: asClass(ShippingService),
    outboundService: asClass(OutboundService),
    inventoryManagementService: asClass(InventoryManagementService),
    fAService: asClass(FAService),
    baseService: asClass(BaseService),
  });
}

module.exports = async function (fastify, opts) {
  diContainer.register({
    DB: asClass(DB, {
      lifetime: Lifetime.SINGLETON,
      dispose: (module) => {
        module.READ.$disconnect();
        module.WRITE.$disconnect();
      },
    }),
    FASTIFY: asValue(fastify),
    DBR_CONNECTION_STRING: asValue(Env.R_DATABASE_URL),
    DBW_CONNECTION_STRING: asValue(Env.W_DATABASE_URL),
  });

  fastify.get('/healthcheck', function (request, reply) {
    reply.send({ status: 'Server running' })
  });
  // Place here your custom code!
  fastify
    .register(require('@fastify/cors'), {
      origin: true,
      methods: 'GET, POST, OPTIONS, PATCH, PUT, DELETE, HEAD',
      allowedHeaders: 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With,X-Api-Key,X-Access-Token',
      exposedHeaders: 'X-Access-Token',
    })
    .register(require('@fastify/jwt'), {
      secret: Env.JWT_SECRET,
      algorithms: ['HS512'],
      sign: {
        algorithm: 'HS512',
        expiresIn: Env.JWT_EXPIRES_IN,
      },
      verify: {
        maxAge: Env.JWT_EXPIRES_IN,
      },
    })
    .register(require('@fastify/view'), {
      engine: {
        handlebars: require('handlebars'),
      },
      root: path.join(__dirname, 'templates'), // Points to `./views` relative to the current file
      viewExt: 'handlebars', // Sets the default extension to `.handlebars`
      // propertyName: "render", // The template can now be rendered via `reply.render()` and `fastify.render()`
      defaultContext: {
        environment: Env.NODE_ENV === 'production' ? '' : Env.NODE_ENV, // Inside your templates, `dev` will be `true` if the expression evaluates to true
      },
      options: {}, // No options passed to handlebars
    })
    .register(fastifyAwilixPlugin, {
      disposeOnClose: true,
      disposeOnResponse: true,
    })

    // Do not touch the following lines

    // This loads all plugins defined in plugins
    // those should be support plugins that are reused
    // through your application
    .register(AutoLoad, {
      dir: path.join(__dirname, 'plugins'),
      options: Object.assign({}, opts),
    })

    // This loads all plugins defined in routes
    // define your routes in one of these
    // .register(AutoLoad, {
    //   dir: path.join(__dirname, 'routes'),
    //   options: Object.assign({}, opts)
    // })
    .register(require('./arrival'), { prefix: setPrefix('arrival') })
    .register(require('./outbound'), { prefix: setPrefix('outbound') })
    .register(require('./shipping'), { prefix: setPrefix('shipping') })
    .register(require('./inventory-management'), { prefix: setPrefix('inventory-management') })
    .register(require('./fa'), { prefix: setPrefix('fa') })
    .setValidatorCompiler(({
      schema,
    }) => ajv.compile(schema))
    .setSchemaErrorFormatter((errors, dataVar) => {
      let text = '';
      const payload = [];
      errors.forEach((error) => {
        if (!text) {
          text = error.message;
        }
        payload.push({
          instancePath: error.instancePath || dataVar,
          message: error.message,
        });
      });
      return new BadRequestException(text, payload);
    })
    .setErrorHandler(async (error, request, reply) => {
      let response;
      if (error instanceof ApplicationException) {
        response = responseError(error.message, error.payload, error.code);
        APP_LOGGER.business(error.message, { className: 'app.js' }, request);
      } else if (error.code
        && (error instanceof Prisma.PrismaClientKnownRequestError ||
          error instanceof Prisma.PrismaClientUnknownRequestError)) {
        await new Promise((resolve) => setTimeout(resolve, 800));
        response = responseError(MESSAGE.ERROR_MESSAGE_500, null, 504);
        APP_LOGGER.business(error.message, { className: 'app.js' }, request);
      } else {
        response = responseError(MESSAGE.ERROR_MESSAGE_500, null, 500);
        APP_LOGGER.exception(error.stack, { className: 'app.js' }, request);
      }
      reply.send(response);
    })
    .addHook('onRequest', async (request) => {
      DI(request, {});
    })
    .addHook('preValidation', async (request) => {
      const userAgent = request.headers['user-agent'];
      if (!Env.USER_AGENT_NOT_LOG.split(',').includes(userAgent)
        && ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
        APP_LOGGER.request(request);
      }
    })
    .addHook('onSend', async (request, reply, payload) => {
      const userAgent = request.headers['user-agent'];
      if (!Env.USER_AGENT_NOT_LOG.split(',').includes(userAgent)
        && ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
        APP_LOGGER.response(request, reply, payload);
      }
      return payload;
    })
    // when all plugins ready
    .ready((error) => {
      if (error) {
        APP_LOGGER.error(error);
      }
      APP_LOGGER.info('All Fastify plugins ready');
    });
};

// Pass --options via CLI arguments in command to enable these options.
module.exports.options = {};
