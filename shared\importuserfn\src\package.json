{"name": "unagi-importuser-fn", "version": "0.0.0", "description": "Unagi import user lambda function", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "Saishunkan System", "license": "ISC", "dependencies": {"@aws-sdk/client-dynamodb": "^3.830.0", "@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/lib-dynamodb": "^3.830.0", "dayjs": "^1.11.13", "encoding-japanese": "^2.2.0", "knex": "^3.1.0", "lodash": "^4.17.21", "papaparse": "^5.5.3", "pg": "^8.16.1", "short-unique-id": "^5.3.2", "sprintf-js": "^1.1.3"}, "devDependencies": {"@eslint/js": "^9.32.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "eslint": "^9.32.0", "globals": "^16.3.0"}}