import BaseRepository from '../base.repository';

class AdminUserRepository extends BaseRepository {
  constructor() {
    super('admin/user');
  }

  getUserList(data) {
    return this.client.get('/get', data);
  }

  getUserDetail(userId) {
    return this.client.get(`/user-detail/${userId}`);
  }

  registerUser(body) {
    return this.client.post('/register-user', body);
  }

  editUser(id, body) {
    return this.client.post(`/edit-user/${id}`, body);
  }

  presignImportUser(body) {
    return this.client.post('/presign-import-user', body);
  }

  getImportUser() {
    return this.client.get('/get-import-user');
  }

  downloadImportUser(body) {
    return this.client.post('/download-import-user', body);
  }

  bulkUpdateUserStatus(body) {
    return this.client.post('/bulk-update-user-status', body);
  }
}

export default new AdminUserRepository();
