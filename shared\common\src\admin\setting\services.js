const dayjs = require('dayjs');
const BaseService = require('../../base/serviceFn');
const { MESSAGE } = require('../../utils/message');

class AdminSettingService extends BaseService {
  // ====== 1. Private method ==========
  async #checkSettingNameExist(settingNames) {
    const connect = this.DB.READ;
    const settingNamesArray = await connect.sys_settings.findMany({
      select: {
        setting_name: true,
      },
    });

    return settingNames.every((settingName) =>
      settingNamesArray.some((item) => item.setting_name === settingName)
    );
  }

  // ====== 2. Public method ==========
  async getAdminSettings() {
    const connect = this.DB.READ;
    const data = await connect.sys_settings.findMany({
      select: {
        setting_name: true,
        setting_value: true,
      },
    });

    const result = data.reduce((acc, item) => {
      acc[item.setting_name] = item.setting_value;
      return acc;
    }, {});

    return this.SUCCESS(result);
  }

  async getSystemSettingsForNormalUser() {
    const connect = this.DB.READ;
    const data = await connect.sys_settings.findMany({
      select: {
        setting_name: true,
        setting_value: true,
      },
    });

    const result = data.reduce((acc, item) => {
      acc[item.setting_name] = item.setting_value;
      return acc;
    }, {});

    return this.SUCCESS(result);
  }

  async updateSystemSettings(user, data) {
    const dataFormat = Object.keys(data).map((key) => ({
      setting_name: key,
      setting_value: data[key],
    }));
    const connect = this.DB.WRITE;

    const checkSettingNameExist = await this.#checkSettingNameExist(
      Object.keys(data)
    );
    if (!checkSettingNameExist) {
      return this.ERROR(MESSAGE.MSG_NO_DATA_INFO);
    }

    await connect.$transaction(async (tx) => {
      await Promise.all(
        dataFormat.map((item) => {
          return tx.sys_settings.update({
            where: {
              setting_name: item.setting_name,
            },
            data: {
              setting_value: item.setting_value,
              latest_updated_by_id: user.id,
              latest_updated_on: dayjs().toDate(),
            },
          });
        })
      );
    });

    const newData = await connect.sys_settings.findMany({
      select: {
        setting_name: true,
        setting_value: true,
      },
    });
    const result = newData.reduce((acc, item) => {
      acc[item.setting_name] = item.setting_value;
      return acc;
    }, {});

    return this.SUCCESS(result);
  }
}

module.exports = AdminSettingService;
