<template>
  <q-dialog v-model="provideData.isPopup.value" persistent>
    <q-card class="tw:min-w-[22rem] tw:max-w-[36rem] tw:tl:min-w-[40rem] tw:p-2">
      <div class="tw:flex tw:flex-col tw:m-4">
        <span class="tw:tl:text-[2.25rem] tw:tl:leading-[2.25rem] tw:mb-4 tw:border-b
        tw:pb-1 tw:font-[700] tw:text-[1.125rem] tw:leading-[1.5rem] tw:border-gray">
          {{ provideData.titlePopup }}</span>
        <div class="tw:text-[1rem] tw:leading-[1.5rem] tw:font-[400]
        tw:tl:text-[2rem] tw:tl:leading-[2rem]">
          {{ provideData.caption.value ?? provideData.caption }}
        </div>
        <div class="tw:flex tw:justify-end tw:mt-6">
          <q-btn
              class="tw:tl:w-32 tw:w-24 tw:flex tw:justify-center tw:items-center tw:mr-4
              tw:font-[900]
              tw:tl:text-[1.5rem] tw:text-[1rem]"
              :class="`tw:text-${colorMain}`"
              outline
              label="いいえ"
              @click.prevent="closeModal"
            />
            <q-btn
              type="button"
              class="tw:w-24 tw:flex tw:justify-center tw:items-center tw:text-white tw:font-[900]
              tw:tl:w-32
              tw:tl:text-[1.5rem] tw:text-[1rem]"
              :class="`tw:bg-${colorSub}`"
              @click.prevent="acceptModal"
              label="はい"
            />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { inject } from 'vue';

const { colorMain, colorSub } = storeToRefs(useAppStore());
const provideData = inject('popupConfirmTextProvideData');

const closeModal = () => {
  provideData.handleCloseModal();
};

const acceptModal = () => {
  provideData.handleAcceptModal();
};

</script>
