class ApplicationException extends Error {
  constructor(message) {
    super(message);
    this.name = 'ApplicationException';
    this.code = 999;

    Error.captureStackTrace(this, ApplicationException);
  }
}

class BadRequestException extends ApplicationException {
  constructor(message, payload) {
    super(message);
    this.name = 'BadRequestException';
    this.code = 400;
    this.payload = payload;
    Error.captureStackTrace(this, BadRequestException);
  }
}

class UnauthorizedAccessException extends ApplicationException {
  constructor(message = 'アクセス拒否') {
    super(message);
    this.name = 'UnauthorizedAccessException';
    this.code = 401;
    Error.captureStackTrace(this, UnauthorizedAccessException);
  }
}

class InvalidPermissionException extends ApplicationException {
  constructor(message = '全く許可しません') {
    super(message);
    this.name = 'InvalidPermissionException';
    this.code = 402;
    Error.captureStackTrace(this, InvalidPermissionException);
  }
}

class ControlledException extends ApplicationException {
  constructor(message, payload, code = 403) {
    super(message);
    this.name = 'ExceptionControlled';
    this.code = code;
    this.payload = payload;
    Error.captureStackTrace(this, ControlledException);
  }
}

module.exports = {
  ApplicationException,
  BadRequestException,
  UnauthorizedAccessException,
  InvalidPermissionException,
  ControlledException,
};