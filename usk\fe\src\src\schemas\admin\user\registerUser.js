import MESSAGE from 'src/helpers/message';
const registerUserSchema = {
  additionalProperties: false,
  type: 'object',
  properties: {
    provinceId: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterpriseType: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterpriseCode: {
      type: 'string',
          maxLength: 7,
          errorMessage: {
            _: MESSAGE.MSG_LIMITS_ENTERPRISENUMBER_ERROR,
          },
    },
    typeStaffOrEnterprise: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterpriseName: {
      type: 'string',
      minLength: 1,
      maxLength: 50,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        maxLength: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterpriseNameKana: {
      type: 'string',
      oneOf: [
        {
          minLength: 0,
          maxLength: 0,
        },
        {
          maxLength: 50,
          pattern: '^[ァ-ヴー　]+$',
        },
      ],
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_KANA_ERROR,
      },
    },
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    phone: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
      },
    },
    startExpiryDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endExpiryDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endExpiryDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
    status: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    password: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
      pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.NOTE1_ERROR,
      },
    },
    note2: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.NOTE2_ERROR,
      },
    },
  },
};

export default registerUserSchema;
