<template>
  <PopupConfirmTextAS />
  <div class="tw:mx-auto tw:max-w-[600px] tw:mt-3">
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">件名</span>
      <div :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`">
        {{ notificationDetail?.title }}
      </div>
    </div>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">作成日時</span>
      <div :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`">
        {{
          notificationDetail?.date_send
            ? FORMAT_DATE(notificationDetail.date_send, 'YYYY/MM/DD HH:mm:ss')
            : ''
        }}
      </div>
    </div>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">ユーザーID</span>
      <div :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`">
        {{ notificationDetail?.creator?.user_code }}
      </div>
    </div>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">本文</span>
      <div
        :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded tw:break-words`"
        v-html="clearHTML(linkify(notificationDetail?.content || ''))"
      ></div>
    </div>
    <div class="tw:mb-2">
      <span class="tw:text-[1rem] tw:block tw:mb-1">送付対象ロール</span>
      <div :class="`tw:text-[1rem] tw:bg-qcard tw:px-5 tw:py-4 tw:rounded`">
        <p
          v-for="(item, index) in notificationDetail?.role_notify"
          :key="index"
        >
          {{ userRoleOptions.find((option) => option.value === item)?.label }}
        </p>
      </div>
    </div>
    <div class="tw:flex tw:justify-center tw:gap-3 tw:mt-4">
      <BaseButton
        outline
        padding="0.75rem"
        :class="`tw:bg-white tw:text-${colorSub} tw:w-44 tw:text-[1rem]`"
        label="戻る"
        @click.prevent="handleClickCancel"
      />
      <BaseButton
        padding="0.75rem"
        :class="`tw:bg-red tw:text-[white] tw:w-44 tw:text-[1rem]`"
        label="削除する"
        @click.prevent="handleClickRemove"
      />
    </div>
  </div>
</template>

<script setup>
import BaseButton from 'components/base/BaseButton.vue';
import { storeToRefs } from 'pinia';
import { USER_ROLE_OPTIONS } from 'src/helpers/constants';
import { useAppStore } from 'src/stores/app-store';
import { useRouter } from 'vue-router';
import { onMounted, provide, ref } from 'vue';
import { clearHTML, linkify, FORMAT_DATE } from 'src/helpers/common';
import notificationService from 'src/shared/services/notification.service';
import PopupConfirmTextAS from 'src/components/PopupConfirmTextAS.vue';
import MESSAGE from 'src/helpers/message';
import toast from 'src/shared/utilities/toast';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();
const isPopupConformTextPopup = ref(false);
const userRoleOptions = USER_ROLE_OPTIONS;
const notificationDetail = ref(null);

const handleClickCancel = () => {
  router.back();
};

const popupConfirmTextProvideData = {
  isPopup: isPopupConformTextPopup,
  titlePopup: '確認',
  caption: 'お知らせを削除します。よろしいですか？',
  handleCloseModal: () => {
    isPopupConformTextPopup.value = false;
  },
  handleAcceptModal: async () => {
    const result = await notificationService.adminDeleteNotification(
      notificationDetail.value?.id
    );
    if (result.code === 0) {
      isPopupConformTextPopup.value = false;
      toast.access(MESSAGE.MSG_CANCEL_NOTICE_INFO);
      await router.push({ name: 'adminNotificationManager' });
    }
  },
};

const handleClickRemove = async () => {
  isPopupConformTextPopup.value = true;
};

onMounted(async () => {
  const notificationId = router.currentRoute.value.params.id;
  // call api to get notification detail

  const notificationDetailResponse = await notificationService.adminGetNotificationDetail(notificationId);

  notificationDetail.value = notificationDetailResponse.payload;
});

// #region provide
// for confirm info shipment popup
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
// #endregion
</script>

<style scoped>
:deep(a) {
  color: #007bff !important;
}
</style>
