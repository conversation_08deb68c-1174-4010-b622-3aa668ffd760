import Ajv from 'ajv';
import ajvErrors from 'ajv-errors';
import ajvKeywords from 'ajv-keywords';
import formatsPlugin from 'ajv-formats';
import dayjs from 'src/boot/dayjs';
import MESSAGE from 'src/helpers/message';
import _ from 'lodash';

const $ajv = new Ajv({
  allErrors: true,
  removeAdditional: true,
  useDefaults: true,
  coerceTypes: true,
  $data: true,
});
// enhance the ajv instance
ajvErrors($ajv);
ajvKeywords($ajv);
formatsPlugin($ajv);

// add custom formats
$ajv.addFormat('slash-date', {
  type: 'string',
  validate: data => dayjs(data, 'YYYY/MM/DD', true).isValid() || !data,
  compare: (data1, data2) =>
    new Date(data1).getTime() - new Date(data2).getTime(),
});

// add keyWord
$ajv.addKeyword({
  keyword: 'checkFormatDecimal',
  type: 'number',
  validate: function validate(schema, data) {
    if (!data) {
      return true;
    }
    const regex = /^-?\d{1,14}(\.\d{1,2})?$/;
    const checkFormatDecimal = regex.test(data);
    if (checkFormatDecimal) {
      return true;
    }
    this.errors = [
      {
        keyword: 'checkFormatDecimal',
        params: { keyword: 'checkFormatDecimal' },
      },
    ];
    return false;
  },
  errors: true,
});

// check import user
$ajv.addKeyword({
  keyword: 'checkImportUser',
  metaSchema: {
    minItems: 1,
    maxItems: 5000,
    items: [
      {
        type: 'object',
        properties: {
          urlcardid: {
            type: 'string',
          },
          user_code: {
            type: 'string',
          },
          init_password: {
            type: 'string',
          },
          enterprise_code: {
            type: 'string',
          },
          license_number: {
            type: 'string',
          },
          name: {
            type: 'string',
          },
          name_kana: {
            type: 'string',
          },
          province_name: {
            type: 'string',
          },
          phone: {
            type: 'string',
          },
          expiry_date: {
            type: 'string',
          },
          expiry_date_start: {
            type: 'string',
          },
          note_1: {
            type: 'string',
          },
          note_2: {
            type: 'string',
          },
          enterprise_type: {
            type: 'string',
          },
          staff_type: {
            type: 'string',
          },
          account_registration_status: {
            type: 'string',
          },
          province_id: {
            type: 'string',
          },
          status: {
            type: 'string',
          },
        },
      },
    ],
  },
  validate: function validate(schema, data) {
    if (data?.length === 0) {
      validate.errors = [
        {
          message: [
            {
              line: '',
              key: '',
              message: MESSAGE.MSG_NO_DATA_INFO,
            },
          ],
        },
      ];
      return false;
    }
    if (data?.length > 5000) {
      validate.errors = [
        {
          message: [
            {
              line: '',
              key: '',
              message: MESSAGE.MSG_LIMITS_ROWS_ERROR,
            },
          ],
        },
      ];
      return false;
    }
    const listError = [];
    const valueFormat = {
      line: 0,
      field: '',
      key: '',
      message: '',
    };
    const listUserCodeDuplicate = [];
    const listEnterpriseCodeDuplicate = [];
    data.forEach((item, i) => {
      const isCatch = ['0'].includes(item.enterprise_type);
      const isDistribute = ['5'].includes(item.enterprise_type);
      const isEel = ['9'].includes(item.enterprise_type);
      const isStaff = ['1'].includes(item.staff_type);
      const isNewUser = !item.user_code;

      // user_code
      if (!/^\d{7}-\d{4}$/.test(item.user_code) && item.user_code !== '') {
        valueFormat.line = i + 1;
        valueFormat.field = 'user_code';
        valueFormat.key = 'トレサビシステムユーザーID';
        valueFormat.message = MESSAGE.SG_INVARID_USERIDFORMAT_ERROR;
        listError.push({ ...valueFormat });
      }
      // enterprise_code
      if (isCatch || isDistribute || (isEel && item.enterprise_code)) {
        if (item.enterprise_code?.startsWith("'")) {
          item.enterprise_code = item.enterprise_code.slice(1);
        }

        if (/^\d{1,6}$/.test(item.enterprise_code)) {
          item.enterprise_code = item.enterprise_code.padStart(7, '0');
        }

        if (!item.enterprise_code && !isEel && !item.user_code) {
          valueFormat.line = i + 1;
          valueFormat.field = 'enterprise_code';
          valueFormat.key = '届出番号';
          valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
          listError.push({ ...valueFormat });
        } else if (!/^\d{7}$/.test(item.enterprise_code) && item.enterprise_code
        ) {
          valueFormat.line = i + 1;
          valueFormat.field = 'enterprise_code';
          valueFormat.key = '届出番号';
          valueFormat.message = MESSAGE.MSG_LIMITS_NOTIFICATIONUMBER_ERROR;
          listError.push({ ...valueFormat });
        } else if (isCatch && item.enterprise_code[0] !== '0' && item.enterprise_code
        ) {
          valueFormat.line = i + 1;
          valueFormat.field = 'enterprise_code';
          valueFormat.key = '届出番号';
          valueFormat.message =
            MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_SAIHO_ERROR;
          listError.push({ ...valueFormat });
        } else if (isDistribute && item.enterprise_code[0] !== '5' && item.enterprise_code
        ) {
          valueFormat.line = i + 1;
          valueFormat.field = 'enterprise_code';
          valueFormat.key = '届出番号';
          valueFormat.message =
            MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_DISTRIBUTOR_ERROR;
          listError.push({ ...valueFormat });
        } else if (listEnterpriseCodeDuplicate.includes(item.enterprise_code)
          && item.enterprise_code && !isEel && !isStaff
          && (!item.user_code || item.user_code.endsWith('-0000'))) {
            valueFormat.line = i + 1;
            valueFormat.field = 'enterprise_code';
            valueFormat.key = '届出番号';
            valueFormat.message = MESSAGE.MSG_NOTIFICATIONUMBER_DUPLICATE_IN_CSV;
            listError.push({ ...valueFormat });
        } else {
          listEnterpriseCodeDuplicate.push(item.enterprise_code);
        }
      }
      // license_number
      if (isCatch || isEel) {
        if (!item.license_number) {
          valueFormat.line = i + 1;
          valueFormat.field = 'license_number';
          valueFormat.key = '許可番号';
          valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
          listError.push({ ...valueFormat });
        } else if (
          item.license_number?.length > 256 ||
          (item.license_number?.length > 0 && item.enterprise_type === '5')
        ) {
          valueFormat.line = i + 1;
          valueFormat.field = 'license_number';
          valueFormat.key = '許可番号';

          if (item.license_number?.length > 0 && item.enterprise_type === '5') {
            valueFormat.message = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
          } else {
            valueFormat.message = MESSAGE.MSG_SAFE_DIGITS_ERROR;
          }
          listError.push({ ...valueFormat });
        }
      }
      if (isDistribute) {
        if (item.license_number) {
          valueFormat.line = i + 1;
          valueFormat.field = 'license_number';
          valueFormat.key = '許可番号';
          valueFormat.message = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
          listError.push({ ...valueFormat });
        }
      }
      // name
      if (item.name?.length > 50 || item.name === '') {
        valueFormat.line = i + 1;
        valueFormat.field = 'name';
        valueFormat.key = '氏名';
        valueFormat.message =
          item.name === ''
            ? MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR
            : MESSAGE.MSG_SAFE_DIGITS_ERROR;
        listError.push({ ...valueFormat });
      }
      // name_kana
      if (
        item.name_kana?.length > 50 ||
        (!/^[ァ-ヴー\u3000]+$/.test(item.name_kana) &&
          item.name_kana?.length > 0)
      ) {
        valueFormat.line = i + 1;
        valueFormat.field = 'name_kana';
        valueFormat.key = '氏名（カナ）';
        if (
          !/^[ァ-ヴー\u3000]+$/.test(item.name_kana) &&
          item.name_kana?.length > 0
        ) {
          valueFormat.message = MESSAGE.MSG_LIMITS_KANA_ERROR;
        } else if (item.name_kana?.length > 50) {
          valueFormat.message = MESSAGE.MSG_SAFE_DIGITS_ERROR;
        }
        listError.push({ ...valueFormat });
      }
      // province_name
      if (item.province_name === '') {
        valueFormat.line = i + 1;
        valueFormat.field = 'province_name';
        valueFormat.key = '都道府県';
        valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        listError.push({ ...valueFormat });
      }
      // phone
      if (item.phone?.length > 256) {
        valueFormat.line = i + 1;
        valueFormat.field = 'phone';
        valueFormat.key = '連絡先等';
        valueFormat.message = MESSAGE.MSG_SAFE_DIGITS_ERROR;
        listError.push({ ...valueFormat });
      }
      // expiry_date_start
      if (
        (!dayjs(item.expiry_date_start, 'YYYY-MM-DD', true).isValid() &&
          item.expiry_date_start !== '') ||
        (isDistribute && item.expiry_date_start !== '') ||
        (dayjs(item.expiry_date, 'YYYY-MM-DD', true).isValid() &&
          dayjs(item.expiry_date_start, 'YYYY-MM-DD', true).isValid() &&
          dayjs(item.expiry_date).diff(item.expiry_date_start) < 0) ||
        (item.expiry_date_start === '' && isCatch) ||
        (item.expiry_date_start === '' && item.expiry_date !== '' && isEel)
      ) {
        valueFormat.line = i + 1;
        valueFormat.field = 'expiry_date_start';
        valueFormat.key = '許可の有効期間（開始）';
        if (item.expiry_date_start === '' && isCatch) {
          valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        } else if (
          item.expiry_date_start === '' &&
          item.expiry_date !== '' &&
          isEel
        ) {
          valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        } else if (isDistribute && item.expiry_date_start !== '') {
          valueFormat.message = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
        } else if (
          !dayjs(item.expiry_date_start, 'YYYY-MM-DD', true).isValid() &&
          item.expiry_date_start !== ''
        ) {
          valueFormat.message = MESSAGE.MSG_LIMITS_DATE_ERROR_CSV;
        } else {
          valueFormat.message = MESSAGE.MSG_DATE_CONSTRAINT_ERROR;
        }
        listError.push({ ...valueFormat });
      }
      // expiry_date
      if (
        (!dayjs(item.expiry_date, 'YYYY-MM-DD', true).isValid() &&
          item.expiry_date !== '') ||
        (isDistribute && item.expiry_date !== '') ||
        (isCatch && item.expiry_date === '') ||
        (isEel && item.expiry_date === '' && item.expiry_date_start !== '')
      ) {
        valueFormat.line = i + 1;
        valueFormat.field = 'expiry_date';
        valueFormat.key = '許可の有効期間（終了）';
        if (item.expiry_date === '' && isCatch) {
          valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        } else if (
          item.expiry_date === '' &&
          isEel &&
          item.expiry_date_start !== ''
        ) {
          valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        } else if (isDistribute && item.expiry_date !== '') {
          valueFormat.message = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
        } else if (
          !dayjs(item.expiry_date, 'YYYY-MM-DD', true).isValid() &&
          item.expiry_date !== ''
        ) {
          valueFormat.message = MESSAGE.MSG_LIMITS_DATE_ERROR_CSV;
        } else {
          valueFormat.message = MESSAGE.MSG_DATE_CONSTRAINT_ERROR;
        }
        listError.push({ ...valueFormat });
      }
      // note1
      if (item.note_1?.length > 256) {
        valueFormat.line = i + 1;
        valueFormat.field = 'note_1';
        valueFormat.key = '備考1';
        valueFormat.message = MESSAGE.MSG_SAFE_DIGITS_ERROR;
        listError.push({ ...valueFormat });
      }
      // note2
      if (item.note_2?.length > 256) {
        valueFormat.line = i + 1;
        valueFormat.field = 'note_2';
        valueFormat.key = '備考2';
        valueFormat.message = MESSAGE.MSG_SAFE_DIGITS_ERROR;
        listError.push({ ...valueFormat });
      }
      // check duplicate user_code
      if (
        /^\d{7}-\d{4}$/.test(item.user_code) &&
        listUserCodeDuplicate.includes(item.user_code)
      ) {
        valueFormat.line = i + 1;
        valueFormat.field = 'user_code';
        valueFormat.key = 'トレサビシステムユーザーID';
        valueFormat.message = MESSAGE.MSG_USER_CODE_DUPLICATE_IN_CSV;
        listError.push({ ...valueFormat });
      } else if (
        !listUserCodeDuplicate.includes(item.user_code) &&
        /^\d{7}-\d{4}$/.test(item.user_code)
      ) {
        listUserCodeDuplicate.push(item.user_code);
      }
      // enterprise_type
      if (!item.enterprise_type) {
        valueFormat.line = i + 1;
        valueFormat.field = 'enterprise_type';
        valueFormat.key = '事業者種別';
        valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        listError.push({ ...valueFormat });
      } else if (!['0', '5', '9'].includes(item.enterprise_type)) {
        valueFormat.line = i + 1;
        valueFormat.field = 'enterprise_type';
        valueFormat.key = '事業者種別';
        valueFormat.message = MESSAGE.MSG_IS_ROLE_NOK;
        listError.push({ ...valueFormat });
      }
      // staff_type
      if (!item.staff_type) {
        valueFormat.line = i + 1;
        valueFormat.field = 'staff_type';
        valueFormat.key = '事業者/従事者区分';
        valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        listError.push({ ...valueFormat });
      } else if (!['0', '1'].includes(item.staff_type)) {
        valueFormat.line = i + 1;
        valueFormat.field = 'staff_type';
        valueFormat.key = '事業者/従事者区分';
        valueFormat.message = MESSAGE.MSG_IS_ENTERPRISE_NOK;
        listError.push({ ...valueFormat });
      } else if (isEel && isStaff) {
        valueFormat.line = i + 1;
        valueFormat.field = 'staff_type';
        valueFormat.key = '事業者/従事者区分';
        valueFormat.message =
          MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_WORKERCLASS_ERROR;
        listError.push({ ...valueFormat });
      }
      // account_registration_status
      if (!item.account_registration_status) {
        valueFormat.line = i + 1;
        valueFormat.field = 'account_registration_status';
        valueFormat.key = 'アカウント登録状況';
        valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        listError.push({ ...valueFormat });
      } else if (!['0', '1'].includes(item.account_registration_status)) {
        valueFormat.line = i + 1;
        valueFormat.field = 'account_registration_status';
        valueFormat.key = 'アカウント登録状況';
        valueFormat.message = MESSAGE.MSG_IS_ACCOUNT_REGISTRATION_STATUS_NOK;
        listError.push({ ...valueFormat });
      } else if (!item.user_code && item.account_registration_status === '1') {
        valueFormat.line = i + 1;
        valueFormat.field = 'account_registration_status';
        valueFormat.key = 'アカウント登録状況';
        valueFormat.message =
          MESSAGE.MSG_INVALID_ACCOUNT_REGISTRATION_STATUS_ERROR;
        listError.push({ ...valueFormat });
      }
      // status
      if (!item.status) {
        valueFormat.line = i + 1;
        valueFormat.field = 'status';
        valueFormat.key = '状態';
        valueFormat.message = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
        listError.push({ ...valueFormat });
      } else if (!['0', '1', '2'].includes(item.status)) {
        valueFormat.line = i + 1;
        valueFormat.field = 'status';
        valueFormat.key = '状態';
        valueFormat.message = MESSAGE.MSG_IS_STATUS_NOK;
        listError.push({ ...valueFormat });
      } else if (
        item.enterprise_code &&
        item.status === '2' &&
        !item.user_code
      ) {
        valueFormat.line = i + 1;
        valueFormat.field = 'status';
        valueFormat.key = '状態';
        valueFormat.message = MESSAGE.MSG_INVALID_STATUS_ERROR;
        listError.push({ ...valueFormat });
      }
    });

    if (listError.length > 0) {
      validate.errors = [
        {
          message: listError,
        },
      ];
    }
    return listError.length === 0;
  },
});

export default $ajv;
