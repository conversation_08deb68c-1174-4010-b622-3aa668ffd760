<template>
  <q-input
    v-model="model"
    v-bind="$attrs"
    ref="inputRef"
    popup-content-class="popup-content-class-custom tw:text-l-design"
    hide-bottom-space
    no-error-icon
    @blur="handleBlur"
    @focus="handleFocus"
  >
    <template v-for="(_, name) in $slots" v-slot:[name]="props" :key="name">
      <slot :name="name" :props="props" />
    </template>
  </q-input>
</template>

<script setup>
import IMask from 'imask';
import { onMounted, ref } from 'vue';

defineOptions({ inheritAttrs: false });
const rawValue = ref();
const inputRef = ref();
const props = defineProps({
  mask: Object,
});
const model = defineModel();

// #region: functions
const handleFocus = () => {
  rawValue.value = model.value;
};
// #endregion
onMounted(() => {
  if (props.mask && Object.keys(props.mask).length) {
    const inputElement = inputRef.value.$el.querySelector('input');
    if (inputElement) {
      const masked = new IMask(inputElement, props.mask);
      masked.on('accept', () => {
        model.value = masked.value;
        rawValue.value = masked.value;
      });
    }
  }
});
</script>
