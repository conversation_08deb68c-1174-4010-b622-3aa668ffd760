const BaseService = require('../base/serviceFn');
const { ControlledException } = require('../base/errors');
const { MESSAGE } = require('../utils/message');
const dayjs = require('../boot/dayjs');
const { ADMIN_SYSTEM_SETTING_KEYS_ENUM } = require('../helpers/enum');
class SettingService extends BaseService {
  // ====== 1. Private method ==========

  // ====== 2. Public method ==========
  async getSetting(user) {
    const connect = this.DB.READ;
    const setting = await connect.settings.findFirst({
      where: {
        user_id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        unit_per_gram: true,
        price_per_kilogram: true,
        destination_id: true,
        display_shipment_weight: true,
        display_actual_received: true,
        qr_scan_init: true,
        receipt_number: true,
        report_type: true,
        include_tax_type: true,
        enable_session_timeout: true,
        session_expirytime: true,
        price_per_quantity: true,
        inventory_control_type: true,
      }
    });

    const systemSetting = await connect.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.CATCHING_RESERVE_PERIOD,
        delete_flag: false
      },
      select: {
        setting_value: true
      }
    });

    if (!setting || !systemSetting) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 402);
    }
    return this.SUCCESS({
      ...setting,
      catching_reserve_period: systemSetting.setting_value,
    });
  }

  async updateSetting(user, body) {
    const connect = this.DB.WRITE;

    await connect.settings.update({
      where: {
        user_id: user.id,
      },
      data: {
        ...body,
        destination_id: body.destination_id || null,
        latest_updated_by_id: user.id,
        latest_updated_on: dayjs().toDate(),
      }
    });
    return this.SUCCESS({ message: MESSAGE.MSG_CHANGED_SETTING_INFO });
  }
}

module.exports = SettingService;
