<template>
  <div class="tw:p-6">
    <!-- title -->
    <div
      class="tw:text-2xl tw:tl:text-[2.3rem] tw:pb-2 tw:border-b tw:border-gray tw:font-bold"
    >
      新規ユーザー登録
    </div>
    <!-- content -->
    <div>
      <!-- notice code -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
      届出番号<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model="enterpriseCode"
        autocomplete="nope"
        lazy-rules
        maxlength="7"
        inputmode="numeric"
        :error="!!errors.enterpriseCode"
        :error-message="errors.enterpriseCode"
      />
      <!-- company name -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
      事業者名<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model="enterpriseName"
        autocomplete="nope"
        lazy-rules
        maxlength="50"
        :error="!!errors.enterpriseName"
        :error-message="errors.enterpriseName"
      />
      <!-- name kana -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        事業者名（カナ）
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model="enterpriseNameKana"
        autocomplete="nope"
        lazy-rules
        maxlength="50"
        :error="!!errors.enterpriseNameKana"
        :error-message="errors.enterpriseNameKana"
      />
      <!-- phone number -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        携帯電話番号
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model="mobileNumber"
        autocomplete="nope"
        lazy-rules
        type="tel"
        inputmode="numeric"
        maxlength="11"
        :error="!!errors.mobileNumber"
        :error-message="errors.mobileNumber"
      />
      <!-- password -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        パスワード<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model="password"
        autocomplete="nope"
        maxlength="256"
        :type="showPassword ? 'text' : 'password'"
        lazy-rules
        :error="!!errors.password"
        :error-message="errors.password"
      >
        <template v-slot:append>
          <q-icon
            :name="showPassword ? 'visibility' : 'visibility_off'"
            @click.prevent="showPassword = !showPassword"
          />
        </template>
      </q-input>
      <!-- confirm password -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        パスワード（確認用）<span class="tw:text-red">*</span>
      </div>
      <q-input
        class="tw:mt-1"
        outlined
        v-model="passwordConfirm"
        autocomplete="nope"
        maxlength="256"
        :type="showPasswordConfirm ? 'text' : 'password'"
        lazy-rules
        :error="!!errors.passwordConfirm"
        :error-message="errors.passwordConfirm"
      >
        <template v-slot:append>
          <q-icon
            :name="showPasswordConfirm ? 'visibility' : 'visibility_off'"
            @click.prevent="showPasswordConfirm = !showPasswordConfirm"
          />
        </template>
      </q-input>
      <!-- policy -->
      <div
        class="tw:mt-5 tw:text-base tw:tl:text-[1.53rem] tw:tl:leading-[2.3rem]"
      >
        <div>以下の利用規約をご確認の上、登録を行ってください。</div>
        <div class="tw:text-blue-1 tw:cursor-pointer" @click.prevent="openPolicy">
          利用規約はこちら
        </div>
        <div class="tw:mt-2">
          <q-checkbox v-model="acceptPolicy" label="利用規約に同意する" />
          <div
          class="tw:text-[12px] tw:text-red-error tw:ml-6"
          >{{ acceptPolicyError }}</div>
        </div>
      </div>
      <q-btn
        class="tw:w-full tw:mt-5 tw:text-base tw:tl:text-[1.53rem]
        tw:font-bold tw:text-white"
        label="確認する"
        type="button"
        :class="`tw:bg-${colorSub}`"
        @click.prevent="confirmUser"
      />
    </div>
  </div>
  <PopupConfirmItems/>
</template>

<script setup>

import { ref, provide } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import PopupConfirmItems from 'components/PopupConfirmItems.vue';
import MESSAGE from 'helpers/message';
import addUserSchema from 'schemas/addUser';
import UserService from 'services/user.service';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();
const { errors, validateData } = useValidate();

// ===== REF =====
const showPassword = ref(false);
const showPasswordConfirm = ref(false);
const enterpriseName = ref('');
const enterpriseCode = ref('');
const enterpriseNameKana = ref('');
const mobileNumber = ref('');
const passwordConfirm = ref('');
const password = ref('');
const acceptPolicy = ref(false);
const formDataConfirm = ref({});
const acceptPolicyError = ref('');

// ===== METHOD =====
const openPolicy = () => {
  const link = router.resolve({ name: 'privacyPolicyUser' }).href;
  window.open(link, '_blank');
};

const confirmFunc = async () => {
  formDataConfirm.value.licenseCode = router.currentRoute.value.params.licenseCode;
  const result = await UserService.addUser(formDataConfirm.value);
  if (result) {
    await router.push({
      name: 'home',
    });
  }
};

const popupConfirmItems = ref({
  isPopup: false,
  titlePopup: '以下の内容で登録します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 122,
  minWidthTlDefault: 172,
  minWidthDtDefault: 264,
});

const changeTypeEnterprises = value => {
  let typeEnterprises = '';
  if (value.startsWith('0')) {
    typeEnterprises = '採捕事業者';
  } else if (value.startsWith('5')) {
    typeEnterprises = '取扱事業者';
  } else {
    typeEnterprises = '養鰻事業者';
  }
  return typeEnterprises;
};

const confirmUser = () => {
  // validate
  const formData = {
    enterpriseName: enterpriseName.value,
    enterpriseCode: enterpriseCode.value,
    enterpriseNameKana: enterpriseNameKana.value,
    mobileNumber: mobileNumber.value,
    password: password.value,
    passwordConfirm: passwordConfirm.value,
  };

  const valid = validateData(addUserSchema, formData);
  if (!valid) {
    return;
  }

  if (!acceptPolicy.value) {
    acceptPolicyError.value = MESSAGE.MSG_NA_TERMS_ERROR;
    return;
  }

  // Change name type enterprise
  const typeEnterprises = changeTypeEnterprises(enterpriseCode.value);

  popupConfirmItems.value.isPopup = true;
  popupConfirmItems.value.titlePopup = '以下の内容で登録します';
  popupConfirmItems.value.listItems = ([
    { key: '届出番号', value: enterpriseCode.value },
    { key: '事業者名', value: enterpriseName.value },
    { key: '事業者名（カナ）', value: enterpriseNameKana.value },
    { key: '携帯電話番号', value: mobileNumber.value },
    { key: '事業者/従事者区分', value: typeEnterprises },

  ]);
  formDataConfirm.value = formData;
  acceptPolicyError.value = '';
};

// provide
provide('popupConfirmItems', popupConfirmItems);

</script>
