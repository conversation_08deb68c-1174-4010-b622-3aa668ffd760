import { ROLES_ENUM } from 'helpers/constants';
import <PERSON><PERSON><PERSON><PERSON> from 'helpers/message';

const searchAdminNotificationCondition = {
  type: 'object',
  additionalProperties: false,
  properties: {
    title: {
      type: 'string',
    },
    creator: {
      type: 'string',
      pattern: '^[\u0020-\u007E]*$',
      errorMessage: {
        pattern: MESSAGE.MSG_CHARACTERS_HALF_SIZE_ERROR,
      },
    },
    startDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
    roles: {
      type: 'array',
      items: {
        type: 'string',
      },
    },
  },
  errorMessage: {
    additionalProperties: 'No additional properties are allowed',
  },
};

export default searchAdminNotificationCondition;
