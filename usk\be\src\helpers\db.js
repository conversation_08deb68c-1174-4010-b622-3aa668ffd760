const { PrismaClient, Prisma } = require('@prisma/client');
const { DB_LOGGER } = require('../boot/logger');
const dayjs = require('../boot/dayjs');

Prisma.Decimal.prototype.toJSON = function () {
  return this.toNumber();
};

BigInt.prototype.toJSON = function () {
  return Number(this);
};

const logConfig = [
  {
    level: 'query',
    emit: 'event',
  },
  {
    level: 'error',
    emit: 'stdout',
  },
  {
    level: 'info',
    emit: 'stdout',
  },
  {
    level: 'warn',
    emit: 'stdout',
  },
];

module.exports = class DB {
  currentCompanyRlsExtension() {
    const serviceId = '';
    return Prisma.defineExtension((prisma) =>
      prisma.$extends({
        query: {
          $allModels: {
            async $allOperations({ model, operation, args, ...rest }) {
              const setRLS = async (client) => {
                await client.$executeRaw`SELECT set_config('app.service_id', ${serviceId}, TRUE)`;
              };

              //set current date for created_on in create query
              if (operation === "create") { args.data.created_on = dayjs().toDate(); }
              else if (operation === "update") {
                //set current date for latest_updated_on in update query
                if (args.data.latest_updated_on === undefined || args.data.latest_updated_on === null) {
                  args.data.latest_updated_on = dayjs().toDate();
                };
              }

              // check if this query already runs within a transaction
              if (rest.__internalParams.transaction) {
                const transaction = rest.__internalParams.transaction;
                if (transaction.kind === 'itx') {
                  // we are in an interactive transaction and create a client for it
                  const transactionClient =
                    prisma._createItxClient(transaction);
                  setRLS(transactionClient);
                  return transactionClient[model][operation](args);
                } else if (transaction.kind === 'batch') {
                  throw new Error(
                    'Sequential transactions are not supported in prisma client extensions.'
                  );
                }
              } else {
                // if we are not in a transaction, we can run our own
                return prisma.$transaction(async (tx) => {
                  setRLS(tx);
                  return tx[model][operation](args);
                });
              }
            },
          },
        },
      })
    );
  }

  constructor({ DBR_CONNECTION_STRING, DBW_CONNECTION_STRING }) {
    // const prismaWriteEndpoint = new PrismaClient({ log: logConfig });

    const prismaReadEndpoint = new PrismaClient({
      datasources: {
        db: {
          url: DBR_CONNECTION_STRING,
        },
      },
      log: logConfig,
    });

    const prismaWriteEndpoint = new PrismaClient({
      datasources: {
        db: {
          url: DBW_CONNECTION_STRING,
        },
      },
      log: logConfig,
    });

    if (process.env.NODE_ENV !== 'production') {
      prismaWriteEndpoint.$on('query', (e) => {
        const data = {
          SQL: e,
          LogType: 'SQL',
        };
        DB_LOGGER.query(data);
      });

      prismaReadEndpoint.$on('query', (e) => {
        const data = {
          SQL: e,
          LogType: 'SQL',
        };
        DB_LOGGER.query(data);
      });
    }

    this.READ = prismaReadEndpoint.$extends(this.currentCompanyRlsExtension());
    this.WRITE = prismaWriteEndpoint.$extends(
      this.currentCompanyRlsExtension()
    );
  }
};
