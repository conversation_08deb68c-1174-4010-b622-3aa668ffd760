<template>
  <q-tabs
    :class="`tw:border tw:rounded-xl tw:text-[#004AB9] tw:tl:mr-2 tw:w-full
    tw:h-[4.25rem] tw:min-h-[4.25rem] tw:block tw:overflow-hidden`"
    v-model="activeTab"
    :active-class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:overflow-hidden`"
    indicator-color="transparent"
  >
    <q-tab
      v-for="(tab, index) in provideData.tabOptions"
      :key="tab"
      :name="tab.value"
      :label="tab.label"
      class="tw:w-full tw:h-[4.25rem] tw:min-h-[4.25rem] tw:block tw:overflow-hidden"
      :class="index !==  provideData.tabOptions.length - 1 ? 'tw:border-r' : ''"
      content-class="tw:font-bold tw:overflow-hidden tw:block tw:p-0 tw:pt-[0.5rem]"
      @click.prevent="provideData.handleClickTabItem(tab)"
    />
  </q-tabs>
</template>

<script setup>
import { inject, ref } from 'vue';

const provideData = inject('tabsItemProvideData');

const activeTab = provideData.activeTab || ref(provideData.tabOptions[0].value);
</script>

<style>
.q-tab__label {
  font-size: 2rem !important;
  line-height: 1.5 !important;
}
</style>
