const { GetObjectCommand } = require('@aws-sdk/client-s3');
const { s3Client } = require('./s3client');

const convertToString = async (data) => {
  data.ResponseContentLanguage = 'ja';
  const getObjectCommand = new GetObjectCommand(data);
  const res = await s3Client.send(getObjectCommand);
  const result = await res.Body.transformToString();
  return result;
};

module.exports = { convertToString };