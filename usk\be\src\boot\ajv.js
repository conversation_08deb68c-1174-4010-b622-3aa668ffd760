const Ajv = require('ajv');
const AjvErrors = require('ajv-errors');
const dayjs = require('../boot/dayjs');

const ajv = new Ajv({
  allErrors: true,
  removeAdditional: true,
  useDefaults: true,
  coerceTypes: true,
  $data: true,
});
// enhance the ajv instance
AjvErrors(ajv);

// add custom formats
ajv.addFormat('slash-date', {
  type: 'string',
  validate: (data) => dayjs(data, 'YYYY/MM/DD', true).isValid() || !data,
  compare: (data1, data2) => {
    return new Date(data1).getTime() - new Date(data2).getTime();
  },
});

// add keyWord
ajv.addKeyword({
  keyword: 'checkFormatDecimal',
  type: 'number',
  validate: function validate(_, data) {
    if (!data) return true;
    const regex = /^-?\d{1,14}(\.\d{1,2})?$/;
    const checkFormatDecimal = regex.test(data);
    if (checkFormatDecimal) return true;
    this.errors = [
      {
        keyword: 'checkFormatDecimal',
        params: { keyword: 'checkFormatDecimal' },
      },
    ];
    return false;
  },
  errors: true,
});

require('ajv-keywords')(ajv);
require('ajv-formats')(ajv);

module.exports = ajv;
