<template>
  <div class="tw:my-4">
    <!-- userID -->
    <div class="tw:flex tw:flex-col tw:my-2">
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        ユーザーID
      </span>
      <div
        class="tw:font-[400] tw:text-[1rem] tw:tl:min-h-[3.54rem] tw:min-h-[3rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4 tw:tl:text-[1.53rem]"
      >
        {{ userCode }}
      </div>
    </div>
    <!-- name -->
    <div class="tw:flex tw:flex-col">
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        氏名<span class="tw:text-red">*</span>
      </span>
      <div class="tw:mt-1">
        <q-input
          outlined
          v-model.trim="data.name"
          autocomplete="nope"
          lazy-rules
          maxlength="50"
          :error="!!errors.name"
          :error-message="errors.name"
          no-error-icon
          :class="{ 'tw:mb-[1.2rem] tw:sm:mb-[0rem]': !!errors.name }"
        />
      </div>
    </div>
    <!-- name_kana -->
    <div class="tw:flex tw:flex-col">
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        氏名（カナ）
      </span>
      <q-input
        outlined
        v-model.trim="data.nameKana"
        autocomplete="nope"
        lazy-rules
        maxlength="50"
        :error="!!errors.nameKana"
        :error-message="errors.nameKana"
        no-error-icon
      />
    </div>
    <!-- license code -->
    <div
      class="tw:flex tw:flex-col"
      v-if="
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          user
        )
      "
    >
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        許可番号
      </span>
      <q-input
        class="tw:mt-1"
        outlined
        v-model.trim="data.licenseNumber"
        autocomplete="nope"
        lazy-rules
        maxlength="256"
        :error="!!errors.licenseNumber"
        :error-message="errors.licenseNumber"
        no-error-icon
      />
    </div>
    <!-- phone -->
    <div class="tw:flex tw:flex-col tw:my-2">
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        携帯電話番号
      </span>
      <q-input
        class="tw:mt-1"
        outlined
        type="tel"
        v-model="data.phone"
        autocomplete="off"
        lazy-rules
        maxlength="256"
        :error="!!errors.phone"
        :error-message="errors.phone"
        no-error-icon
      />
    </div>

    <!-- password -->
    <div class="tw:flex tw:flex-col tw:my-2">
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        パスワード
      </span>
      <div
        class="tw:font-[400] tw:text-[1rem] tw:dt:text-[1rem] tw:bg-qcard tw:p-4 tw:tl:text-[1.53rem]"
      >
        ********
      </div>
    </div>

    <!-- note1 -->
    <div
      class="tw:flex tw:flex-col tw:my-2"
      v-if="
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          user
        )
      "
    >
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        備考１
      </span>
      <q-input
        class="tw:mt-1"
        outlined
        v-model.trim="data.note1"
        autocomplete="nope"
        lazy-rules
        maxlength="256"
        :error="!!errors.note1"
        :error-message="errors.note1"
        no-error-icon
      />
    </div>

    <!-- note2 -->
    <div
      class="tw:flex tw:flex-col tw:my-2"
      v-if="
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          user
        )
      "
    >
      <span
        class="tw:font-[400] tw:text-[1rem] tw:tl:text-[1.53rem] tw:dt:text-[1rem] tw:pb-1"
      >
        備考２
      </span>
      <q-input
        class="tw:mt-1"
        outlined
        v-model.trim="data.note2"
        autocomplete="nope"
        lazy-rules
        maxlength="256"
        :error="!!errors.note2"
        :error-message="errors.note2"
        no-error-icon
      />
    </div>

    <div class="tw:mt-2 tw:flex tw:flex-col tw:gap-2">
      <q-btn
        class="tw:w-full tw:text-white tw:flex tw:justify-center
        tw:items-center tw:mr-4 tw:font-[700] tw:tl:leading-[2.3rem]
        tw:tl:text-[1.53rem] tw:border tw:leading-[1.5rem] tw:text-[1rem] tw:rounded-lg"
        :class="`tw:bg-${colorSub}`"
        label="確認する"
        @click.prevent="edit"
      />
    </div>
    <PopupConfirmItems />
  </div>
</template>
<script setup>
import { onMounted, provide, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import staffService from 'services/staff.service';
import toast from 'utilities/toast';
import PopupConfirmItems from 'components/PopupConfirmItems.vue';
import useValidate from 'composables/validate';
import editStaff from 'schemas/editStaff';
import {
  ROLES_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
} from 'helpers/constants';
import { CHECK_ROLE } from 'src/helpers/common';
import { useAuthStore } from 'src/stores/auth-store';

const { colorSub } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const data = ref({});
const { errors, validateData } = useValidate();
const userCode = ref();
// method

const confirmFunc = async () => {
  const result = await staffService.editStaff(
    router.currentRoute.value.params.id,
    data.value
  );
  if (result.code === 0) {
    toast.access(result.payload.message);
    router.push({
      name: 'employeeList',
    });
  }
};

const popupConfirmItems = ref({
  isPopup: false,
  titlePopup: '以下の内容で、従事者を修正します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 90,
  minWidthTlDefault: 138,
  minWidthDtDefault: 212,
});

const edit = async () => {
  const valid = validateData(editStaff, data.value);
  if (!valid) {
    return;
  }
  if (
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
      [STAFF_TYPE_ENUM.ENTERPRISE],
      user.value
    )
  ) {
    popupConfirmItems.value.listItems = [
      { key: '氏名', value: data.value.name },
      { key: '氏名（カナ）', value: data.value.nameKana },
      { key: '許可番号', value: data.value.licenseNumber },
      { key: '携帯電話番号', value: data.value.phone },
      { key: '備考１', value: data.value.note1 },
      { key: '備考２', value: data.value.note2 },
    ];
  } else {
    popupConfirmItems.value.listItems = [
      { key: '氏名', value: data.value.name },
      { key: '氏名（カナ）', value: data.value.nameKana },
      { key: '携帯電話番号', value: data.value.phone },
    ];
  }

  popupConfirmItems.value.isPopup = true;
};

// provide
provide('popupConfirmItems', popupConfirmItems);

onMounted(async () => {
  const result = await staffService.getOneStaff(
    router.currentRoute.value.params.id
  );
  if (result.code !== 401) {
    if (result.code === 0) {
      data.value = {
        name: result.payload.data.name,
        nameKana: result.payload.data.name_kana,
        note1: result.payload.data.note_1,
        note2: result.payload.data.note_2,
        phone: result.payload.data.phone,
        licenseNumber: result.payload.data.license_number,
      };
      userCode.value = result.payload.data.user_code;
    } else {
      await router.push({
        name: 'home',
      });
    }
  }
});
</script>
