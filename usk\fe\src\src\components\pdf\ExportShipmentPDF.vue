<template>
  <div class="parentPdf tw:font-pdfSans">
    <div id="export-shipment-pdf-file-data">
      <div :style="{ marginLeft: '32px', fontSize: '36px', fontWeight: 'bold' }">日付・数量のみ表示</div>
      <div class="tw:mt-[64px] tw:space-y-10">
        <table v-for="(shipping, index) in distributionOrigins" :key="shipping" class="page-break-after">
          <thead>
            <tr>
              <th>No.</th>
              <th>漁獲/荷口番号</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>No.</th>
              <th>関連漁獲/荷口番号<br />又は輸入/養殖である旨</th>
              <th>事業者名</th>
              <th>日付</th>
              <th>数量[g]</th>
              <th>総量[g]</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(origin, originIndex) in shipping || []" :key="originIndex">
              <tr
                v-for="(inventory, inventoryIndex) in origin.shippingInfo || []"
                :key="inventory.code"
              >
                <!-- Chỉ hiển thị dữ liệu cha ở dòng đầu tiên -->
                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.shippingInfo.length" class="tw:text-left">{{ upper(index, index) }}</td>
                  <td :rowspan="origin.shippingInfo.length" class="tw:w-[200px] tw:text-left">
                    {{ hash(generateOriginCode(origin.code),'code') }}
                  </td>
                  <td :rowspan="origin.shippingInfo.length" class="tw:w-[190px] tw:text-left">
                    {{ user.role === ROLES_ENUM.NORMAL_USER ?
                    '####': `${origin.starting_user_name}(${origin.starting_enterprise_name})` }}
                  </td>
                  <td :rowspan="origin.shippingInfo.length" class="tw:w-[100px] tw:text-left">
                    {{ FORMAT_DATE(origin.shipping_date) }}
                  </td>
                  <td :rowspan="origin.shippingInfo.length" class="tw:w-[100px] tw:text-right">
                    {{ FORMAT_NUMBER(origin.shipping_net_weight) }}
                  </td>
                </template>

                <!-- Inventory info -->
                <td class="tw:text-left">{{ upper(index + 1, inventoryIndex) }}</td>
                <td class="tw:w-[200px] tw:text-left">
                  {{ hash(generateOriginCode(inventory.code),'code') }}
                </td>
                <td class="tw:w-[190px] tw:text-left">
                  {{ user.role === ROLES_ENUM.NORMAL_USER ?
                    '####': `${inventory.starting_user_name}(${inventory.starting_enterprise_name})` }}
                </td>
                <td class="tw:w-[100px] tw:text-left">
                  {{ FORMAT_DATE(inventory.shipping_date) }}
                </td>
                <td class="tw:w-[100px] tw:text-right">
                  {{ FORMAT_NUMBER(inventory.arrival_net_weight) }}
                </td>

                <template v-if="inventoryIndex === 0">
                  <td :rowspan="origin.shippingInfo.length" class="tw:w-[100px] tw:text-right">
                    {{ FORMAT_NUMBER(origin.sum) }}
                  </td>
                </template>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import { FORMAT_DATE, FORMAT_NUMBER } from 'helpers/common';
import { maskCodeString } from 'src/helpers/common';
import { useAuthStore } from 'src/stores/auth-store';
import { storeToRefs } from 'pinia';
import { ROLES_ENUM } from 'src/helpers/constants';

const data = inject('exportShipmentPDFProvideData');

const distributionOrigins = computed(() => data.value.distributionOrigins);
const upper = (i, inventoryIndex) =>
`${String.fromCharCode(65 + i)}${String(inventoryIndex + 1).padStart(2, '0')}`;

const { user } = storeToRefs(useAuthStore());

const generateOriginCode = code => {
  if (!code) {
    return '';
  }
  return maskCodeString(code);
};

const hash = (text = '', type = 'code')=>{
  if (user.value.role === ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN){
    return text;
  }
  if (type === 'code'){
    return text.split('-')
    .map((part, index) => {
      if (index === 0) {
        return part[0] + '#'.repeat(part.length - 1);
      }
      return '#'.repeat(part.length - 1);
    })
    .join('-');
  }
  return '#####';
};

</script>

<style scoped>
.parentPdf {
  display: none !important;
  font-size: 100% !important;
}

table {
  width: 100%;
  border-collapse: collapse;
}

tr {
  page-break-inside: avoid;
}

th,
td {
  border: 1px solid #000;
  padding-left: 8px !important;
  padding-right: 8px !important;
  padding-top: 0px;
  padding-bottom: 14px !important;
  vertical-align: middle;
  height: 40px;
  font-size: 14px !important;
}

td {
  font-weight: 400;
}

th {
  background-color: #fff;
  text-align: center;
  font-weight: bold !important;
}
</style>
