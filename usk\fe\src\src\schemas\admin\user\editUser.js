import MESSAGE from 'src/helpers/message';
import {OPTIONS_STATUS_REGISTER_ACCOUNT, OPTIONS_STATUS_USER, USER_STATUS_REGISTER_ENUM } from 'src/helpers/constants';

const editUserSchema = {
  additionalProperties: false,
  type: 'object',
  properties: {
    provinceId: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterprise_type: {
      type: 'integer',
    },
    staff_type: {
      type: 'integer',
    },
    enterpriseCode: {
      type: 'string',
      minLength: 1,
      maxLength: 7,
      pattern: '^\\d{7}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_ENTERPRISENUMBER_ERROR,
      },
    },
    name: {
      type: 'string',
      minLength: 1,
      maxLength: 50,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        maxLength: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    nameKana: {
      type: 'string',
      oneOf: [
        {
          minLength: 0,
          maxLength: 0,
        },
        {
          maxLength: 50,
          pattern: '^[ァ-ヴー　]+$',
        },
      ],
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_KANA_ERROR,
      },
    },
    licenseNumber: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    phone: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
      },
    },
    startExpiryDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endExpiryDate',
      },
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endExpiryDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
    status: {
      type: 'integer',
      enum: OPTIONS_STATUS_USER.map(item => item.value),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    account_registration_status: {
      type: 'integer',
      enum: Object.values(USER_STATUS_REGISTER_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        type: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    password: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
      pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.NOTE1_ERROR,
      },
    },
    note2: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.NOTE2_ERROR,
      },
    },
    enable_export_function: {
      type: 'boolean',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    province_custom_data: {
      type: 'object',
    },
  },
};

export default editUserSchema;
