<template>
  <form>
    <div
      :class="`tw:grid tw:cols-1 tw:mt-2 tw:gap-x-3
      tw:grid-cols-1 tw:tl:grid-cols-2 tw:tl:gap-x-5`"
    >
      <div class="tw:mb-4">
        <span class="tw:tl:mb-2 tw:text-xs-design tw:block tw:mb-1">
          仕入先(届出事業者名)</span
        >
        <BaseInput
          v-model="searchFormProvideData.form.value.enterpriseName"
          :error="!!searchFormProvideData.errors.value.enterpriseName"
          :error-message="searchFormProvideData.errors.value.enterpriseName"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          autocomplete="nope"
          no-error-icon
        />
      </div>

      <div class="tw:mb-4">
        <span class="tw:tl:mb-2 tw:text-xs-design tw:block tw:mb-1">
          仕入先(事業者名)</span
        >

        <BaseInput
          v-model="searchFormProvideData.form.value.name"
          :error="!!searchFormProvideData.errors.value.name"
          :error-message="searchFormProvideData.errors.value.name"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          autocomplete="nope"
          no-error-icon
        />
      </div>

      <div class="tw:mb-4">
        <span class="tw:tl:mb-2 tw:text-xs-design tw:block tw:mb-1">
          {{ searchFormProvideData.forHistory ? '修正日' : '入荷日' }}
        </span>
        <div class="tw:flex tw:tl:items-center tw:gap-2 tw:mb-1 tw:flex-col tw:tl:flex-row">
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchFormProvideData.form.value.startDate"
              :error="!!searchFormProvideData.errors.value.startDate"
              :error-message="searchFormProvideData.errors.value.startDate"
              :class="{'tw:mb-[3rem] tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]'
                : !!searchFormProvideData.errors.value.startDate ||
                !!searchFormProvideData.errors.value.endDate}"
            />
          </div>
          <span class="tw:text-[1.5rem] tw:font-semibold "
          :class="{'tw:mb-[4rem] tw:sm:mb-[1rem] tw:tl:mb-[1.5rem] tw:dt:mb-[2.3rem]'
            : !!searchFormProvideData.errors.value.startDate
            || !!searchFormProvideData.errors.value.endDate}">~</span>
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="searchFormProvideData.form.value.endDate"
              :error="!!searchFormProvideData.errors.value.endDate"
              :error-message="searchFormProvideData.errors.value.endDate"
              :class="{'tw:mb-[3rem] tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]'
                : !!searchFormProvideData.errors.value.startDate ||
                !!searchFormProvideData.errors.value.endDate}"
            />
          </div>
        </div>
      </div>
      <div class="tw:mb-4">
        <span class="tw:tl:mb-2 tw:text-xs-design tw:block tw:mb-1">
          届出番号</span
        >

        <BaseInput
          v-model="searchFormProvideData.form.value.code"
          :error="!!searchFormProvideData.errors.value.code"
          :error-message="searchFormProvideData.errors.value.code"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          autocomplete="nope"
          no-error-icon
          inputmode="numeric"
          maxlength="7"
        />
      </div>

      <div class="tw:mb-4">
        <span class="tw:tl:mb-2 tw:text-xs-design tw:block tw:mb-1">
          許可番号</span
        >

        <BaseInput
          v-model="searchFormProvideData.form.value.licenseNumber"
          :error="!!searchFormProvideData.errors.value.licenseNumber"
          :error-message="searchFormProvideData.errors.value.licenseNumber"
          outlined
          input-class="tw:text-[#333333]
            tw:pb-1 tw:font-[400] tw:text-m-design"
          class="tw:mt-2"
          autocomplete="nope"
          no-error-icon
        />
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import {inject} from 'vue';
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
// #endregion
// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion
</script>
