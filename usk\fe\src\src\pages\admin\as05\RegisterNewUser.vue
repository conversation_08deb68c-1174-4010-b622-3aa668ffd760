<template>
  <div class="tw:flex tw:justify-center">
    <div class="tw:w-[40%] tw:min-w-[550px]">
      <!-- province -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          都道府県<span class="tw:text-red">*</span>
        </span>
        <q-select
        outlined
        map-options
        emit-value
        v-model="dataRegister.provinceId"
        :options="optionsRegions"
        class="tw:text-base"
        :error="!!errors.provinceId"
        :error-message="errors.provinceId"
        no-error-icon

      >
        <template v-slot:option="scope">
          <div>
            <div
              class="text-weight-bold text-black q-pl-sm q-pt-xs q-pb-xs"
              style="pointer-events: none;"
            >
              {{ scope.opt.label }}
            </div>

            <div v-for="child in scope.opt.children" :key="child.value">
              <q-item
                dense
                clickable
                v-ripple
                v-close-popup
                @click="dataRegister.provinceId = child.value"
                :class="{ 'bg-light-blue-1': dataRegister.provinceId === child.value }"
              >
                <q-item-section>
                  <q-item-label>{{ child.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </template>

        <template v-slot:selected-item>
          <q-item v-if="selectedProvince" class="tw:p-0">
            <q-item-section>
              <q-item-label>{{ selectedProvince.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-select>

      </div>
      <!-- enterprise_type -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          事業者種別<span class="tw:text-red">*</span>
        </span>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionsEnterpriseType"
          v-model="dataRegister.enterpriseType"
          class="tw:text-base"
          :error="!!errors.enterpriseType"
          :error-message="errors.enterpriseType"
          no-error-icon
        />
      </div>
      <!-- enterprise_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          届出番号
        </span>
        <q-input
          v-model="dataRegister.enterpriseCode"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="7"
          :error="!!errors.enterpriseCode"
          :error-message="errors.enterpriseCode"
          no-error-icon
        />
      </div>
      <!-- typeStaffOrEnterprise -->
      <div class="tw:flex tw:flex-col tw:mt-4 tw:mb-6">
        <span class="tw:text-base tw:pb-1">
          事業者/従事者区分<span class="tw:text-red">*</span>
        </span>
        <q-field
          :error="!!errors.typeStaffOrEnterprise"
          :error-message="errors.typeStaffOrEnterprise"
          borderless
          no-error-icon
          class="tw:mt-1"
        >
          <template #control>
            <div class="tw:flex tw:flex-row gap-2">
              <q-radio
                v-for="(item, index) in STAFF_OR_ENTERPRISE"
                :key="index"
                v-model="dataRegister.typeStaffOrEnterprise"
                :val="item.value"
                :label="item.label"
              />
            </div>
          </template>
        </q-field>
      </div>
      <!-- enterprise_name -->
      <div class="tw:flex tw:flex-col tw:mb-4">
        <span class="tw:text-base tw:pb-1">
          事業者名<span class="tw:text-red">*</span>
        </span>
        <q-input
          v-model="dataRegister.enterpriseName"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="50"
          :error="!!errors.enterpriseName"
          :error-message="errors.enterpriseName"
          no-error-icon
        />
      </div>
      <!-- enterprise_name_kana -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          事業者名（カナ）
        </span>
        <q-input
          v-model="dataRegister.enterpriseNameKana"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="50"
          :error="!!errors.enterpriseNameKana"
          :error-message="errors.enterpriseNameKana"
          no-error-icon
        />
      </div>
      <!-- license_number -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          許可番号
        </span>
        <q-input
          v-model="dataRegister.licenseNumber"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.licenseNumber"
          :error-message="errors.licenseNumber"
          no-error-icon
        />
      </div>
      <!-- phone -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          連絡先等
        </span>
        <q-input
          v-model="dataRegister.phone"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.phone"
          :error-message="errors.phone"
          no-error-icon
        />
      </div>
      <!-- expiry_date -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          採捕許可有効期間
        </span>
        <div class="tw:flex tw:gap-2 tw:mb-1 tw:items-center">
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="dataRegister.startExpiryDate"
              :error="!!errors.startExpiryDate"
              :error-message="errors.startExpiryDate"
              input-class="tw:text-base"
            />
          </div>
          <span class="tw:text-[0.77rem] tw:mb-[18px]"> ～ </span>
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="dataRegister.endExpiryDate"
              :error="!!errors.endExpiryDate"
              :error-message="errors.endExpiryDate"
              input-class="tw:text-base"
            />
          </div>
        </div>
      </div>
      <!-- status -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          状態<span class="tw:text-red">*</span>
        </span>
        <q-select
          outlined
          map-options
          emit-value
          :options="OPTIONS_STATUS_USER_REGISTER"
          v-model="dataRegister.status"
          class="tw:text-base"
          :error="!!errors.status"
          :error-message="errors.status"
          no-error-icon
        />
      </div>
      <!-- status register -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          アカウント登録状況
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:break-all tw:text-base tw:min-h-14">
          仮登録
        </div>
      </div>
      <!-- password -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          初期パスワード<span class="tw:text-red">*</span>
        </span>
        <q-input
          class="tw:mt-3"
          outlined
          v-model="dataRegister.password"
          autocomplete="new-password"
          lazy-rules
          :error="!!errors.password"
          :error-message="errors.password"
          no-error-icon
          maxlength="256"
          input-class="tw:text-base"
        >
        </q-input>
      </div>
      <!-- note 1 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          備考1
        </span>
        <q-input
          v-model="dataRegister.note1"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.note1"
          :error-message="errors.note1"
          no-error-icon
        />
      </div>
      <!-- note 2 -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          備考2
        </span>
        <q-input
          v-model="dataRegister.note2"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.note2"
          :error-message="errors.note2"
          no-error-icon
        />
      </div>
      <!-- button -->
      <div class="tw:flex tw:justify-center tw:gap-4">
        <BaseButton
          outline
          padding="0.75rem"
          :class="`tw:bg-white tw:text-${colorMain} tw:w-44 tw:text-[1rem]`"
          label="戻る"
          @click.prevent="goBack"
        />
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-44 tw:text-[1rem]`"
          label="確認する"
          @click.prevent="confirmRegisterUser"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
// #region import
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import useValidate from 'composables/validate';
import {
  LIST_PROVINCE,
  STAFF_OR_ENTERPRISE,
} from 'helpers/constants';
import BaseDatePicker from 'components/base/BaseDatePicker.vue';
import registerUserSchema from 'src/schemas/admin/user/registerUser';
import BaseButton from 'src/components/base/BaseButton.vue';
import { ENTERPRISE_TYPE_ENUM, ENTERPRISE_TYPE_OPTIONS,
OPTIONS_STATUS_USER_REGISTER, STAFF_TYPE_ENUM } from 'src/helpers/constants';
import regionsService from 'src/shared/services/regions.service';
import MESSAGE from 'src/helpers/message';
// #endregion import

const router = useRouter();
const { colorMain, colorSub, dataAdminRegisterUser } = storeToRefs(useAppStore());
const { setAdminRegisterUser } = useAppStore();
const { errors, validateData } = useValidate();

const optionsRegions = ref([]);
const dataRegister = ref({});
const optionsEnterpriseType = ref(ENTERPRISE_TYPE_OPTIONS);

const showPassword = ref(false);

const selectedProvince = computed(() => {
  const provinceId = dataRegister.value.provinceId;
  for (const region of optionsRegions.value) {
    const found = region.children.find(p => p.value === provinceId);
    if (found) {return found;}
  }
  return null;
});

const goBack = () => {
  router.back();
};

const confirmRegisterUser = () => {
  const data = {
    provinceId: dataRegister.value.provinceId,
    enterpriseType: dataRegister.value.enterpriseType,
    enterpriseCode: dataRegister.value.enterpriseCode,
    typeStaffOrEnterprise: dataRegister.value.typeStaffOrEnterprise,
    enterpriseName: dataRegister.value.enterpriseName,
    enterpriseNameKana: dataRegister.value.enterpriseNameKana,
    licenseNumber: dataRegister.value.licenseNumber,
    phone: dataRegister.value.phone,
    startExpiryDate: dataRegister.value.startExpiryDate,
    endExpiryDate: dataRegister.value.endExpiryDate,
    status: dataRegister.value.status,
    password: dataRegister.value.password,
    note1: dataRegister.value.note1,
    note2: dataRegister.value.note2,
  };
  const newErrors = ref({});

  errors.value = {};
  newErrors.value = {};

  if (data.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE || data.enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    if (data.startExpiryDate && !data.endExpiryDate) {
      newErrors.value.endExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.startExpiryDate && data.endExpiryDate) {
      newErrors.value.startExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
  }

  if (data.enterpriseCode && !/^\d{7}$/.test(data.enterpriseCode)) {
    newErrors.value.enterpriseCode = MESSAGE.MSG_LIMITS_ENTERPRISENUMBER_ERROR;
  }

  // EEL_FARMING_ENTERPRISE
  if (data.enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    if (data.typeStaffOrEnterprise === STAFF_TYPE_ENUM.STAFF) {
      newErrors.value.enterpriseType = MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_WORKERCLASS_ERROR;
    }
    if (!data.licenseNumber) {
      newErrors.value.licenseNumber = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.enterpriseCode) {
      delete data.enterpriseCode;
    } else if (!/^\d{7}$/.test(data.enterpriseCode)) {
      newErrors.value.enterpriseCode = MESSAGE.MSG_LIMITS_ENTERPRISENUMBER_ERROR;
    }
    if (!data.endExpiryDate) {
      delete data.endExpiryDate;
    }
    if (!data.startExpiryDate) {
      delete data.startExpiryDate;
    }
  }

  // DISTRIBUTE_ENTERPRISE
  if (data.enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
    if (!data.enterpriseCode) {
      newErrors.value.enterpriseCode = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.enterpriseCode.startsWith('5') && data.enterpriseCode !== '') {
      newErrors.value.enterpriseCode = MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_DISTRIBUTOR_ERROR;
    }

    if (data.licenseNumber) {
      newErrors.value.licenseNumber = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
    } else {
      delete data.licenseNumber;
    }

    if (data.startExpiryDate) {
      newErrors.value.startExpiryDate = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
    } else {
      delete data.startExpiryDate;
    }

    if (data.endExpiryDate) {
      newErrors.value.endExpiryDate = MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR;
    } else {
      delete data.endExpiryDate;
    }
  }

  // CATCH_ENTERPRISE
  if (data.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    if (!data.enterpriseCode) {
      newErrors.value.enterpriseCode = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.enterpriseCode.startsWith('0') && data.enterpriseCode !== '') {
      newErrors.value.enterpriseCode = MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_SAIHO_ERROR;
    }
    if (!data.licenseNumber) {
      newErrors.value.licenseNumber = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.startExpiryDate) {
      newErrors.value.startExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.endExpiryDate) {
      newErrors.value.endExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
  }
  const valid = validateData(registerUserSchema, data);
  errors.value = { ...errors.value, ...newErrors.value };
  if (!valid || Object.keys(newErrors.value).length > 0) {return;}

  setAdminRegisterUser(data);
  router.push({ name: 'adminUserRegisterComplete' });
};

const generateSecurePassword = (length = 8) => {
  const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lower = 'abcdefghijklmnopqrstuvwxyz';
  const number = '**********';
  const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  const getRandom = chars => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(upper),
    getRandom(lower),
    getRandom(number),
    getRandom(special),
  ];

  const allChars = upper + lower + number + special;

  for (let i = password.length; i < length; i++) {
    password.push(getRandom(allChars));
  }

  password = password.sort(() => 0.5 - Math.random());

  return password.join('');
};

// onMounted
onMounted(async () => {
  dataRegister.value = dataAdminRegisterUser.value;
  if (!dataRegister.value.password) {
    dataRegister.value.password = generateSecurePassword();
  }
  const listRegions = await regionsService.getRegionWithProvinces();
  optionsRegions.value = listRegions.payload.items.map(item => ({
    label: item.region_name,
    children: item.province.map(province => ({
      label: province.name,
      value: province.id,
    })),
  }));
});

</script>
<style scoped>
:deep(.q-radio__label) {
  font-size: 1rem;
  line-height: 1.5rem;
}

:deep(.q-radio__inner) {
  font-size: 2.75rem;
}

</style>
