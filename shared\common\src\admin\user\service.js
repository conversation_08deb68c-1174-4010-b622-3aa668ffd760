const ShortUniqueId = require('short-unique-id');
const { Prisma } = require('@prisma/client');
const { PutObjectCommand } = require('@aws-sdk/client-s3');
const { ControlledException } = require('../../base/errors');
const BaseService = require('../../base/serviceFn');
const {
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
  USER_STATUS_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  DYNAMODB_TABLE_NAME,
  STAFF_TYPE_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
  TOTAL_LIMIT_EXPORT,
  USER_STATUS_REGISTER_ENUM,
} = require('../../helpers/enum');
const { MESSAGE } = require('../../utils/message');
const dayjs = require('../../boot/dayjs');
const {
  getSignedUrl: getSignedUrlS3,
} = require('@aws-sdk/s3-request-presigner');
const { s3Client } = require('../../utils/s3client');
const Env = require('../../boot/env');
const { scanDb, putDb, queryDb } = require('../../base/scandynamodb');

class UserAdminService extends BaseService {
  // ====== 1. Private method ==========

  // ====== 2. Public method ==========
  async getUserListAdmin(query) {
    const {
      limit,
      provinceId,
      enterpriseName,
      userName,
      userId,
      status,
      statusRegister,
      userType,
      licenseNumber,
      startUpdateDate,
      endUpdateDate,
      startExpiryDate,
      endExpiryDate,
      note1,
      note2,
    } = query;

    const connect = this.DB.READ;

    const USER_TYPE_MAPPING = {
      [ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF]: {
        enterprise_type: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        staff_type: STAFF_TYPE_ENUM.STAFF,
      },
      [ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE]: {
        enterprise_type: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
      },
      [ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE]: {
        enterprise_type: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
      },
      [ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF]: {
        enterprise_type: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
        staff_type: STAFF_TYPE_ENUM.STAFF,
      },
      [ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE]: {
        enterprise_type: ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
      },
    };

    const mappedType = USER_TYPE_MAPPING[userType] || {};

    const searchCondition = {
      delete_flag: false,
      province_id: provinceId !== undefined ? +provinceId : undefined,

      name: userName?.trim()
        ? { contains: userName.trim(), mode: 'insensitive' }
        : undefined,

      user_code: userId?.trim()
        ? { contains: userId.trim().replace(/\s+/g, ''), mode: 'insensitive' }
        : undefined,

      status:
        status !== undefined
          ? {
            in: Array.from(
              new Set([...(Array.isArray(status) ? status : [status]), 2])
            ),
          }
          : status,

      license_number: licenseNumber?.trim()
        ? {
          contains: licenseNumber.trim().replace(/\s+/g, ''),
          mode: 'insensitive',
        }
        : undefined,

      role: ROLES_ENUM.NORMAL_USER,

      latest_updated_on: {
        gte: startUpdateDate
          ? dayjs.getDateFromJST(`${startUpdateDate} 00:00:00`).toDate()
          : undefined,
        lte: endUpdateDate
          ? dayjs.getDateFromJST(`${endUpdateDate} 23:59:59`).toDate()
          : undefined,
      },

      license:
        startExpiryDate || endExpiryDate
          ? {
            expiry_date: {
              gte: startExpiryDate
                ? dayjs.getDateFromJST(`${startExpiryDate} 00:00:00`).toDate()
                : undefined,
              lte: endExpiryDate
                ? dayjs.getDateFromJST(`${endExpiryDate} 23:59:59`).toDate()
                : undefined,
            },
          }
          : undefined,

      note_1: note1?.trim()
        ? { contains: note1.trim().replace(/\s+/g, ''), mode: 'insensitive' }
        : undefined,

      note_2: note2?.trim()
        ? { contains: note2.trim().replace(/\s+/g, ''), mode: 'insensitive' }
        : undefined,

      account_registration_status: statusRegister ? +statusRegister : undefined,

      enterprise_type: mappedType.enterprise_type ?? undefined,
      staff_type: mappedType.staff_type ?? undefined,

      enterprise: enterpriseName?.trim()
        ? {
          enterprise_name: {
            contains: enterpriseName.trim(),
            mode: 'insensitive',
          },
          delete_flag: false,
        }
        : undefined,
    };

    const totalCount = await connect.users.count({
      where: searchCondition,
    });

    if (totalCount > TOTAL_LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_TOO_MANY_RESULTS);
    }

    if (totalCount === 0) {
      return this.SUCCESS({
        items: [],
        total_item: 0,
        page: 1,
        page_size: +limit,
      });
    }

    const filter = {
      select: {
        id: true,
        name: true,
        name_kana: true,
        user_code: true,
        password: true,
        account_registration_status: true,
        status: true,
        phone: true,
        province_id: true,
        role: true,
        license_number: true,
        latest_updated_on: true,
        note_1: true,
        note_2: true,
        enable_export_function: true,
        license: {
          select: {
            license_code: true,
            expiry_date_start: true,
            expiry_date: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
        province_custom_data: true,
        provinces: true,
        enterprise: {
          select: {
            id: true,
            enterprise_name: true,
          },
        },
      },
      where: searchCondition,
    };

    const dataSearch = await connect.users.findMany({
      ...filter,
      orderBy: { latest_updated_on: 'desc' },
    });

    let isKagoshima = false;
    const data = dataSearch.map((value) => {
      if (
        value.account_registration_status === USER_STATUS_REGISTER_ENUM.OFFICIAL
      ) {
        delete value.password;
      }
      if (value.provinces?.setting?.is_show_info) {
        isKagoshima = true;
      }

      return {
        ...value,
        enterpriseName: value.enterprise?.enterprise_name || null,
      };
    });

    return this.SUCCESS({
      items: data,
      total_item: totalCount,
      page_size: totalCount,
      isKagoshima,
    });
  }

  async getUserDetail(userId) {
    const connect = this.DB.READ;
    const data = await connect.users.findFirst({
      where: {
        id: +userId,
        delete_flag: false,
      },
      include: {
        license: true,
        enterprise: true,
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    if (
      data.account_registration_status === USER_STATUS_REGISTER_ENUM.OFFICIAL
    ) {
      delete data.password;
    }
    if (data.province_custom_data) {
      Object.keys(data.province_custom_data).forEach((key) => {
        if (typeof data.province_custom_data[key] === 'string') {
          data.province_custom_data[key] = data.province_custom_data[
            key
          ].replaceAll(';', '\n');
        }
      });
    }
    return this.SUCCESS(data);
  }

  async editUser(id, body, user) {
    // define connect for read and write
    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    const {
      provinceId,
      enterpriseCode,
      phone,
      name,
      nameKana,
      startExpiryDate,
      endExpiryDate,
      licenseNumber,
      password,
      status,
      note1,
      note2,
      account_registration_status,
      enable_export_function,
      province_custom_data,
    } = body;

    // check exist user
    const checkExistUser = await connectRead.users.findFirst({
      where: {
        id: +id,
      },
      include: {
        license: true,
      },
    });

    if (!checkExistUser) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // data user for update
    const userDataUpdate = {
      province_id: provinceId,
      name: name,
      name_kana: nameKana,
      name_nospace: name?.replace(/\s+/g, ''),
      name_kana_nospace: nameKana?.replace(/\s+/g, ''),
      license_number: licenseNumber,
      status,
      note_1: note1,
      note_2: note2,
      latest_updated_by_id: user.id,
      password: password || checkExistUser.password,
      phone: phone || '',
      account_registration_status: account_registration_status,
      enable_export_function: enable_export_function,
    };

    if (province_custom_data) {
      for (let key in province_custom_data) {
        if (typeof province_custom_data[key] === 'string') {
          province_custom_data[key] = province_custom_data[key].replace(
            /\r\n|\r|\n/g,
            ';'
          );
        }
      }
      userDataUpdate.province_custom_data = Object.assign(
        checkExistUser.province_custom_data || {},
        province_custom_data
      );
    }

    await connectWrite.$transaction(
      async (tx) => {
        // not change expiry date and start expiry date
        const isEel =
          checkExistUser.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE;
        const isCatch =
          checkExistUser.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE;
        const isDistribute =
          checkExistUser.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE;

        const hasExpiryDateChanged =
          (endExpiryDate || startExpiryDate) &&
          (!dayjs(checkExistUser.license?.expiry_date).isSame(
            dayjs.getDateFromJST(`${endExpiryDate} 23:59:59`).toDate(),
            'day'
          ) ||
            !dayjs(checkExistUser.license?.expiry_date_start).isSame(
              dayjs.getDateFromJST(`${startExpiryDate} 00:00:00`).toDate(),
              'day'
            ));

        if (hasExpiryDateChanged && (isCatch || isEel)) {
          if (isCatch) {
            // CATCH: Always create a new license and mark the old one as deleted
            const licenseCode = new ShortUniqueId({
              length: 16,
              dictionary: 'alphanum',
            }).randomUUID();

            if (checkExistUser.license_id) {
              await tx.licenses.update({
                where: {
                  id: checkExistUser.license_id,
                  delete_flag: false,
                },
                data: {
                  delete_flag: true,
                  latest_updated_by_id: user.id,
                  latest_updated_on: dayjs().toDate(),
                },
              });
            }

            const newLicense = await tx.licenses.create({
              data: {
                expiry_date: dayjs
                  .getDateFromJST(`${endExpiryDate} 23:59:59`)
                  .toDate(),
                expiry_date_start: dayjs
                  .getDateFromJST(`${startExpiryDate} 00:00:00`)
                  .toDate(),
                created_by_id: user.id,
                user_id: +id,
                license_code: licenseCode,
              },
            });

            await tx.users.update({
              where: { id: +id },
              data: {
                ...userDataUpdate,
                license_id: newLicense.id,
                latest_updated_by_id: user.id,
                latest_updated_on: dayjs().toDate(),
              },
            });
          }

          if (isEel) {
            if (checkExistUser.license_id) {
              // EEL: If a license exists, update its dates
              await tx.licenses.update({
                where: {
                  id: checkExistUser.license_id,
                  delete_flag: false,
                },
                data: {
                  expiry_date: dayjs
                    .getDateFromJST(`${endExpiryDate} 23:59:59`)
                    .toDate(),
                  expiry_date_start: dayjs
                    .getDateFromJST(`${startExpiryDate} 00:00:00`)
                    .toDate(),
                  latest_updated_by_id: user.id,
                  latest_updated_on: dayjs().toDate(),
                },
              });

              await tx.users.update({
                where: { id: +id },
                data: {
                  ...userDataUpdate,
                  latest_updated_by_id: user.id,
                  latest_updated_on: dayjs().toDate(),
                },
              });
            } else {
              // EEL: No license exists, create a new one
              const licenseCode = new ShortUniqueId({
                length: 16,
                dictionary: 'alphanum',
              }).randomUUID();

              const newLicense = await tx.licenses.create({
                data: {
                  expiry_date: dayjs
                    .getDateFromJST(`${endExpiryDate} 23:59:59`)
                    .toDate(),
                  expiry_date_start: dayjs
                    .getDateFromJST(`${startExpiryDate} 00:00:00`)
                    .toDate(),
                  created_by_id: user.id,
                  user_id: +id,
                  license_code: licenseCode,
                },
              });

              await tx.users.update({
                where: { id: +id },
                data: {
                  ...userDataUpdate,
                  license_id: newLicense.id,
                  latest_updated_by_id: user.id,
                  latest_updated_on: dayjs().toDate(),
                },
              });
            }
          }
        } else {
          if (isDistribute) {
            // DISTRIBUTE: Do nothing with license, keep license_id unchanged
            await tx.users.update({
              where: { id: +id },
              data: {
                ...userDataUpdate,
                latest_updated_by_id: user.id,
                latest_updated_on: dayjs().toDate(),
              },
            });
          } else {
            // When date fields are empty or unchanged
            if (!endExpiryDate && !startExpiryDate) {
              if (isCatch && checkExistUser.license_id) {
                // CATCH: Delete existing license and remove license_id
                await tx.licenses.update({
                  where: {
                    id: checkExistUser.license_id,
                    delete_flag: false,
                  },
                  data: {
                    delete_flag: true,
                    latest_updated_by_id: user.id,
                    latest_updated_on: dayjs().toDate(),
                  },
                });

                await tx.users.update({
                  where: { id: +id },
                  data: {
                    ...userDataUpdate,
                    license_id: null,
                    latest_updated_by_id: user.id,
                    latest_updated_on: dayjs().toDate(),
                  },
                });
              } else if (isEel) {
                // EEL: Do nothing with license, keep license_id unchanged
                if (checkExistUser.license_id) {
                  await tx.licenses.update({
                    where: {
                      id: checkExistUser.license_id,
                      delete_flag: false,
                    },
                    data: {
                      expiry_date: null,
                      expiry_date_start: null,
                      latest_updated_by_id: user.id,
                      latest_updated_on: dayjs().toDate(),
                    },
                  });
                }
                await tx.users.update({
                  where: { id: +id },
                  data: {
                    ...userDataUpdate,
                    latest_updated_by_id: user.id,
                    latest_updated_on: dayjs().toDate(),
                  },
                });
              }
            } else {
              // Dates haven't changed, just update user data
              await tx.users.update({
                where: { id: +id },
                data: {
                  ...userDataUpdate,
                  latest_updated_by_id: user.id,
                  latest_updated_on: dayjs().toDate(),
                },
              });
            }
          }
        }

        // check if user is enterprise representative and has changed enterprise code
        if (
          checkExistUser.enterprise_type ===
          ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
        ) {
          await tx.enterprises.update({
            where: { id: checkExistUser.enterprise_id },
            data: {
              enterprise_code: enterpriseCode,
              latest_updated_on: dayjs().toDate(),
              latest_updated_by_id: user.id,
            },
          });
        }
        // check if user is enterprise representative and has changed name or name kana
        if (
          checkExistUser.user_code.endsWith('0000') &&
          (name || nameKana) &&
          (checkExistUser.name !== name ||
            checkExistUser.name_kana !== nameKana)
        ) {
          await tx.enterprises.update({
            where: {
              id: checkExistUser.enterprise_id,
            },
            data: {
              enterprise_name: name,
              enterprise_name_kana: nameKana,
              enterprise_name_nospace: name?.replace(/\s+/g, ''),
              enterprise_name_kana_nospace: nameKana?.replace(/\s+/g, ''),
              latest_updated_on: dayjs().toDate(),
              latest_updated_by_id: user.id,
            },
          });
        }
      },
      { isolationLevel: Prisma.TransactionIsolationLevel.Serializable }
    );
    return this.SUCCESS();
  }

  async registerNewUser(body, user) {
    const {
      provinceId,
      enterpriseType,
      enterpriseCode,
      typeStaffOrEnterprise,
      enterpriseName,
      enterpriseNameKana,
      licenseNumber,
      startExpiryDate,
      endExpiryDate,
      status,
      password,
      note1,
      note2,
    } = body;

    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;
    let checkEnterpriseExist = null;
    const dateTimeNow = dayjs().toDate();

    if (
      enterpriseCode &&
      enterpriseType !== ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      checkEnterpriseExist = await connectRead.enterprises.findFirst({
        where: {
          enterprise_code: enterpriseCode,
          type: enterpriseType,
        },
      });
    }
    if (
      checkEnterpriseExist &&
      typeStaffOrEnterprise === STAFF_TYPE_ENUM.ENTERPRISE
    ) {
      throw new ControlledException(MESSAGE.MSG_USED_NOTIFICATIONUMBER_ERROR);
    }

    if (
      typeStaffOrEnterprise === STAFF_TYPE_ENUM.STAFF &&
      !checkEnterpriseExist
    ) {
      throw new ControlledException(
        MESSAGE.MSG_INVARID_CREATEUSER_NOTWORKER_ERROR
      );
    }

    if (
      typeStaffOrEnterprise === STAFF_TYPE_ENUM.STAFF &&
      enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      throw new ControlledException(
        MESSAGE.MSG_INVARID_NOTIFICATIONUMBER_WORKERCLASS_ERROR
      );
    }

    if (enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
      if (licenseNumber || startExpiryDate || endExpiryDate) {
        throw new ControlledException(
          MESSAGE.MSG_INVARID_ITEM_DISTRIBUTOR_ERROR
        );
      }
    }

    await connectWrite.$transaction(
      async (tx) => {
        let newEnterprise;
        if (!checkEnterpriseExist) {
          newEnterprise = await tx.enterprises.create({
            data: {
              enterprise_code:
                !enterpriseCode &&
                  enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
                  ? ''
                  : enterpriseCode,
              enterprise_name: enterpriseName,
              enterprise_name_kana: enterpriseNameKana || null,
              enterprise_name_nospace: enterpriseName.replace(/\s+/g, ''),
              enterprise_name_kana_nospace:
                enterpriseNameKana?.replace(/\s+/g, '') || null,
              type:
                enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
                  ? ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
                  : enterpriseType ===
                    ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
                    ? ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
                    : ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE,
              created_by_id: user.id,
              delete_flag: false,
              latest_updated_on: dateTimeNow,
            },
          });
        }

        let staffType,
          qrScanInit = null;

        staffType =
          typeStaffOrEnterprise === 0
            ? STAFF_TYPE_ENUM.ENTERPRISE
            : STAFF_TYPE_ENUM.STAFF;

        qrScanInit = SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA;

        const plainPassword = password;
        const isTargetEnterprise =
          enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
          enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE;

        const newUser = await tx.users.create({
          data: {
            enterprise_id: checkEnterpriseExist
              ? checkEnterpriseExist.id
              : newEnterprise.id,
            name: enterpriseName,
            name_kana: enterpriseNameKana || null,
            name_nospace: enterpriseName?.replace(/\s+/g, ''),
            name_kana_nospace: enterpriseNameKana?.replace(/\s+/g, '') || null,
            phone: body.phone || '',
            password: plainPassword,
            role: ROLES_ENUM.NORMAL_USER,
            statistics_date_from:
              enterpriseType === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
                ? dateTimeNow
                : null,
            status: status,
            account_registration_status: 0,
            enterprise_type: enterpriseType,
            staff_type: staffType,
            province_id: provinceId,
            latest_updated_on: null,
            license_number: isTargetEnterprise ? licenseNumber : null,
            note_1: note1,
            note_2: note2,
            created_by_id: user.id,
          },
        });

        await tx.settings.create({
          data: {
            user_id: newUser.id,
            price_per_kilogram: [200],
            delete_flag: false,
            qr_scan_init: qrScanInit,
            price_per_quantity: [400],
            display_actual_received: true,
            display_shipment_weight: true,
            created_by_id: user.id,
            unit_per_gram: 0.2,
            enable_session_timeout: true,
            latest_updated_on: dateTimeNow,
          },
        });

        const licenseCode = new ShortUniqueId({
          length: 16,
          dictionary: 'alphanum',
        }).randomUUID();

        const newLicense = await tx.licenses.create({
          data: {
            expiry_date: isTargetEnterprise
              ? endExpiryDate
                ? dayjs.getDateFromJST(`${endExpiryDate} 23:59:59`).toDate()
                : null
              : null,
            expiry_date_start: isTargetEnterprise
              ? startExpiryDate
                ? dayjs.getDateFromJST(`${startExpiryDate} 00:00:00`).toDate()
                : null
              : null,
            created_by_id: user.id,
            user_id: newUser.id,
            license_code: licenseCode,
            latest_updated_on: dateTimeNow,
          },
        });

        await tx.users.update({
          where: {
            id: newUser.id,
          },
          data: {
            license_id: newLicense.id,
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow,
          },
        });
      },
      { isolationLevel: Prisma.TransactionIsolationLevel.Serializable }
    );

    return this.SUCCESS();
  }

  async presignImportUser(body) {
    const fileKey = new ShortUniqueId({
      length: 32,
      dictionary: 'alphanum',
    }).randomUUID();
    const param = {
      Bucket: Env.S3_BUCKET_NAME_IMPORT_CSV,
      Key: `importcsvfile/${fileKey}.csv`,
    };
    const command = new PutObjectCommand(param);
    const url = await getSignedUrlS3(s3Client, command, {
      expiresIn: 14400,
    });
    const paramPutDB = {
      TableName: DYNAMODB_TABLE_NAME.IMPORT_USERS_PROCESS,
      Item: {
        fileKey: `importcsvfile/${fileKey}.csv`,
        status: 'INIT',
        createDate: dayjs.getDateFromJST().format('YYYY-MM-DD HH:mm:ss'),
        fileName: `importcsvfile/${body.nameFile}`,
      },
    };
    await putDb(paramPutDB);
    return this.SUCCESS({ data: url });
  }

  async getListImportUserFile() {
    const param = {
      ProjectionExpression:
        '#fileKey, #status, #createDate, #datetimeprocess, #insertedcount, #updatedcount, #changedcount, #contents, #fileName',
      ExpressionAttributeNames: {
        '#fileKey': 'fileKey',
        '#status': 'status',
        '#createDate': 'createDate',
        '#datetimeprocess': 'datetimeprocess',
        '#insertedcount': 'inserted_count',
        '#updatedcount': 'updated_count',
        '#changedcount': 'changed_count',
        '#contents': 'contents',
        '#fileName': 'fileName',
      },
      TableName: DYNAMODB_TABLE_NAME.IMPORT_USERS_PROCESS,
    };
    const data = await scanDb(param);
    return this.SUCCESS({ data });
  }

  async downloadDataImportUserByFile(body) {
    const { fileKey } = body;
    const param = {
      TableName: DYNAMODB_TABLE_NAME.IMPORT_USERS_DATA,
      KeyConditionExpression: '#fileKey = :fileKey',
      ExpressionAttributeNames: {
        '#fileKey': 'fileKey',
      },
      ExpressionAttributeValues: {
        ':fileKey': fileKey,
      },
      ConsistentRead: true,
    };
    const data = await queryDb(param);
    return this.SUCCESS({ data });
  }

  async bulkUpdateUserStatus(body, adminUser) {
    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;

    const { roles = [], provinceIds = [] } = body;

    if (!roles.length || !provinceIds.length) {
      throw new ControlledException(MESSAGE.MSG_REQUIRE_FILTER_CONDITION);
    }
    const validUsers = await connectRead.users.findMany({
      where: {
        enterprise_type: { in: roles },
        province_id: { in: provinceIds },
        status: USER_STATUS_ENUM.ACTIVE,
      },
      select: {
        id: true,
      },
    });

    if (!validUsers.length) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_TO_UPDATE_ERROR);
    }

    const userIds = validUsers.map((u) => u.id);

    await connectWrite.$transaction(
      async (tx) => {
        await tx.users.updateMany({
          where: {
            id: { in: userIds },
          },
          data: {
            status: 1,
            latest_updated_by_id: adminUser.id,
            latest_updated_on: dayjs().toDate(),
          },
        });
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
      }
    );

    return this.SUCCESS({ count: userIds.length });
  }
}
module.exports = UserAdminService;
