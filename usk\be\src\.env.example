# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
NODE_ENV=development
R_DATABASE_URL="postgresql://ssv:Ab123456a@@localhost:5432/uskdb?schema=public&connection_limit=10&pool_timeout=20"
W_DATABASE_URL="postgresql://ssv:Ab123456a@@localhost:5432/uskdb?schema=public&connection_limit=10&pool_timeout=20"

JWT_SECRET=XLsRiPyI6vaJjqThpj3y9wpMQ053bf
JWT_EXPIRES_IN=7d
X_API_KEY=tfsdbkuytrd3r8gxd2f7jgdry5xsgtu