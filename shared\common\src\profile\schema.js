const { MESSAGE } = require('../utils/message');
const {
  sharedSchema,
} = require('../validations');

const getProfileSchema = {
  summary: 'Get profile',
  description: 'Get profile',
  tags: ['Profile'],
  security: [{ 'bearToken': [] }],
  response: sharedSchema.response,
};

const updatePhoneSchema = {
  summary: 'Update phone',
  description: 'Update phone',
  tags: ['Profile'],
  security: [{ 'bearToken': [] }],
  body: {
    type: 'object',
    required: ['phone'],
    properties: {
      phone: {
        type: 'string',
        maxLength: 256,
        errorMessage: {
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR
        }
      }
    },
    errorMessage: {
      required: {
        phone: MESSAGE.MSG_SAFE_DIGITS_ERROR
      }
    }
  },
  response: sharedSchema.response,
}

module.exports = {
  getProfileSchema,
  updatePhoneSchema
}