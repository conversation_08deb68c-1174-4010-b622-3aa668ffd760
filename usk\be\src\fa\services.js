const dayjs = require('dayjs');
const BaseService = require('../base/serviceFn');
const {
  PAGINATION,
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
} = require('../helpers/enum');
const { InvalidPermissionException } = require('../base/errors');

class FAService extends BaseService {
  /**
   * get partner list for hybrid search
   * @param {*} user user login information
   * @param {*} queries queries from request
   * @returns partner list
   */
  async getExportShipmentList(user, queries) {
    const connect = this.DB.READ;

    // get queries
    const {
      descending = PAGINATION.DEFAULT_DESCENDING,
      page = PAGINATION.FIRST_PAGE,
      rowsPerPage = PAGINATION.PAGE_SIZE,
      code,
      enterpriseName,
      startDate,
      endDate,
    } = queries;

    // validation role
    if (user.role !== ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN) {
      throw new InvalidPermissionException();
    }

    // build search condition
    const searchCondition = {
      code: code ? { contains: code } : undefined,
      delete_flag: false,
      starting_enterprise: enterpriseName
        ? {
            delete_flag: false,
            name: { contains: enterpriseName },
          }
        : undefined,
      destination_user: {
        delete_flag: false,
        enterprise_type: ENTERPRISE_TYPE_ENUM.FOREIGN,
      },
      starting_user: {
        delete_flag: false,
        enterprise_type: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
      },
      shipping_date: {
        gte: startDate
          ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate()
          : undefined,
        lte: endDate
          ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate()
          : undefined,
      },
    };

    // calculate total item
    const total_item = await connect.the_origins.count({
      where: searchCondition,
    });

    // if total item is 0, return empty items
    if (!total_item) {
      return this.SUCCESS({
        total_item,
        items: [],
        page: 1,
        page_size: +rowsPerPage,
      });
    }

    const items = await connect.the_origins.findMany({
      where: searchCondition,
      select: {
        id: true,
        code: true,
        starting_user: {
          select: {
            id: true,
            name: true,
          },
        },
        shipping_date: true,
      },
      orderBy: {
        id: descending ? 'desc' : 'asc',
      },
    });

    return this.SUCCESS({
      total_item,
      items,
      page: page,
      page_size: +rowsPerPage,
    });
  }
}

module.exports = FAService;
