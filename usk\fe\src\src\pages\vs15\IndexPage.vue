<template>
  <q-page>
    <q-card class="tw:text-[#333333] tw:p-4">
      <div class="tw:text-l-design tw:font-bold tw:mb-4 tw:mt-2">
        ユーザー設定
      </div>
      <div class="tw:mb-[10rem]">
        <div class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-4">
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">届出番号</div>
            <div class="tw:text-l-design tw:font-bold">
              {{ profile?.enterprise?.enterprise_code }}
            </div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">事業者名</div>
            <div class="tw:text-l-design tw:font-bold">{{ profile?.name }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">事業者名（カナ）</div>
            <div class="tw:text-l-design tw:font-bold">
              {{ profile?.name_kana }}
            </div>
          </div>
          <div
            v-if="
              CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
                [],
                user
              )
            "
            class="tw:my-1"
          >
            <div class="tw:text-m-design tw:font-normal">許可番号</div>
            <div class="tw:text-l-design tw:font-bold">{{ profile?.license_number }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">連絡先等</div>
            <div class="tw:text-l-design tw:font-bold tw:break-words">{{ profile.phone }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">事業者/従事者区分</div>
            <div class="tw:text-l-design tw:font-bold">
              {{ showRole(profile) }}
            </div>
          </div>
          <div
            v-if="
              CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
                [],
                user
              )
            "
            class="tw:my-1"
          >
            <div class="tw:text-m-design tw:font-normal">採捕許可有効期限</div>
            <div class="tw:text-l-design tw:font-bold">
              {{ FORMAT_DATE(profile?.license?.expiry_date) }}
            </div>
          </div>
          <div
            class="tw:my-1"
            v-for="item in Object.keys(profile.province_custom_data || {})"
            :key="item"
          >
            <div
              v-if="
                CHECK_ROLE(
                  [ROLES_ENUM.NORMAL_USER],
                  [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
                  [],
                  user
                ) && profile.province_custom_data
              "
              class="tw:text-m-design tw:font-normal"
            >
              {{ item }}
            </div>
            <div
              v-if="
                CHECK_ROLE(
                  [ROLES_ENUM.NORMAL_USER],
                  [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
                  [],
                  user
                ) && profile.province_custom_data
              "
              class="tw:text-l-design tw:font-bold tw:whitespace-pre-line"
            >
              {{ replaceSemicolon(profile.province_custom_data[item]) }}
            </div>
          </div>
        </div>
      </div>
      <PopupConfirmItems />
    </q-card>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="トップに戻る"
        @click.prevent="goToPage('home')"
      />
    </q-footer>
  </q-page>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { onMounted, provide, ref } from 'vue';
import { useRouter } from 'vue-router';
import profileService from 'services/profile.service';
import {
  COMPARE_FULL_DATE_TIME_WITH_NOW,
  FORMAT_DATE,
  CHECK_ROLE,
} from 'helpers/common';
import { ROLES_ENUM } from 'helpers/constants';
import useValidate from 'composables/validate';
import updatePhoneSchema from 'schemas/updatePhone';
import toast from 'utilities/toast';
import PopupConfirmItems from 'components/PopupConfirmItems.vue';
import BaseButton from 'components/base/vs/BaseButton.vue';
import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import { useAuthStore } from 'src/stores/auth-store';
import { showRole } from 'src/helpers/common';

// #region variable
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const profile = ref({});
const { validateData } = useValidate();

// #endregion variable

// #region function

const confirmFunc = async () => {
  const result = await profileService.updatePhone({
    phone: profile.value.phone,
  });
  if (result.code === 0) {
    toast.access(result.payload.message);
    await router.push({ name: 'home' });
  }
};

const popupConfirmItems = ref({
  isPopup: false,
  titlePopup: '以下の内容で登録します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 90,
  minWidthTlDefault: 130,
  minWidthDtDefault: 200,
});

const pushToEdit = async () => {
  const valid = validateData(updatePhoneSchema, { phone: profile.value.phone });
  if (!valid) {
    return;
  }
  popupConfirmItems.value.listItems = [
    { key: '連絡先等', value: profile.value.phone },
  ];
  popupConfirmItems.value.isPopup = true;
};

const goToPage = name => {
  router.push({ name });
};

const replaceSemicolon = value => {
  if (value) {
    return value.replaceAll(';', '\n');
  }
  return value;
};

// #endregion function
// provide
provide('popupConfirmItems', popupConfirmItems);

onMounted(async () => {
  const result = await profileService.getProfile();
  profile.value = result.payload.data;
});
</script>
