<template>
  <form>
    <div class="tw:flex tw:mt-2 tw:flex-col tw:gap-[.5rem] tw:tl:gap-[2rem]">
      <div class ="tw:w-full tw:flex tw:flex-col tw:tl:flex-row tw:gap-[.5rem] tw:tl:gap-[2rem] tw:justify-between">
        <div class="tw:font-normal tw:text-[1rem] tw:leading-[1.5rem] tw:tl:w-[48%]">
          <div class="tw:mb-1 tw:text-xs-design">
            届出事業者名
          </div>
          <BaseInput
            v-model="searchFormProvideData.form.value.enterpriseName"
            outlined
            input-class="tw:text-[#333333]
              tw:pb-1 tw:font-[400] tw:text-m-design"
            class="tw:mt-2"
            autocomplete="nope"
            :error="!!searchFormProvideData.errors.value.enterpriseName"
            :error-message="searchFormProvideData.errors.value.enterpriseName"
            no-error-icon
            maxlength="50"
          />
        </div>
        <div class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mt-10 tw:tl:mt-0">
          <div class="tw:mb-[.25rem] tw:text-xs-design">
            事業者名
          </div>
          <BaseInput
            v-model="searchFormProvideData.form.value.name"
            outlined
            input-class="tw:text-[#333333]
              tw:pb-1 tw:font-[400] tw:text-m-design"
            class="tw:mt-2"
            maxlength="50"
            autocomplete="nope"
            :error="!!searchFormProvideData.errors.value.name"
            :error-message="searchFormProvideData.errors.value.name"
            no-error-icon
          />
        </div>

      </div>
      <div class="tw:w-full tw:flex tw:flex-col tw:tl:flex-row tw:gap-[.5rem] tw:tl:gap-[2rem] tw:justify-between">
        <div class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mt-10 tw:tl:mt-0">
          <div class="tw:mb-1 tw:text-xs-design">
            届出番号
          </div>
          <BaseInput
            v-model="searchFormProvideData.form.value.code"
            outlined
            input-class="tw:text-[#333333]
              tw:pb-1 tw:font-[400] tw:text-m-design"
            class="tw:mt-2"
            maxlength="7"
            autocomplete="nope"
            :error="!!searchFormProvideData.errors.value.code"
            :error-message="searchFormProvideData.errors.value.code"
            no-error-icon
            inputmode="numeric"
          />
        </div>
        <div class="tw:font-normal tw:text-xs-design tw:tl:w-[48%] tw:mt-10 tw:tl:mt-0">
          <div class="tw:mb-[.25rem] tw:text-xs-design">
            許可番号
          </div>
          <BaseInput
            v-model="searchFormProvideData.form.value.licenseNumber"
            outlined
            input-class="tw:text-[#333333]
              tw:pb-1 tw:font-[400] tw:text-m-design"
            class="tw:mt-2"
            maxlength="256"
            autocomplete="nope"
            :error="!!searchFormProvideData.errors.value.licenseNumber"
            :error-message="searchFormProvideData.errors.value.licenseNumber"
            no-error-icon
          />
        </div>

      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import { inject } from 'vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
// #endregion
// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion
</script>
