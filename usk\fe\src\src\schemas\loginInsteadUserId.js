import MESSAGE from 'helpers/message';

const loginInsteadSchema = {
  type: 'object',
  required: [
    'userid',
  ],
  properties: {
    userid: {
      type: 'string',
      minLength: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      userid: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  additionalProperties: false,
};

export default loginInsteadSchema;
