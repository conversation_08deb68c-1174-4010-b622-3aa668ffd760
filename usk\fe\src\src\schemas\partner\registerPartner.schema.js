import MESSAG<PERSON> from 'helpers/message';
import { ENTERPRISE_TYPE_ENUM, PARTNER_TYPE_ENUM } from 'src/helpers/constants';

const registerPartnerSchema = {
  additionalProperties: false,
  type: 'object',
  required: ['enterpriseType', 'province', 'enterpriseCode', 'enterpriseName', 'partnerType'],
  properties: {
    enterpriseType: {
      type: 'number',
      enum: Object.values(ENTERPRISE_TYPE_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    province: {
      type: 'number',
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterpriseCode: {
      type: 'string',
      pattern: '^[0-9]{7}$',
      minLength: 1,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    enterpriseName: {
      type: 'string',
      minLength: 1,
      maxLength: 50,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        maxLength: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    enterpriseNameKana: {
      type: 'string',
      maxLength: 50,
      pattern: '^[ァ-ヴー　]+$',
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_KANA_ERROR,
      },
    },
    partnerType: {
      type: 'array',
      minItems: 1,
      items: {
        type: 'number',
        enum: Object.values(PARTNER_TYPE_ENUM),
      },
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
};

export default registerPartnerSchema;
