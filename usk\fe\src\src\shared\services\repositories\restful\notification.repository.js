import BaseRepository from './base.repository';

class NotificationRepository extends BaseRepository {
  constructor() {
    super('notification');
  }

  // #region user normal site
  getNotificationList(params) {
    return this.client.get('/list', params);
  }

  getNotificationDetail(id) {
    return this.client.get(`/detail/${id}`);
  }

  checkHasUnreadNotification() {
    return this.client.get('/unread');
  }
  // #endregion user normal site

  // #region admin site
  adminGetNotificationList(params) {
    return this.client.get('/manager/list', params);
  }

  adminRegisterNotification(data) {
    return this.client.post('/manager/register', data);
  }

  adminGetNotificationDetail(id) {
    return this.client.get(`/manager/detail/${id}`);
  }

  adminDeleteNotification(id) {
    return this.client.delete(`/manager/delete/${id}`);
  }
  // #endregion admin site
}

export default new NotificationRepository();
