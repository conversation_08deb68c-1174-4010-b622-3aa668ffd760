import MESSAGE from 'helpers/message';

const editStaff = {
  additionalProperties: false,
  type: 'object',
  required: ['name'],
  properties: {
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    phone: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
      },
    },
    name: {
      type: 'string',
      minLength: 1,
      // pattern: '^[ａ-ｚＡ-Ｚ０-９、。，．・「」『』〜｛｝［］：；！？］＊＆＠＃％＋＝＞＜ぁ-んァ-ン一-龯]+$',
      maxLength: 50,
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        maxLength: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    nameKana: {
      type: 'string',
      oneOf: [
        {
          minLength: 0,
          maxLength: 0,
        },
        {
          maxLength: 50,
          pattern: '^[ァ-ヴー　]+$',
        },
      ],
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_KANA_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
    },
    note2: {
      type: 'string',
      maxLength: 256,
    },
  },
  errorMessage: {
    required: {
      name: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default editStaff;
