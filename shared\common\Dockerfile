FROM public.ecr.aws/amazonlinux/amazonlinux:2023.6.20241010.0-minimal

LABEL version="1.0.5"
LABEL description="Common back-end"
LABEL maintainer="SSV <<EMAIL>>"

ARG NODE_ENV=local
ENV NODE_ENV=$NODE_ENV

ENV NODE_VERSION=22.16.0
# install node 22
RUN dnf upgrade -y && \
  dnf -y install wget && \
  dnf -y install tar && \
  dnf -y install gzip && \
  dnf -y install which && \
  dnf -y install make gcc* && \
  dnf -y install zip && \
  dnf clean all

RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

RUN /bin/bash -c "source /root/.nvm/nvm.sh; nvm install ${NODE_VERSION}"

RUN { \
  echo 'export NVM_DIR=~/.nvm'; \
  echo '. ~/.nvm/nvm.sh'; \
  } > /root/.bashrc

RUN /bin/bash -c "source /root/.nvm/nvm.sh; nvm use ${NODE_VERSION}"

ENV PATH="/root/.nvm/versions/node/v${NODE_VERSION}/bin/:${PATH}"

# set the working directory
WORKDIR /app
# Copy the working directory in the container
COPY src/package*.json ./
# Install the project dependencies
RUN npm install
# Copy the rest of the project files to the container
COPY src/. .
# Prisma generate
RUN npx prisma generate
# Install .env
RUN npm install pm2 -g
# Expose the port 3000
EXPOSE 3000
# Run API with pm2
# RUN ["chmod", "+x", "/app/runserver.sh"]
# CMD ["/app/runserver.sh"]
CMD ["pm2-runtime", "server.js"]