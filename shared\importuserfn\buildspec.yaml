version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 22.x
    commands:
      # - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &
      # - timeout 15 sh -c "until docker info; do echo .; sleep 1; done"
  pre_build:
    commands:
      # set S3 BUCKET and stackname
      - echo START pre_build phase...
      # set environment stage : production/staging
      - |-
        [ ! -z "$UNAGI_STAGE" ] && echo $UNAGI_STAGE || export UNAGI_STAGE=development
      - echo $UNAGI_STAGE
      # set S3 BUCKET
      - echo $S3_BUCKET
      - echo $SECURITYGROUPIDS
      - echo $SUBNETIDS
      - export STACKNAME=UNAGI-IMPORTUSEFN-$UNAGI_STAGE
      - ls
      - cd shared/importuserfn/src
      #install dependencies
      - echo Installing node...
      - npm install
      - cd ..
      - ls
      - echo DONE pre_build phase...
  build:
    commands:
      - sam package --debug --template-file unagiimportusefn.yaml
        --s3-bucket $S3_BUCKET
        --output-template-file outUNAGIImportUseFN.yaml
      - sam deploy --stack-name $STACKNAME
        --capabilities CAPABILITY_IAM
        --parameter-overrides StageName=$UNAGI_STAGE SecurityGroupIds=$SECURITYGROUPIDS SubnetIds=$SUBNETIDS S3Bucket=$S3_BUCKET
        --template-file outUNAGIImportUseFN.yaml
  post_build:
    commands:
      - echo Build completed