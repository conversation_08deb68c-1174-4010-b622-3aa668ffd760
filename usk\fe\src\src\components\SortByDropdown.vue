<template>
  <q-btn-dropdown
    :class="`tw:tl:text-[1.3rem] tw:tl:leading-[1.5rem]
      tw:font-normal tw:text-[1rem] tw:leading-[1.5rem] tw:py-0`"
    icon="sort"
    :label="provideData.sortBySelectedLabel.value"
    unelevated
  >
    <q-list>
      <q-item
        v-for="option in provideData.sortByOptions"
        :key="option.value"
        clickable
        v-close-popup
        @click.prevent="handleClickOption(option)"
      >
        <q-item-section>
          <q-item-label>{{ option.label }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-btn-dropdown>
</template>

<script setup>
import { inject } from 'vue';

const provideData = inject('sortByDropdownProvideData');

const handleClickOption = option => {
  provideData.handleClickSortByItem(option);
};
</script>

<style scoped>
:deep(.q-icon.q-btn-dropdown__arrow) {
  display: none;
}
</style>
