import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';

const searchPartnerHybridListConditionSchema = {
  additionalProperties: false,
  type: 'object',
  required: ['type', 'name'],
  properties: {
    type: {
      type: 'integer',
      not: { const: '' },
      enum: Object.values(ENTERPRISE_TYPE_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    name: {
      type: 'string',
      minLength: 1,
      maxLength: 50,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    user_code: {
      type: 'string',
      minLength: 1,
      pattern: '^\\d{7}-\\d{4}$',
      errorMessage: {
        minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        pattern: MESSAGE.MSG_INVARID_USERID_ERROR,
      },
    },
    license_number: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
  },
  if: {
    properties: {
      type: { const: ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE },
    },
  },
  then: {
    required: ['license_number'],
  },
  else: {
    required: ['user_code'],
  },
};

export default searchPartnerHybridListConditionSchema;
