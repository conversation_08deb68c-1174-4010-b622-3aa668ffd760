import { ROLES_ENUM } from 'helpers/constants';
import ME<PERSON>AG<PERSON> from 'helpers/message';

const userSchema = {
  name: {
    type: 'string',
    minLength: 1,
    maxLength: 80,
    errorMessage: {
      _: 'name is invalid',
    },
  },
  password: {
    type: 'string',
    minLength: 1,
    maxLength: 256,
    pattern: '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])[a-zA-Z0-9!-~]{8,256}$',
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_LIMITS_PASSWARD_ERROR,
    },
  },
  passwordConfirm: {
    type: 'string',
    minLength: 1,
    const: {
      $data: '1/password',
    },
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_NOTMATCH_PASSWARD_ERROR,
    },
  },
  userid: {
    type: 'string',
    minLength: 1,
    maxLength: 12,
    pattern: '^\\d{7}-\\d{4}$',
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_LIMITS_ALPHACHARA_ERROR,
    },
  },
  enterpriseName: {
    type: 'string',
    minLength: 1,
    maxLength: 50,
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_CHARACTERS_ARE_NOT_FULLSIZE,
    },
  },
  enterpriseCode: {
    type: 'string',
    minLength: 1,
    maxLength: 7,
    pattern: '^\\d{7}$',
    errorMessage: {
      minLength: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      _: MESSAGE.MSG_LIMITS_ENTERPRISENUMBER_ERROR,
    },
  },
  enterpriseNameKana: {
    type: 'string',
    oneOf: [
      {
        maxLength: 50,
        pattern: '^[ァ-ヴー　]+$',
      },
      {
        minLength: 0,
        maxLength: 0,
      },
    ],
    errorMessage: {
      _: MESSAGE.MSG_LIMITS_KANA_ERROR,
    },
  },
  role: {
    minItems: 1,
    type: 'array',
    items: {
      enum: Object.values(ROLES_ENUM),
    },
    errorMessage: {
      _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
  mobileNumber: {
    type: 'string',
    oneOf: [
      {
        pattern: '^\\d{11}$',
      },
      {
        minLength: 0,
        maxLength: 0,
      },
    ],
    errorMessage: {
      _: MESSAGE.MSG_LIMITS_PHONENUMBER_ERROR,
    },
  },
  qrCode: {
    type: 'string',
  },
  licenseCode: {
    type: 'string',
    pattern: '^[A-Za-z0-9]{16}$',
  },
};

export default userSchema;
