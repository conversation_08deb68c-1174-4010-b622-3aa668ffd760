// ======== IMPORT ========================
const {
  checkUserIdSchema,
  addUserSchema,
  linkUserIdSchema
} = require('./schema');
const { apiKeyVerify } = require('../base/authorized');

// ===== 1. Router Functions ==============

async function checkUserId(request, reply) {
  const accountService = request.diScope.resolve('accountService');
  const { userId } = request.params;
  const response = await accountService.checkUserId(userId);
  return reply.send(response);
}

async function linkUserId(request, reply) {
  const accountService = request.diScope.resolve('accountService');
  const { userid, licenseCode } = request.body;
  const response = await accountService.linkUserId(userid, licenseCode);
  return reply.send(response);
}

async function addUser(request, reply) {
  const accountService = request.diScope.resolve('accountService');
  const response = await accountService.create(request.body);
  return reply.send(response);
}

// ===== 2. Router Registration ============
module.exports = async (fastify) => {
  fastify.get('/check-userid/:userId', { schema: checkUserIdSchema, onRequest: [apiKeyVerify] }, checkUserId);
  fastify.post('/link-userid', { schema: linkUserIdSchema, onRequest: [apiKeyVerify] }, linkUserId);
  fastify.post('/add-user', { schema: addUserSchema, onRequest: [apiKeyVerify] }, addUser);
};