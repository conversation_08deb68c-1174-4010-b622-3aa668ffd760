
// ======== IMPORT ========================
const { getExportShipmentListSchema } = require('./schemas');
const { auth, apiKeyVerify } = require('../base/authorized');
// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
async function getExportShipmentList(request, reply) {
  const fAService = request.diScope.resolve('fAService');
  const response = await fAService.getExportShipmentList(request.user, request.query);
  return reply.send(response);
}

// ===== 3. Router Registration ========
module.exports = async (fastify) => {
  fastify.get(
    '/export-shipment/list',
    {schema: getExportShipmentListSchema, onRequest: [apiKeyVerify, auth] },
    getExportShipmentList
  );
};
