import BaseRepository from './base.repository';

class ShippingRepository extends BaseRepository {
  constructor() {
    super('shipping');
  }

  getShippingList(query) {
    return this.client.get('/get-shipping-list', query);
  }

  getShippingDetail(id) {
    return this.client.get(`/get-shipping-detail/${id}`);
  }

  cancelShipping(id) {
    return this.client.delete(`/cancel-shipping/${id}`);
  }

  editShipping(id, body) {
    return this.client.post(`/edit-shipping/${id}`, body);
  }

  exportShippingList(query) {
    return this.client.get('/export-shipping-list', query);
  }

  exportShippingDetail(query) {
    return this.client.get('/export-shipping-detail', query);
  }

  getExportToday() {
    return this.client.get('/export-today');
  }

  resetExportToday() {
    return this.client.post('/reset-export-today');
  }
}

export default new ShippingRepository();
