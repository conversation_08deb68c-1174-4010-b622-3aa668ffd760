<template>
  <form>
    <div
      class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:mt-2 tw:gap-x-[4rem] tw:gap-y-[3rem] tw:tl:gap-y-[2.5rem] tw:text-[#333333]"
    >
      <div>
        <div class="tw:mb-1 tw:text-xs-design">事業者区分</div>
        <BaseSingleSelectInput
          v-model="searchFormProvideData.form.value.type"
          :options="ENTERPRISE_TYPE_OPTIONS"
          :error="!!searchFormProvideData.errors.value.type"
          :error-message="searchFormProvideData.errors.value.type"
          :use-input="false"
          emit-value
          map-options
          @clear="searchFormProvideData.form.value.type = ''"
          :is-clearable="!!searchFormProvideData.form.value.type"
        />
      </div>
      <div class="tw:pt-3 tw:tl:pt-0">
        <div class="tw:mb-1 tw:text-xs-design">事業者名</div>
        <BaseInput
          v-model="searchFormProvideData.form.value.name"
          outlined
          input-class="tw:text-[#333333] tw:text-m-design"
          maxlength="50"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.name"
          :error-message="searchFormProvideData.errors.value.name"
        />
      </div>
      <div
        class="tw:pt-3 tw:tl:pt-0 tw:pb-[3rem]"
        v-if="
          searchFormProvideData.form.value.type ===
          ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
        "
      >
        <div class="tw:mb-1 tw:text-xs-design">許可番号</div>
        <BaseInput
          v-model="searchFormProvideData.form.value.licenseNumber"
          outlined
          input-class="tw:text-[#333333] tw:text-m-design"
          maxlength="256"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.license_number"
          :error-message="searchFormProvideData.errors.value.license_number"
        />
      </div>
      <div class="tw:pt-3 tw:tl:pt-0 tw:pb-[3rem]" v-else>
        <div class="tw:mb-1 tw:text-xs-design">
          ユーザーID（届出番号-数字4桁）
        </div>
        <BaseInput
          v-model="searchFormProvideData.form.value.userCode"
          outlined
          input-class="tw:text-[#333333] tw:text-m-design"
          maxlength="12"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.user_code"
          :error-message="searchFormProvideData.errors.value.user_code"
          inputmode="numeric"
          :mask="{
            mask: '0000000-0000',
          }"
        />
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseSingleSelectInput from 'src/components/base/vs/BaseSingleSelectInput.vue';
import {
  ENTERPRISE_TYPE_ENUM,
  ENTERPRISE_TYPE_OPTIONS,
} from 'src/helpers/constants';
import { inject, watch } from 'vue';
// #endregion

// #region state
const searchFormProvideData = inject('searchFormProvideData');
// #endregion

// #region watch
watch(
  () => searchFormProvideData.form.value.type,
  () => {
    if (
      searchFormProvideData.form.value.type ===
      ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      searchFormProvideData.form.value.userCode = '';
      searchFormProvideData.errors.value.user_code = '';
    } else {
      searchFormProvideData.form.value.licenseNumber = '';
      searchFormProvideData.errors.value.license_number = '';
    }
  }
);
// #endregion
</script>
