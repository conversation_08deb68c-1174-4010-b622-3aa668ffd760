import BaseRepository from '../base.repository';

class AdminSystemRepository extends BaseRepository {
  constructor() {
    super('admin/setting');
  }

  getSystemSettings() {
    return this.client.get('/get');
  }

  updateSystemSettings(data) {
    return this.client.put('/update', data);
  }

  getSystemSettingsForNormalUser() {
    return this.client.get('/get-for-normal-user');
  }
}

export default new AdminSystemRepository();
