<template>
  <div class="tw:flex tw:justify-center">
    <div class="tw:w-[40%] tw:min-w-[550px]">
      <!-- province -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          都道府県<span class="tw:text-red">*</span>
        </span>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionsRegions"
          v-model="form.provinceId"
          class="tw:text-base"
          :error="!!errors.provinceId"
          :error-message="errors.provinceId"
          no-error-icon
        >
        <template v-slot:option="scope">
          <div>
            <div
              class="text-weight-bold text-black q-pl-sm q-pt-xs q-pb-xs"
              style="pointer-events: none;"
            >
              {{ scope.opt.label }}
            </div>

            <div v-for="child in scope.opt.children" :key="child.value">
              <q-item
                dense
                clickable
                v-ripple
                v-close-popup
                @click="form.provinceId = child.value"
                :class="{ 'bg-light-blue-1': form.provinceId === child.value }"
              >
                <q-item-section>
                  <q-item-label>{{ child.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </template>

        <template v-slot:selected-item>
          <q-item v-if="selectedProvince" class="tw:p-0">
            <q-item-section>
              <q-item-label>{{ selectedProvince.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-select>
      </div>
      <!-- staff type -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          事業者/従事者区分
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showRole(editUser) }}
        </div>
      </div>
      <!-- enterprise_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          届出番号
        </span>
        <q-input
          v-if="editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE"
          v-model="form.enterpriseCode"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="7"
          :error="!!errors.enterpriseCode"
          :error-message="errors.enterpriseCode"
          no-error-icon
        />
        <div v-else class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          {{ editUser.enterpriseCode }}
        </div>
      </div>
      <!-- user_code -->
      <div class="tw:flex tw:flex-col tw:mt-4 tw:mb-8">
        <span class="tw:text-base tw:pb-1">
          ユーザーID
        </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          {{ editUser.userCode }}
        </div>
      </div>
      <!-- enterprise_name -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          事業者名<span class="tw:text-red">*</span>
        </span>
        <q-input
          v-model="form.name"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="50"
          :error="!!errors.name"
          :error-message="errors.name"
          no-error-icon
        />
      </div>
      <!-- enterprise_name_kana -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          事業者名（カナ）
        </span>
        <q-input
          v-model="form.nameKana"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="50"
          :error="!!errors.nameKana"
          :error-message="errors.nameKana"
          no-error-icon
        />
      </div>
      <!-- license_number -->
      <div class="tw:flex tw:flex-col tw:my-4"
        v-if="editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE ||
        editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE"
      >
        <span class="tw:text-base tw:pb-1">
          許可番号<span class="tw:text-red">*</span>
        </span>
        <q-input
          v-model="form.licenseNumber"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.licenseNumber"
          :error-message="errors.licenseNumber"
          no-error-icon
        />
      </div>
      <!-- phone -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          連絡先等
        </span>
        <q-input
          v-model="form.phone"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.phone"
          :error-message="errors.phone"
          no-error-icon
        />
      </div>
      <!-- expiry_date -->
      <div class="tw:flex tw:flex-col tw:my-4"
        v-if="editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
        || editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE"
      >
        <span class="tw:text-base tw:pb-1">
          採捕許可有効期間
        </span>
        <div class="tw:flex tw:gap-2 tw:mb-1 tw:items-center">
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="form.startExpiryDate"
              :error="!!errors.startExpiryDate"
              :error-message="errors.startExpiryDate"
              input-class="tw:text-base"
            />
          </div>
          <span class="tw:text-[0.77rem] tw:mb-[18px]"> ～ </span>
          <div class="tw:flex-1">
            <BaseDatePicker
              v-model="form.endExpiryDate"
              :error="!!errors.endExpiryDate"
              :error-message="errors.endExpiryDate"
              input-class="tw:text-base"
            />
          </div>
        </div>
      </div>
      <!-- status user -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          状態<span class="tw:text-red">*</span>
        </span>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionsStatusUser"
          v-model="form.status"
          class="tw:text-base"
          :error="!!errors.status"
          :error-message="errors.status"
          no-error-icon
        />
      </div>
      <!-- status register -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          アカウント登録状況
        </span>
        <div v-if="editUser.account_registration_status === USER_STATUS_REGISTER_ENUM.TEMPORARY"
        class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          {{ showStatusRegister(editUser.account_registration_status) }}
        </div>
        <q-select
          v-else
          outlined
          map-options
          emit-value
          :options="OPTIONS_STATUS_REGISTER_ACCOUNT"
          v-model="form.account_registration_status"
          class="tw:text-base"
          :error="!!errors.account_registration_status"
          :error-message="errors.account_registration_status"
          no-error-icon
        />
      </div>
      <!-- temporary password -->
      <div v-if="form.account_registration_status === USER_STATUS_REGISTER_ENUM.TEMPORARY"
        class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          初期パスワード<span class="tw:text-red">*</span>
        </span>
        <q-input
          class="tw:mt-3"
          input-class="tw:text-base"
          outlined
          v-model="form.password"
          autocomplete="new-password"
          lazy-rules
          maxlength="256"
          :error="!!errors.password"
          :error-message="errors.password"
          no-error-icon
        >
        </q-input>
      </div>
      <!-- official password -->
      <div v-if="form.account_registration_status === USER_STATUS_REGISTER_ENUM.OFFICIAL" class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:font-[400] tw:text-base tw:pb-1">
          パスワード
        </span>
        <div class="tw:bg-qcard tw:p-4 tw:text-base tw:min-h-14">
          **********
        </div>
      </div>
      <!-- note 1 -->
      <div class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:text-base tw:pb-1">
          備考1
        </span>
        <q-input
          v-model="form.note1"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.note1"
          :error-message="errors.note1"
          no-error-icon
        />
      </div>
      <!-- note 2 -->
      <div class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:text-base tw:pb-1">
          備考2
        </span>
        <q-input
          v-model="form.note2"
          input-class="tw:text-base"
          autocomplete="nope"
          outlined
          maxlength="256"
          :error="!!errors.note2"
          :error-message="errors.note2"
          no-error-icon
        />
      </div>
      <!-- enable export function -->
      <div v-if="editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
      && editUser.staff_type === STAFF_TYPE_ENUM.ENTERPRISE" class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1">
          輸出機能<span class="tw:text-red">*</span>
        </span>
        <q-select
          outlined
          map-options
          emit-value
          :options="optionEnableExport"
          v-model="form.enable_export_function"
          class="tw:text-base"
          :error="!!errors.enable_export_function"
          :error-message="errors.enable_export_function"
          no-error-icon
        />
      </div>
      <!-- customization_item -->
      <div v-if="editUser.province_custom_data
      && editUser.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE">
        <div class="tw:flex tw:flex-col tw:my-4"
        v-for="item in Object.keys(form.province_custom_data)" :key="item">
          <span class="tw:text-base tw:pb-1">
            {{ item }}
          </span>
          <q-input
            v-model="form.province_custom_data[item]"
            input-class="tw:text-base"
            outlined
            type="textarea"
            :error="!!errors[item]"
            :error-message="errors[item]"
            no-error-icon
          />
        </div>
      </div>
      <!-- button -->
      <div class="tw:flex tw:justify-center tw:gap-4">
        <BaseButton
          outline
          padding="0.75rem"
          :class="`tw:bg-white tw:text-${colorMain} tw:w-40 tw:text-[1rem]`"
          label="戻る"
          @click.prevent="goBack"
        />
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-40 tw:text-[1rem]`"
          label="確認する"
          @click.prevent="confirmEditUser"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
// #region import
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import useValidate from 'composables/validate';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import {
  FORMAT_DATE,
  showRole,
  showStatusRegister,
} from 'src/helpers/common';
import {
  OPTIONS_STATUS_USER,
  ENTERPRISE_TYPE_ENUM,
  USER_STATUS_REGISTER_ENUM,
  OPTION_ENABLE_EXPORT_FUNCTION,
  STAFF_TYPE_ENUM,
  OPTIONS_STATUS_REGISTER_ACCOUNT,
} from 'src/helpers/constants';
import BaseDatePicker from 'components/base/BaseDatePicker.vue';
import editUserSchema from 'src/schemas/admin/user/editUser';
import MESSAGE from 'src/helpers/message';
import BaseButton from 'src/components/base/BaseButton.vue';
import regionsService from 'src/shared/services/regions.service';
// #endregion import

const router = useRouter();
const { colorMain, colorSub, dataAdminEditUser } = storeToRefs(useAppStore());
const { setAdminEditUser } = useAppStore();
const { errors, validateData } = useValidate();

const userId = ref('');
const editUser = ref({});
const form = ref({});
const optionsRegions = ref([]);
const optionsStatusUser = ref(OPTIONS_STATUS_USER);
const optionEnableExport = ref(OPTION_ENABLE_EXPORT_FUNCTION);

const selectedProvince = computed(() => {
  const provinceId = form.value.provinceId;
  for (const region of optionsRegions.value) {
    const found = region.children.find(p => p.value === provinceId);
    if (found) {return found;}
  }
  return null;
});

const goBack = () => {
  setAdminEditUser({});
  router.back();
};

const confirmEditUser = () => {
  const data = {
    ...form.value,
  };

  const newError = ref({});
  errors.value = {};
  if (!data.province_custom_data) {
    delete data.province_custom_data;
  }
  if (data.account_registration_status === USER_STATUS_REGISTER_ENUM.OFFICIAL) {
    delete data.password;
  }

  if (data.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
  data.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    if (data.startExpiryDate && !data.endExpiryDate) {
      newError.value.endExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.startExpiryDate && data.endExpiryDate) {
      newError.value.startExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
  }

  // DISTRIBUTE_ENTERPRISE
  if (data.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
    delete data.licenseNumber;
    delete data.startExpiryDate;
    delete data.endExpiryDate;
    if (data.staff_type === STAFF_TYPE_ENUM.STAFF) {
      delete data.enable_export_function;
    }
  } else {
    delete data.enable_export_function;
  }

  // CATCH_ENTERPRISE
  if (data.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    if (!data.startExpiryDate) {
      newError.value.startExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
    if (!data.endExpiryDate) {
      newError.value.endExpiryDate = MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR;
    }
  }

  // EEL_FARMING_ENTERPRISE
  if (data.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    if (!data.startExpiryDate) {
     delete data.startExpiryDate;
    }
    if (!data.endExpiryDate) {
      delete data.endExpiryDate;
    }
    if (!data.enterpriseCode) {
      delete data.enterpriseCode;
    }
  }
  const valid = validateData(editUserSchema, data);
  errors.value = { ...errors.value, ...newError.value };

  if (!valid || Object.keys(newError.value).length > 0) {
    return;
  }

  setAdminEditUser(form.value);
  router.push({
    name: 'adminUserEditComplete',
    params: {
      id: userId.value,
    },
  });
};

// onMounted
onMounted(async () => {
  userId.value = router.currentRoute.value.params?.id;
  const listRegions = await regionsService.getRegionWithProvinces();
  optionsRegions.value = listRegions.payload.items.map(item => ({
    label: item.region_name,
    children: item.province.map(province => ({
      label: province.name,
      value: province.id,
    })),
  }));
  const result = await userAdminService.getUserDetail(userId.value);
    if (result) {
      editUser.value = {
        provinceId: result.province_id || '',
        enterprise_type: result.enterprise_type,
        staff_type: result.staff_type,
        endExpiryDate: FORMAT_DATE(result.license?.expiry_date),
        licenseNumber: result.license_number,
        startExpiryDate: FORMAT_DATE(result.license?.expiry_date_start),
        userCode: result.user_code,
        enterpriseCode: result.enterprise?.enterprise_code,
        name: result.name,
        nameKana: result.name_kana,
        status: result.status,
        phone: result.phone,
        role: result.role,
        province_custom_data: result.province_custom_data,
        account_registration_status: result.account_registration_status,
        password: result?.password || '',
        enable_export_function: result.enable_export_function || false,
        note1: result.note_1,
        note2: result.note_2,
      };
    }
  if (Object.keys(dataAdminEditUser.value || {}).length > 0) {
    form.value = { ...dataAdminEditUser.value };
  } else {
    form.value = { ...editUser.value };

  }
});

</script>
<style lang="css">
textarea { resize: none !important; }

</style>
