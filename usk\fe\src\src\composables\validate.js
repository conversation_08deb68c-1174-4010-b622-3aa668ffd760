import { ref } from 'vue';

import ajv from '../boot/ajv';

export default function useValidate() {
  const errors = ref({});
  const validateData = (schema, data) => {
    errors.value = {};
    const validate = ajv.compile(schema);
    const valid = validate(data);
    if (validate.errors) {
      const lst = {};
      validate.errors.forEach(obj => {
        const dataPath = obj.instancePath
          .slice(1, obj.instancePath.length);
        if (!lst[dataPath]) {
          lst[dataPath] = obj.message;
        }
      });
      errors.value = lst;
    }

    return valid;
  };

  return {
    errors,
    // methods
    validateData,
  };
}
