import BaseService from 'services/base.service';

class ShippingService extends BaseService {
  async getShippingList(query) {
    try {
      return this.dao.getShippingList(query);
    } catch (error) {
      return null;
    }
  }

  async getShippingDetail(id) {
    try {
      return this.dao.getShippingDetail(id);
    } catch (error) {
      return null;
    }
  }

  async cancelShipping(id) {
    try {
      return this.dao.cancelShipping(id);
    } catch (error) {
      return null;
    }
  }

  async editShipping(id, body) {
    try {
      return this.dao.editShipping(id, body);
    } catch (error) {
      return null;
    }
  }

  async exportShippingList(query) {
    try {
      return this.dao.exportShippingList(query);
    } catch (error) {
      return null;
    }
  }

  async exportShippingDetail(query) {
    try {
      return this.dao.exportShippingDetail(query);
    } catch (error) {
      return null;
    }
  }

  async getExportToday() {
    try {
      return this.dao.getExportToday();
    } catch (error) {
      return null;
    }
  }

  async resetExportToday() {
    try {
      return this.dao.resetExportToday();
    } catch (error) {
      return null;
    }
  }
}

export default new ShippingService('shipping');
