<template>
  <div class="tw:w-[40%] tw:min-w-[550px] tw:m-auto">
    <!-- title -->
    <div
      class="tw:my-5 tw:font-bold tw:text-center tw:text-2xl tw:border-b tw:border-gray tw:pb-4"
    >
      SMS送信完了
    </div>
    <!-- content -->
    <div
      class="tw:my-5 tw:leading-[2rem] tw:text-sm"
    >
      <div>SMSの送信が完了しました。</div>
      <div class="tw:mt-3">
        SMSに記載されているURLからパスワードの再登録をお願いいたします。
      </div>
      <div class="tw:mt-3">
        SMSが届かない場合は、異なる携帯電話番号が登録されている可能性があります
      </div>
    </div>
    <q-btn
      class="tw:w-full tw:mt-5 tw:font-bold tw:leading-[2rem] tw:text-base"
      label="ログイン画面へ"
      type="button"
      outline
      :color="`${colorSub}`"
      @click.prevent="backToLogin"
    />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';

const { colorSub } = storeToRefs(useAppStore());
const router = useRouter();

// ===== REF =====

// ===== METHOD =====
const backToLogin = async () => {
  await router.push({ name: 'adminLogin' });
};
</script>
