-- DROP FUNCTION public.f_generate_user_code();

CREATE OR REPLACE FUNCTION public.f_generate_user_code()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
maxdigit int;
ecode varchar;
begin
	-- Check that user_code is given
	if NEW.user_code is not null then
    	return new;
	end if;

	if new.enterprise_type = 9 then
		select coalesce(max(left(u.user_code,7))::int + 1,0) into maxdigit from users u 
		where u.enterprise_type = 9;
		NEW.user_code = maxdigit ||'-0000';
		return new;
	end if;
	
 	-- Check if the enterprise has staff
	select coalesce(max(right(u.user_code,4))::int + 1,0) into maxdigit from users u 
		where u.enterprise_id = new.enterprise_id;
	-- get enterprise_code from tbl enterprises
	SELECT enterprise_code into ecode FROM enterprises e WHERE e.id = NEW.enterprise_id;

  --set user_code for new staff
	NEW.user_code = ecode ||'-'|| lpad(maxdigit::varchar, 4,'0');
	return new;
end;
$function$
;

-- Table Triggers

create trigger trig_genuserscode before
insert
    on
    public.users for each row execute function f_generate_user_code();