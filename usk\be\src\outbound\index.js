// ======== IMPORT ========================
const {
  registerOutboundShipmentSchema,
  registerProxyOutboundShipmentSchema,
  getInventoryListOptionsSchema,
  checkPermissionForProxyOutboundShipmentSchema,
} = require('./schema');
const { auth, apiKeyVerify, authEnterprise } = require('../base/authorized');
// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
// Register outbound shipment
async function registerOutboundShipment(request, reply) {
  const outboundService = request.diScope.resolve('outboundService');
  const response = await outboundService.registerOutboundShipment(
    request.user,
    request.body
  );
  return reply.send(response);
}

// Check permission for proxy outbound shipment
async function checkPermissionForProxyOutboundShipment(request, reply) {
  const outboundService = request.diScope.resolve('outboundService');
  const response = await outboundService.checkPermissionForProxyOutboundShipment(
    request.user,
    request.params.proxyUserId
  );
  return reply.send(response);
}

// Register proxy outbound shipment
async function registerProxyOutboundShipment(request, reply) {
  const outboundService = request.diScope.resolve('outboundService');
  const response = await outboundService.registerProxyOutboundShipment(
    request.user,
    request.body
  );
  return reply.send(response);
}

async function getInventoryListOptions(request, reply) {
  const outboundService = request.diScope.resolve('outboundService');
  const response = await outboundService.getInventoryListOptions(
    request.user,
    request.query
  );
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.post(
    '/register',
    { schema: registerOutboundShipmentSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    registerOutboundShipment
  );
  fastify.post(
    '/proxy-register',
    { schema: registerProxyOutboundShipmentSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    registerProxyOutboundShipment
  );
  fastify.get(
    '/proxy-check-permission/:proxyUserId',
    { schema: checkPermissionForProxyOutboundShipmentSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    checkPermissionForProxyOutboundShipment
  );
  fastify.get(
    '/inventory-list-options',
    { schema: getInventoryListOptionsSchema, onRequest: [apiKeyVerify, auth, authEnterprise] },
    getInventoryListOptions
  );
};
