import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

dayjs.getDate = (value = dayjs(), timeZone = 'Asia/Tokyo') => (dayjs(value).tz(timeZone));
dayjs.getDateFromJST = (value = dayjs(), timeZone = 'Asia/Tokyo') => (dayjs.tz(value, timeZone));

export default dayjs;
