import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc.js';
import timezone from 'dayjs/plugin/timezone.js';
import duration from 'dayjs/plugin/duration.js';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);

dayjs.getDate = (value = dayjs(), timeZone = 'Asia/Tokyo') => (dayjs(value).tz(timeZone));
dayjs.getDateFromJST = (value = dayjs(), timeZone = 'Asia/Tokyo') => (dayjs.tz(value, timeZone));

export default dayjs;
