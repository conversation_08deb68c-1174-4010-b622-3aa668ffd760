import MESSAGE from 'helpers/message';

const searchAllShippingList = {
  additionalProperties: false,
  type: 'object',
  properties: {
    pageIndex: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_INDEX_INVALID,
      },
    },
    pageSize: {
      type: 'integer',
      errorMessage: {
        _: MESSAGE.MSG_PAGE_SIZE_INVALID,
      },
    },
    licenseNumber: {
      type: 'string',
      maxLength: 256,
      errorMessage: {
        _: MESSAGE.LICENSE_NUMBER_ERROR,
      },
    },
    enterpriseCode: {
        type: 'string',
        pattern: '^\\d+$',
      errorMessage: {
        _: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    supplier: {
      type: 'string',
    },
    enterpriseName: {
      type: 'string',
    },
    startArrivalDate: {
      type: 'string',
      format: 'slash-date',
      formatMaximum: {
        $data: '1/endArrivalDate',
      },
      errorMessage: {
        format: MESSAGE.START_ARRIVAL_DATE_ERROR,
        formatMaximum: MESSAGE.MSG_START_DATE_CONSTRAINT_ERROR,
      },
    },
    endArrivalDate: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.END_ARRIVAL_DATE_ERROR,
      },
    },
    sortBy: {
      type: 'string',
      enum: ['id', 'arrival_net_weight', 'destination_enterprise_name'],
      errorMessage: {
        _: MESSAGE.SORT_BY_ERROR,
      },
    },
    note1: {
      type: 'string',
      maxLength: 256,
    },
    note2: {
      type: 'string',
      maxLength: 256,
    },
  },
};

export default searchAllShippingList;
