#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
  worker_connections  1024;
}


http {
  include       mime.types;
  default_type  application/octet-stream;


  sendfile        on;
  keepalive_timeout  65;

  upstream share_common_api {
      server **************:3000;
      keepalive 500;
  }

  upstream usk_api {
      server **************:3001;
      keepalive 500;
  }

  map $http_origin $allow_origin {
      ~^https?://(.*\.)?**************(:\d+)?$ $http_origin;
      ~^https?://(.*\.)?localhost(:\d+)?$ $http_origin;
      default "";
  }

  server {
      listen 33333;
      server_name **************;
      root /usr/share/nginx/html;
      index index.html index.htm;
      location / {
          try_files $uri $uri/ /index.html;
      }

      proxy_buffer_size 128k;
      proxy_buffers 4 256k;
      proxy_busy_buffers_size 256k;

      location ~^/api/v1/common {
          proxy_hide_header 'Access-Control-Allow-Origin';
          add_header 'Access-Control-Allow-Origin' "$allow_origin" always;
          add_header 'Access-Control-Allow-Credentials' 'true' always;
          add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
          add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With,X-Api-Key' always;
          if ($request_method = 'OPTIONS') {
              return 204;
          }
          proxy_set_header   Host $http_host;
          proxy_set_header   X-Forwarded-Proto $scheme;
          proxy_set_header   X-NginX-Proxy true;
          proxy_set_header   X-Real-IP       $remote_addr;
          proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_pass         http://**************:3000;
          proxy_redirect     off;
          proxy_http_version 1.1;
          proxy_set_header   Upgrade $http_upgrade;
          proxy_set_header Connection $http_connection;
          proxy_cache_bypass $http_upgrade;

          break;
      }

      location ~^/api/v1/756E616769 {
          proxy_hide_header 'Access-Control-Allow-Origin';
          add_header 'Access-Control-Allow-Origin' "$allow_origin" always;
          add_header 'Access-Control-Allow-Credentials' 'true' always;
          add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
          add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With,X-Api-Key' always;
          if ($request_method = 'OPTIONS') {
              return 204;
          }
          proxy_set_header   Host $http_host;
          proxy_set_header   X-Forwarded-Proto $scheme;
          proxy_set_header   X-NginX-Proxy true;
          proxy_set_header   X-Real-IP       $remote_addr;
          proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_pass         http://**************:3001;
          proxy_redirect     off;
          proxy_http_version 1.1;
          proxy_set_header   Upgrade $http_upgrade;
          proxy_set_header Connection $http_connection;
          proxy_cache_bypass $http_upgrade;
          break;
      }
  }
}