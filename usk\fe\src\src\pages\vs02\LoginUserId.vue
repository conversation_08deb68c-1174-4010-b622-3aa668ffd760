<!-- eslint-disable max-len -->
<template>
  <div>
    <q-card class="tw:px-6 tw:text-[#333333] tw:tl:mt-0 tw:mt-[1rem] tw:pb-[6.25rem]">
      <!-- title -->
      <div class="tw:tl:mt-4 tw:text-m-design tw:font-normal">
        ユーザーIDとパスワードを入力して、ログインボタンを押してください。
      </div>
      <!-- content -->
      <div class="tw:mt-3">
        <span class="tw:text-m-design tw:font-normal tw:relative">
          ユーザーID
          <q-badge
          class="tw:text-xxs-design tw:font-normal tw:px-[0.625rem]
          badgeColor tw:ml-2 tw:absolute tw:dt:top-[0.3rem] tw:top-[0.7rem]
          tw:min-h-[1.6875rem] tw:max-h-[1.6875rem] tw:h-[1.6875rem]"
          >
          必須
          </q-badge>
        </span>
        <q-input
          class="tw:mt-3 tw:text-m-design tw:font-normal"
          placeholder="ユーザーIDを入力してください。"
          outlined
          autocomplete="nope"
          lazy-rules
          maxlength="12"
          no-error-icon
          v-model.trim="userId"
          :error="!!errors.userid"
          :error-message="errors.userid"
        />
        <div class="tw:mt-4">
          <span class="tw:text-m-design tw:font-normal tw:relative">
          パスワード
          <q-badge
          class="tw:text-xxs-design tw:font-normal tw:px-[0.625rem]
          badgeColor tw:ml-2 tw:absolute tw:dt:top-[0.3rem] tw:top-[0.7rem]
          tw:min-h-[1.6875rem] tw:max-h-[1.6875rem] tw:h-[1.6875rem]"
          >
          必須
          </q-badge>
        </span>
          <q-input
            :type="showPassword ? 'text' : 'password'"
            :error="!!errors.password"
            no-error-icon
            placeholder="パスワードを入力してください。"
            outlined
            autocomplete="nope"
            :error-message="errors.password"
            maxlength="256"
            lazy-rules
            class="tw:mt-3 tw:text-m-design tw:font-normal"
            v-model="password"
          >
            <template v-slot:append>
              <q-icon class="tw:text-[#004AB9]" size="2.25rem" :name="showPassword ? 'visibility' : 'visibility_off'"
                @click.prevent="showPassword = !showPassword" />
            </template>
          </q-input>
        </div>
        <div class="text-center">
          <BaseButton
            outline

            class="tw:rounded-[40px]"
            :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
            tw:tl:w-[16.5rem] tw:tl:h-[5.5rem]
            tw:w-full`"
            label="ログイン"
            @click.prevent="login"
          />
          <div
          class="tw:text-m-design tw:font-normal tw:text-[#004AB9] tw:mt-8
          tw:cursor-pointer tw:underline tw:mb-8"
          @click.prevent="goContact"
          >パスワードを忘れた方はこちら</div>
        </div>
      </div>

      <div v-if="
        userLink?.province_custom_data &&
        userLink?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
        && userLink?.provinces?.setting.is_show_info === 1
      " class="tw:border-t-1 tw:border-[#D2D2D2] tw:py-4 tw:mb-[15rem]">
        <div class="tw:text-l-design tw:font-bold tw:mb-4">許可情報</div>
        <div class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-4">
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">届出番号</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ userLink?.user_code.substring(0,
              userLink?.user_code.length - 5) }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">事業者名</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ userLink?.name }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">事業者名（カナ）</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ userLink?.name_kana }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">許可番号</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ userLink?.license_number }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">事業者/従事者区分</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ showRole(userLink) }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">採捕許可有効期限</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ FORMAT_DATE(userLink?.license?.expiry_date) }}
            </div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">備考1</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ userLink?.note_1 }}</div>
          </div>
          <div class="tw:my-1">
            <div class="tw:text-m-design tw:font-normal">備考2</div>
            <div class="tw:text-l-design tw:font-normal tw:tl:font-bold">{{ userLink?.note_2 }}</div>
          </div>
          <div class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-4 tw:tl:col-span-2">
            <div class="tw:my-1" v-for="item in Object.keys(userLink.province_custom_data)" :key="item">
              <div class="tw:text-m-design tw:font-normal">{{ item }}</div>
              <div class="tw:text-l-design tw:font-normal tw:tl:font-bold tw:whitespace-pre-line">{{
                replaceSemicolon(userLink.province_custom_data[item]) }}</div>
            </div>
          </div>
        </div>
      </div>
    </q-card>

    <!-- Footer Button -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
            tw:mt-4 tw:flex tw:flex-col tw:gap-8 tw:tl:flex-row tw:tl:items-center tw:tl:justify-start"
    >
      <div class="tw:text-[#333333] tw:text-m-design tw:font-normal tw:text-left">
        他の方法でログインする
      </div>
      <div class="tw:flex tw:justify-center tw:tl:justify-start tw:w-full tw:tl:w-auto tw:gap-4 tw:tl:gap-8">
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
                    tw:tl:w-[19.5rem] tw:h-[4.75rem] tw:min-h-[4.75rem] tw:max-h-[4.75rem]
                    tw:w-[95.5%]`"
          label="QRスキャナー"
          @click.prevent="goQrScan"
        />
      </div>
    </q-footer>

  </div>
</template>

<script setup>
import useValidate from 'composables/validate';
import { storeToRefs } from 'pinia';
import loginUserSchema from 'schemas/loginUser';
import authService from 'services/auth.service';
import { USER_STATUS_ENUM, ROLES_ENUM, USER_STATUS_REGISTER_ENUM, ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import { useAuthStore } from 'stores/auth-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  FORMAT_DATE,
  showRole,
} from 'src/helpers/common';
import BaseButton from 'components/base/vs/BaseButton.vue';

const { userLink } = storeToRefs(useAuthStore());

const router = useRouter();
const { errors, validateData } = useValidate();

// ===== REF =====
const userId = ref('');
const password = ref('');
const showPassword = ref(false);

// ===== METHOD =====

const login = async () => {
  const formData = {
    userid: userId.value,
    password: password.value,
  };
  const valid = validateData(loginUserSchema, formData);
  if (!valid) {
    return;
  }
  const response = await authService.login(formData);

  if (response) {

    if (response.account_registration_status === USER_STATUS_REGISTER_ENUM.TEMPORARY) {
      await router.push({ name: 'recoveryPassword' });
      return;
    }

    if (response.role === ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN && (response.status === USER_STATUS_ENUM.ACTIVE
    || response.status === USER_STATUS_ENUM.NONACTIVE)) {
      await router.push({ name: 'fisheriesDepartmentAdmin' });
      return;
    }

    if (response.role === ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN && (response.status === USER_STATUS_ENUM.ACTIVE
    || response.status === USER_STATUS_ENUM.NONACTIVE)) {
      await router.push({ name: 'localGovernmentAdmin' });
      return;
    }

    if ((response.status === USER_STATUS_ENUM.ACTIVE || response.status === USER_STATUS_ENUM.NONACTIVE) &&
    response.role !== ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN && response.role !== ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN) {
      await router.push({ name: 'home' });
      return;
    }
  }
};

const replaceSemicolon = value => {
  if (value) {
    return value.replaceAll(';', '\n');
  }
  return value;
};

const goQrScan = () => {
  router.push({ name: 'loginQrScan' });
};

const goContact = () => {
  router.push({ name: 'contactScreen' });
};

onMounted(async () => {
  if (userLink.value?.user_code) {
    userId.value = userLink.value?.user_code;
  }
});
</script>

<style>
.badgeColor {
  background-color: #E80F00 !important;
  color: #ffffff !important;
}
</style>
