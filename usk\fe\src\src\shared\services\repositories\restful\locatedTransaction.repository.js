import BaseRepository from './base.repository';

// TODO: change name class to PartnerRepository
// TODO: change file name to partner.repository.js
class LocatedTransactionRepository extends BaseRepository {
  constructor() {
    super('located-transaction');
  }

  searchAll(params) {
    return this.client.get('/search-all', params);
  }

  searchPartnerHybridList(params) {
    return this.client.get('/hybrid-list', params);
  }

  getPartnerHybridDetail(id) {
    return this.client.get(`/hybrid-detail/${id}`);
  }

  registerPartnerHybrid(params) {
    return this.client.post('/register-partner-hybrid', params);
  }

  toggleHidden(params, enterpriseCode) {
    return this.client.post(`/toggle-show/${enterpriseCode}`, params);
  }

  registerHandMade(params) {
    return this.client.post('/register-handmade', params);
  }

  getLocatedTransactionDetail(id) {
    return this.client.get(`/located-transaction-detail/${id}`);
  }

  updateLocatedTransaction(id, params) {
    return this.client.put(`/located-transaction-update/${id}`, params);
  }

  deleteLocatedTransaction(id) {
    return this.client.delete(`/located-transaction-delete/${id}`);
  }
}

export default new LocatedTransactionRepository();
