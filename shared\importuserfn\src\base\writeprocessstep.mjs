import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, UpdateCommand, BatchWriteCommand } from '@aws-sdk/lib-dynamodb';

// Bare-bones DynamoDB Client
const client = new DynamoDBClient({ region: process.env.REGION });

// Bare-bones document client
const ddbDocClient = DynamoDBDocumentClient.from(client); // client is DynamoDB client

export const updateProcessImport = async (args) => {
  const {
    fileKey,
    status,
    contents,
    datetimeprocess,
    insertedcount,
    updatedcount,
    changedcount,
  } = args;
  const command = new UpdateCommand({
    TableName: 'usk-import_users_process',
    Key: {
      fileKey,
    },
    UpdateExpression: `set #status = :status,
      #contents = :contents,
      #datetimeprocess = :datetimeprocess,
      #insertedcount = :insertedcount,
      #updatedcount = :updatedcount,
      #changedcount = :changedcount`,
    ExpressionAttributeNames: {
      '#status': 'status',
      '#contents': 'contents',
      '#datetimeprocess': 'datetimeprocess',
      '#insertedcount': 'inserted_count',
      '#updatedcount': 'updated_count',
      '#changedcount': 'changed_count',
    },
    ExpressionAttributeValues: {
      ':status': status,
      ':contents': contents,
      ':datetimeprocess': datetimeprocess,
      ':insertedcount': insertedcount,
      ':updatedcount': updatedcount,
      ':changedcount': changedcount,
    },
    ReturnValues: 'ALL_NEW',
  });
  await ddbDocClient.send(command);
}

export const insertImportUsersData = async (fileKey, dataUsers, header) => {
  const dataUserSplice = dataUsers.splice(0, 25);
  const putRequests = dataUserSplice.map((dataUser) => ({
    PutRequest: {
      Item: {
        fileKey,
        ...dataUser,
        header
      },
    },
  }));
  const command = new BatchWriteCommand({
    RequestItems: {
      // An existing table is required. A composite key of 'title' and 'year' is recommended
      // to account for duplicate titles.
      'usk-import_users_data': putRequests,
    },
  });
  await ddbDocClient.send(command);
  if (dataUsers.length) {
    await new Promise((resolve) => setTimeout(() => resolve(), 200));
    await insertImportUsersData(fileKey, dataUsers);
  }
}