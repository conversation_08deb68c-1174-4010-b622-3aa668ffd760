<template>
  <div class="tw:flex tw:justify-center tw:flex-col tw:items-center">
    <div class="tw:w-[40%] tw:min-w-[550px]">
      <div class="tw:text-base tw:mt-4 tw:text-left">
        以下の内容で登録します。
      </div>
      <!-- province -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 都道府県 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          {{ showProvinceName(dataAdminRegisterUser.provinceId) }}
        </div>
      </div>
      <!-- enterprise_type -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 事業者種別 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          {{ showEnterpriseType(dataAdminRegisterUser.enterpriseType) }}
        </div>
      </div>
      <!-- enterprise_code -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 届出番号 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          {{ dataAdminRegisterUser.enterpriseCode }}
        </div>
      </div>
      <!-- typeStaffOrEnterprise -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 事業者/従事者区分 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          {{
            showTypeStaffOrEnterprise(
              dataAdminRegisterUser.typeStaffOrEnterprise
            )
          }}
        </div>
      </div>
      <!-- enterprise_name -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 事業者名 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:break-all tw:min-h-14 tw:break-words">
          {{ dataAdminRegisterUser.enterpriseName }}
        </div>
      </div>
      <!-- enterprise_name_kana -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 事業者名（カナ） </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:break-all tw:min-h-14 tw:break-words">
          {{ dataAdminRegisterUser.enterpriseNameKana }}
        </div>
      </div>
      <!-- license_number -->
      <div
        class="tw:flex tw:flex-col tw:my-4"
        v-if="dataAdminRegisterUser.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
        dataAdminRegisterUser.enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE"
      >
        <span class="tw:text-base tw:pb-1"> 許可番号 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:break-all tw:min-h-14 tw:break-words">
          {{ dataAdminRegisterUser.licenseNumber }}
        </div>
      </div>
      <!-- phone -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 連絡先等 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14 tw:break-words">
          {{ dataAdminRegisterUser.phone }}
        </div>
      </div>
      <!-- expiryDate -->
      <div
        class="tw:flex tw:flex-col tw:my-4"
        v-if="dataAdminRegisterUser.enterpriseType === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE ||
        (dataAdminRegisterUser.enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
        && dataAdminRegisterUser.startExpiryDate && dataAdminRegisterUser.endExpiryDate)"
      >
        <span class="tw:text-base tw:pb-1"> 採捕許可有効期間 </span>
        <div  class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14">
          <span>
            {{ `${dataAdminRegisterUser.startExpiryDate}~${dataAdminRegisterUser.endExpiryDate}` }}</span>
        </div>
      </div>
      <!-- status user -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base  tw:pb-1">
          状態
        </span>
        <div
        class="tw:text-base  tw:bg-qcard tw:p-4
        tw:min-h-14">
          {{ showStatus(dataAdminRegisterUser.status) }}
        </div>
      </div>
      <!-- status register -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base  tw:pb-1">
          アカウント登録状況
        </span>
        <div
        class="tw:text-base  tw:bg-qcard tw:p-4
        tw:min-h-14">
          仮登録
        </div>
      </div>
      <!-- password -->
      <div class="tw:flex tw:flex-col tw:my-4">
        <span class="tw:text-base tw:pb-1"> 初期パスワード </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:min-h-14 tw:break-words">
          {{ dataAdminRegisterUser.password }}
        </div>
      </div>
      <!-- note 1 -->
      <div
        class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:text-base tw:pb-1"> 備考1 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:break-all tw:min-h-14">
          <span>
            {{ dataAdminRegisterUser.note1 }}
          </span>
        </div>
      </div>
      <!-- note 2 -->
      <div
        class="tw:flex tw:flex-col tw:my-4"
      >
        <span class="tw:text-base tw:pb-1"> 備考2 </span>
        <div class="tw:text-base tw:bg-qcard tw:p-4 tw:break-all tw:min-h-14">
          <span>
            {{ dataAdminRegisterUser.note2 }}
          </span>
        </div>
      </div>
      <!-- button -->
      <div class="tw:flex tw:gap-3 tw:justify-center">
        <BaseButton
          outline
          padding="0.75rem"
          :class="`tw:bg-white tw:text-${colorMain} tw:w-40 tw:text-[1rem]`"
          label="いいえ"
          @click.prevent="goBack"
        />
        <BaseButton
          padding="0.75rem"
          :class="`tw:bg-${colorSub} tw:text-[white] tw:w-40 tw:text-[1rem]`"
          label="はい"
          @click.prevent="registerHandler"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
// #region import
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { showEnterpriseType, showProvince, showStatus, showTypeStaffOrEnterprise } from 'src/helpers/common';
import userAdminService from 'src/shared/services/admin/user.admin.service';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';
import BaseButton from 'src/components/base/BaseButton.vue';
import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import regionsService from 'src/shared/services/regions.service';
// #endregion import

const router = useRouter();
const listProvince = ref([]);
const { colorMain, colorSub, dataAdminRegisterUser } = storeToRefs(
  useAppStore()
);
const { setAdminRegisterUser } = useAppStore();
const goBack = () => {
  router.back();
};

const showProvinceName = provinceId => {
  if (!listProvince.value || !Array.isArray(listProvince.value)) {return '';}
  const findProvince = listProvince.value.find(p => p.value === +provinceId);
  return findProvince ? findProvince.label : '';
};

const registerHandler = async () => {
  const payload = {
    ...dataAdminRegisterUser.value,
  };

  const result = await userAdminService.registerUser(payload);

  if (result.code === 0) {
    setAdminRegisterUser({
      enterpriseType: '',
      enterpriseCode: '',
      enterpriseName: '',
      enterpriseNameKana: '',
      licenseNumber: '',
      phone: '',
      startExpiryDate: '',
      endExpiryDate: '',
      status: 0,
      note1: '',
      note2: '',
      password: '',
      typeStaffOrEnterprise: '',
      provinceId: '',
    });

    toast.access(MESSAGE.MSG_REGISTER_EMPLOYEE_INFO_BY_ADMIN);

    router.push({
      name: 'adminUserManager',
    });
  }
};

// onMounted
onMounted(async () => {
  const listRegions = await regionsService.getRegionWithProvinces();
  listProvince.value = listRegions.payload.items.flatMap(item =>
  item.province.map(province => ({
    label: province.name,
    value: province.id,
  })));
});
</script>
