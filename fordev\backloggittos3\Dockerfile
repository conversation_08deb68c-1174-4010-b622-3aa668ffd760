FROM public.ecr.aws/lambda/python:3.13.2024.11.22.15

RUN dnf upgrade -y && \
  dnf install -y git

COPY ./.ssh /root/.ssh

RUN chmod 600 /root/.ssh/config
RUN chmod 600 /root/.ssh/known_hosts
RUN chmod 400 /root/.ssh/saibacklog_rsa

# Copy function code
COPY ./ ${LAMBDA_TASK_ROOT}

# Install the specified packages
RUN pip install -r requirements.txt

# Set the CMD to your handler (could also be done as a parameter override outside of the Dockerfile)
CMD [ "lambda_function.handler" ]