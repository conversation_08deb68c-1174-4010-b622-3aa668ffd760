<template>
  <div></div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import authService from 'services/auth.service';
import { useAppStore } from 'stores/app-store';
import { useAuthStore } from 'stores/auth-store';
import toast from 'utilities/toast';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const { setLoading, setLoadingManual } = useAppStore();
const { user } = storeToRefs(useAuthStore());
// ===== REF =====
const qrCode = ref(route.params.qrCode);

// ===== METHOD =====

const checkLoginLink = async () => {
  setLoadingManual(true);
  setLoading(true);
  if (user.value) {
    await router.push({
      name: 'home',
    });
  }
  const loginResult = await authService.loginLink({ qrCode: qrCode.value });
  if (
    loginResult.code === 401
    && router.currentRoute.value.query?.preRoute === 'loginQrScan'
  ) {
    toast.error(loginResult.message);
    setLoading(false);
    setLoadingManual(false);
    await router.push({
      name: 'loginQrScan',
    });
    return;
  }
  await router.push({
    name: 'loginUserId',
  });
  if (loginResult.code !== 0) {
    toast.error(loginResult.message);
  }
  setLoading(false);
  setLoadingManual(false);
};

onMounted(async () => {
  await checkLoginLink();
});
</script>
